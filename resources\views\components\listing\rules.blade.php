@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "rules");
@endphp
<div class="col-md-12">
    <div class="inner_section_main_col">
        <div class="main_step_title">
            <h2>{{ $step_data->title ?? "" }}</h2>
            @isset($step_data->sub_title)
                <p>{{ $step_data->sub_title }}</p>
            @endisset
        </div>
        <div class="allow_pets_field_wrapper">
            <div class="allow_pets_title">
                <h4>{{ translate('stepper.do_you_allow_pets') }}</h4>
            </div>
            <div class="allow_pets_input_wrapper">
                @foreach (['yes', 'no'] as $ans)
                    <div class="allow_pets_input">
                        <label for="{{ $ans }}">{{ ucwords($ans) }}</label>
                        <input type="radio" class="radio_btn" id="{{ $ans }}" name="pet"
                            value="{{ $ans }}" @if (($listing->detail->pet ?? null) === $ans || (($listing->detail->pet ?? null) === null && $ans === 'no')) checked @endif>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="accommodation_custom_rules_wrapper scrollable-section">
            <div class="row">
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-6">
                    <div class="inner_section_col_left">
                        <div class="custom_add_tags_field_wrapper">
                            <div class="label_addBtn_wrapper">
                                <label for="">{{ translate('stepper.whats_allowed') }}</label>
                                <a class="add_tag_btn cust_add" href="javascript:void(0)">{{ translate('stepper.add') }}</a>
                            </div>
                            <div class="txt_field">
                                <input class="form-control field_input no_validate" type="text"
                                    placeholder="{{ $allowedPlaceholder }}">
                            </div>
                            <div class="custom_tags_wrapper">
                                @forelse ($listing->allow_rules ?? [] as $allow_rule)
                                    <div class="single_custom_tag"><label
                                            for="allow_{{ $allow_rule->id }}">{{ $allow_rule->title }}</label>
                                        <input value="{{ $allow_rule->title }}" type="hidden" name="allow_rules[]"
                                            id="allow_{{ $allow_rule->id }}"> <a href="javascript:void(0)"
                                            class="remove-item"><i class="far fa-times-circle"></i></a>
                                    </div>
                                @empty
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-6">
                    <div class="inner_section_col_left">
                        <div class="custom_add_tags_field_wrapper">
                            <div class="label_addBtn_wrapper">
                                <label for="">{{ translate('stepper.whats_not_allowed') }}</label>
                                <a class="add_tag_btn_notAllowed cust_add" href="javascript:void(0)">{{ translate('stepper.add') }}</a>
                            </div>
                            <div class="txt_field">
                                <input class="form-control field_input no_validate" type="text"
                                    placeholder="{{ $notAllowedPlaceholder }}">
                            </div>
                            <div class="custom_tags_wrapper">
                                @forelse ($listing->not_allow_rules ?? [] as $not_allow_rule)
                                    <div class="single_custom_tag"><label
                                            for="allow_not_{{ $not_allow_rule->id }}">{{ $not_allow_rule->title }}</label>
                                        <input value="{{ $not_allow_rule->title }}" type="hidden"
                                            name="not_allow_rules[]" id="allow_not_{{ $not_allow_rule->id }}"> <a
                                            href="javascript:void(0)" class="remove-item"><i
                                                class="far fa-times-circle"></i></a>
                                    </div>
                                @empty
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('js')
    <script>
        function validaterulesField() {
            let noteInputs = $('.rules_expectations_step .custom_add_tags_field_wrapper .field_input');
            let allEmpty = false;

            noteInputs.each(function() {
                if ($(this).val().trim().length > 0) {
                    allEmpty = true;
                    return true;
                }
            });

            $('.rules_expectations_step .next').prop("disabled", allEmpty);
        }

        $(document).on('input blur', '.rules_expectations_step .custom_add_tags_field_wrapper .field_input', function() {
            validaterulesField();
        });


        function rulesValidation() {
            var imageNumber = $(
                '.rules_expectations_step .custom_add_tags_field_wrapper .custom_tags_wrapper .single_custom_tag').length;
            if (imageNumber >= 1) {
                $('.listing_stepper .rules_expectations_step .next.action-button').prop('disabled',
                    false);
            } else {
                $('.listing_stepper .rules_expectations_step .next.action-button').prop('disabled',
                    true);
            }
        }

        $(document).on('change','.rules_expectations_step .allow_pets_input_wrapper input[type="radio"]',function(){
            rulesValidation();
        });

        $(document).on('focusout','.rules_expectations_step .accommodation_custom_rules_wrapper .field_input',function(){
            rulesValidation();
        });


        $(document).on('click', '.rules_expectations_step .custom_add_tags_field_wrapper .add_tag_btn', function() {
            var field_value = $(this).closest('.custom_add_tags_field_wrapper').find('.field_input').val();
            var tags_count = $(this).closest('.custom_add_tags_field_wrapper').find('.single_custom_tag').length +
                1;
            if (field_value != "") {
                $(this).closest('.custom_add_tags_field_wrapper').find('.custom_tags_wrapper').append(
                    '<div class="single_custom_tag"><label for="">' + field_value + '</label> <input value="' +
                    field_value + '" type="hidden" name="allow_rules[]" id="allow_' + tags_count +
                    '"> <a href="javascript:void(0)" class="remove-item"><i class="far fa-times-circle"></i></a></div>'
                );
                $(this).closest('.custom_add_tags_field_wrapper').find('.field_input').val('');
                rulesValidation();
                validaterulesField();
            }
        });

        $(document).on('click', '.rules_expectations_step .custom_add_tags_field_wrapper .add_tag_btn_notAllowed', function() {
            var field_value = $(this).closest('.custom_add_tags_field_wrapper').find('.field_input').val();
            var tags_count = $(this).closest('.custom_add_tags_field_wrapper').find('.single_custom_tag').length +
                1;
            if (field_value != "") {
                $(this).closest('.custom_add_tags_field_wrapper').find('.custom_tags_wrapper').append(
                    '<div class="single_custom_tag"><label for="">' + field_value + '</label> <input value="' +
                    field_value + '" type="hidden" name="not_allow_rules[]" id="not_allow_' + tags_count +
                    '"> <a href="javascript:void(0)" class="remove-item"><i class="far fa-times-circle"></i></a></div>'
                );
                $(this).closest('.custom_add_tags_field_wrapper').find('.field_input').val('');
                rulesValidation();
                validaterulesField();
            }
        });


        $(document).on('click', '.rules_expectations_step .custom_add_tags_field_wrapper .remove-item', function() {
            $(this).closest('.single_custom_tag').remove();
            rulesValidation();
            // validaterulesField();
        });


        $(document).ready(function() {
            rulesValidation();
        });

    </script>
@endpush
