@extends('layouts.master')

@section('content')


    <section class="head_btn dash_head_sec">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_btns align-item-center">
                        <div class="title">
                            <h1 class="box-title pull-left">{{ translate('content_management_system.create_new') }}
                                {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption for ') }}
                                {{ $selected_amenity->name ?? '' }}
                            </h1>
                        </div>
                        <div class="header_btns">
                            @can('view-' . str_slug('AmenityOption'))
                                <a class="btn btn_yellow pull-right" href="{{ route('amenity-option.index', ["amenity_id" => $selected_amenity->ids]) }}">
                                    {{-- <i class="icon-arrow-left-circle"></i>  --}}
                                    {{ translate('content_management_system.back') }}</a>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <section class="create_amenity_option_sec">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white-box">
                        {{-- <h3 class="box-title pull-left">{{ __('create_new') }} {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}</h3>
                    @can('view-' . str_slug('AmenityOption'))
                    <a  class="btn btn_yellow pull-right" href="{{url('/amenityOption/amenity-option')}}"> --}}
                        {{-- <i class="icon-arrow-left-circle"></i>  --}}
                        {{-- {{ __('back') }}</a>
                    @endcan --}}

                        <div class="clearfix"></div>
                        {{-- <hr> --}}
                        @if ($errors->any())
                            <ul class="alert alert-danger">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif

                        <form method="POST"
                            action="{{ route('amenity-option.store', ['amenity_id' => $selected_amenity->ids]) }}"
                            accept-charset="UTF-8" class="form-horizontal" enctype="multipart/form-data">
                            {{ csrf_field() }}

                            @include ('amenityOption.amenity-option.form')
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

@endsection


@push('js')
    <script>
        $(document).ready(function() {
            var urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('amenity-id')) {
                var amenityID = urlParams.get('amenity-id');
                console.log(amenityID);

                $('#amenity_id option').filter(function() {
                    return $(this).val() === amenityID;
                }).prop('selected', true);

                $('#amenity_id').attr('readonly', true);
                $('#amenity_id').css({
                    'pointer-events': 'none',
                    'cursor': 'not-allowed'
                });
            }
        });
    </script>
@endpush
