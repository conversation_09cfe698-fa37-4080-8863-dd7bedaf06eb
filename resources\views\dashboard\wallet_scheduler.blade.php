<div class="modal fade login signup" id="autoSchedule" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content p-4">
            {{-- <div class="modal-header border-0 pb-0">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h1 class="modal-title pb-0" id="exampleModalLabel1">{{ trans('Auto Payout') }}</h1>
            </div> --}}
            <div class="modal-body">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <form method="POST" id="signUp-form">
                    @csrf
                    <fieldset>
                        <div class="heading_switch_wrapper d-flex justify-content-center align-items-center gap-3">
                            <h1 class="modal-title pb-0 pt-0" id="exampleModalLabel1">{{ translate('dashboard_ewallet.auto_payout') }}</h1>
                            {{-- <div class="form-group form_field_padding m-0">
                                <div class="toggle-container">
                                    <label class="switch">
                                        <input type="checkbox" class="toggle-input" value="1" name="schedule_active" checked>
                                        <span class="slider"></span>
                                    </label>
                                    <span class="toggle-label deactive">Inactive</span> <!-- This will be updated -->
                                    <span class="toggle-label active">Active</span> <!-- This will be updated -->
                                </div>
                            </div>
                        </div> --}}
                            <div class="form-group form_field_padding m-0">
                                <div class="toggle-container">
                                    <label class="switch">
                                        <input type="checkbox" class="toggle-input" id="schedule_active_toggle"
                                            {{ auth()->user()->schedule_active ? 'checked' : '' }}>
                                        <span class="slider"></span>
                                    </label>
                                    <span class="toggle-label deactive">{{ translate('dashboard_ewallet.inactive') }}</span>
                                    <span class="toggle-label active">{{ translate('dashboard_ewallet.active') }}</span>
                                </div>
                                <input type="hidden" name="schedule_active" id="schedule_active_hidden"
                                    value="{{ auth()->user()->schedule_active ? '1' : '0' }}">
                            </div>
                        </div>

                            <div class="form-group form_field_padding m-0">
                                <label for="threshold_amount">{{translate('dashboard_ewallet.autopayout_heading')}}.</label>
                                <div class="input-group">
                                    <input type="text" class="form-control cop" id="schedule_amount"
                                        name="schedule_amount" placeholder="{{ translate('dashboard_ewallet.enter_amount') }} (e.g., 500,000)"
                                        value="{{ auth()->user()->schedule_amount ?? '' }}" required />
                                    <div class="input-group-addon">COP</div>
                                </div>

                            </div>
                            {{-- <div class="form-group form_field_padding m-0">
                            <input type="number" min="0" class="form-control" name="wallet_threshold" id="wallet_threshold"
                                placeholder="limit wallet Balance" value="{{ auth()->user()->wallet_threshold ?? '' }}" required />
                        </div> --}}
                            <input type="hidden" name="user_email" id="user_email" class="form-control"
                                value="{{ auth()->user()->email ?? '' }}" />
                                <div id="sign-up-error" style="display: none" class="alert alert-danger text-left"
                            role="alert">
                            <ul>
                            </ul>
                        </div>

                            <button type="button" class="btn_yellow mb-4" id="signup-btn">{{ translate('dashboard_ewallet.submit') }}</button>
                    </fieldset>

                    <fieldset style="display: none;">
                        <h4 class="modal-title text-center fs-18 text-black">{{ translate('dashboard_ewallet.verify_heading') }}</h4>

                        <p class="fs-14 text-center">{{ translate('dashboard_ewallet.verify_message') }} <span
                                class="fw-bold user-email-span"></span></p>
                        <div class="form-group form_field_padding">
                            <input type="text" class="form-control" id="otp_inpt"
                                placeholder="Enter Verification Code here">
                        </div>

                        <input type="button" name="next" id="verify_otp_btn"
                            class="btn_yellow mb-4 w-100 d-inline-block mb-3 disable-on-click" value="Verify" />
                        <button type="button" class="trans_btn text-primary mx-auto w-100 b-none"
                            id="resend-email-otp-btn">{{ translate('dashboard_ewallet.resend') }} {{ translate('dashboard_ewallet.code') }}</button>
                    </fieldset>

                </form>
            </div>

        </div>
    </div>
</div>
