<?php

namespace App\Http\Controllers\HelpCenter;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use App\HelpCenter;
use App\Models\Faq;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HelpCenterController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        $model = str_slug('helpcenter', '-');
        if (auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first() != null) {
            $keyword = $request->get('search');
            $perPage = 25;

            if (!empty($keyword)) {
                $helpcenter = HelpCenter::where('title', 'LIKE', "%$keyword%")
                    ->paginate($perPage);
            } else {
                $helpcenter = HelpCenter::paginate($perPage);
            }

            return view('helpCenter.help-center.index', compact('helpcenter'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('helpcenter', '-');
        if (auth()->user()->permissions()->where('name', '=', 'add-' . $model)->first() != null) {
            return view('helpCenter.help-center.create');
        }
        return response(view('403'), 403);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('helpcenter', '-');
        if (auth()->user()->permissions()->where('name', '=', 'add-' . $model)->first() != null) {
            $request->validate([
                'title' => 'required|string|max:255|unique:help_centers,title',
                'description' => 'nullable|string',
                'items.*.name' => 'required|string|max:255',
                'items.*.description' => 'required|string',
            ], [
                'items.*.name.required' => 'The name field is required for each faq.',
                'items.*.description.required' => 'The description field is required for each faq.',
            ]);

            $helpcenter = new HelpCenter;
            $helpcenter->title = $request->title;
            $helpcenter->description = $request->description;
            $helpcenter->slug = Str::slug($request->title);
            $helpcenter->save();

            if (isset($request->items)) {
                Faq::where("help_center_id", $helpcenter->id)->delete();
                foreach ($request->items as $faq) {
                    if (isset($faq["name"]) && isset($faq['description'])) {
                        $slug = Str::slug($faq["name"]);
                        $originalSlug = $slug;
                        $counter = 1;
                        while (Faq::where('slug', $slug)->exists()) {
                            $slug = $originalSlug . '-' . $counter;
                            $counter++;
                        }
                        $faqdata = new Faq();
                        $faqdata->title = $faq["name"];
                        $faqdata->help_center_id = $helpcenter->id;
                        $faqdata->description = htmlspecialchars($faq['description']);
                        $faqdata->slug = $slug;
                        $faqdata->save();
                    }
                }
            }
            return redirect('helpCenter/help-center')->with('flash_message', translate('dashboard_help_center.helpcenter_added'));
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('helpcenter', '-');
        if (auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first() != null) {
            $helpcenter = HelpCenter::findOrFail($id);
            return view('helpCenter.help-center.show', compact('helpcenter'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('helpcenter', '-');
        if (auth()->user()->permissions()->where('name', '=', 'edit-' . $model)->first() != null) {
            $helpcenter = HelpCenter::findOrFail($id);
            $faqs = $helpcenter->faqs()->paginate(10);
            return view('helpCenter.help-center.edit', compact('helpcenter', 'faqs'));
        }
        return response(view('403'), 403);
    }



    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('helpcenter', '-');
        if (auth()->user()->permissions()->where('name', '=', 'edit-' . $model)->first() != null) {
            $request->validate([
                'title' => 'required|string|max:255|unique:help_centers,title,' . $id,
                'description' => 'nullable|string',
                'items.*.name' => 'required|string|max:255',
                'items.*.description' => 'required|string',
            ], [
                'items.*.name.required' => 'The name field is required for each faq.',
                'items.*.description.required' => 'The description field is required for each faq.',
            ]);

            $helpcenter = HelpCenter::findOrFail($id);
            $helpcenter->title = $request->title;
            $helpcenter->description = $request->description;
            $helpcenter->slug = Str::slug($request->title);
            $helpcenter->updated_at = now();
            $helpcenter->save();

            if (isset($request->items)) {
                Faq::where("help_center_id", $helpcenter->id)->delete();
                foreach ($request->items as $faq) {
                    if (isset($faq["name"]) && isset($faq['description'])) {
                        $slug = Str::slug($faq["name"]);
                        $originalSlug = $slug;
                        $counter = 1;
                        while (Faq::where('slug', $slug)->exists()) {
                            $slug = $originalSlug . '-' . $counter;
                            $counter++;
                        }
                        $faqdata = new Faq();
                        $faqdata->title = $faq["name"];
                        $faqdata->help_center_id = $helpcenter->id;
                        $faqdata->description = htmlspecialchars($faq['description']);
                        $faqdata->slug = $slug;
                        $faqdata->save();
                    }
                }
            }
            return redirect('helpCenter/help-center')->with('flash_message', translate('dashboard_help_center.helpcenter_updated'));
        }
        return response(view('403'), 403);
    }

    // public function helpCenterImage(Request $request)
    // {
    //     try {
    //         $request->validate([
    //             'files.*' => 'required|image|mimes:jpeg,png,jpg,gif|max:10240',
    //         ]);
    
    //         $image = $request->file('files')[0] ?? null;
    //         if (!$image) {
    //             return response()->json([
    //                 'success' => false,
    //                 'error' => 'No image file provided.',
    //             ], 400);
    //         }
    
    //         $filename = time() . '_' . $image->getClientOriginalName();
    
    //         $path = Storage::disk('website')->putFileAs('joditImages', $image, $filename);
    
    //         $imageUrl = Storage::disk('website')->url($path);
    
    //         return response()->json([
    //             'success' => true,
    //             'url' => $imageUrl,
    //             'fileName' => $filename,
    //             'uploaded' => 1,
    //             'baseurl' => url('/'),
    //         ]);
    //     } catch (\Exception $e) {
    //         return response()->json([
    //             'success' => false,
    //             'error' => $e->getMessage(),
    //         ], 500);
    //     }
    // }

    public function viewHelpCenter(Request $request)
    {
        try {
            $id = $request->id;
            $helpcenter = HelpCenter::findOrFail($id);
            return view('helpCenter.help-center.view_modal', compact('helpcenter'));
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function helpCenterImage(Request $request)
    {
        try {
            $isImageUpload = $request->hasFile('files');
            $isFileUpload = $request->hasFile('file');

            if (!$isImageUpload && !$isFileUpload) {
                return response()->json([
                    'success' => false,
                    'error' => 'No file provided.',
                ], 400);
            }

            // Validation based on upload type
            if ($isImageUpload) {
                $request->validate([
                    'files.*' => 'required|image|mimes:jpeg,png,jpg,gif|max:10240',
                ]);
                $file = $request->file('files')[0];
                $storagePath = 'joditImages';
            } else {
                $request->validate([
                    'file' => 'required|file|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx,zip,txt|max:20480',
                ]);
                $file = $request->file('file');
                $storagePath = 'joditFiles';
            }

            $filename = time() . '_' . $file->getClientOriginalName();
            $path = Storage::disk('website')->putFileAs($storagePath, $file, $filename);
            $fileUrl = Storage::disk('website')->url($path);

            return response()->json([
                'success' => true,
                'url' => $fileUrl,
                'filename' => $file->getClientOriginalName(),
                'uploaded' => 1,
                'baseurl' => url('/'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function deleteHelpCenterImage(Request $request)
    {
        try {
            $request->validate([
                'image_url' => 'required|url',
            ]);

            $imageUrl = $request->input('image_url');
            $relativePath = parse_url($imageUrl, PHP_URL_PATH); // e.g., /website/joditImages/123456_image.jpg
            $relativePath = ltrim(str_replace('/website/', '', $relativePath), '/'); // e.g., joditImages/123456_image.jpg

            if (Storage::disk('website')->exists($relativePath)) {
                Storage::disk('website')->delete($relativePath);
                return response()->json([
                    'success' => true,
                    'message' => translate('dashboard_help_center.image_deleted_successfully'),                  
                ]);
            }

            return response()->json([
                'success' => false,
                'error' => 'Image not found.',
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('helpcenter', '-');
        if (auth()->user()->permissions()->where('name', '=', 'delete-' . $model)->first() != null) {
            // HelpCenter::destroy($id);
            HelpCenter::withTrashed()->find($id)?->forceDelete();
            return redirect('helpCenter/help-center')->with('flash_message', translate('dashboard_help_center.helpcenter_deleted'));
        }
        return response(view('403'), 403);
    }
}
