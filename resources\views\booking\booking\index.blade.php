@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
    <link href="{{ asset('plugins/components/bootstrap-daterangepicker/daterangepicker.css') }}" rel="stylesheet">
    <style>
        /* Smooth transitions for CSV buttons to prevent flickering */
        .btn_parent {
            position: relative;
            display: flex;
            gap: 0.25rem;
            align-items: start;
        }

        .btn_parent .csv_btn,
        .btn_parent .csv_btn_all {
            transition: all 0.2s ease-in-out;
            opacity: 1;
        }

        .btn_parent .csv_btn.fade-out {
            opacity: 0;
            transform: scale(0.95);
        }

        .btn_parent .csv_btn.fade-in {
            opacity: 1;
            transform: scale(1);
        }

        /* Prevent layout shift during button transitions */
        .btn_parent .csv_btn {
            white-space: nowrap;
            min-width: fit-content;
        }
    </style>
@endpush

@section('content')
    <div class="container-fluid booking_sec">
        <!-- .row -->
        <div class="row">
            <div class="col-md-12 d-flex justify-content-between align-items-center">
                <div class="d-flex">
                    <h2 class="box-title pull-left">
                        @if (!auth()->user()->hasRole('service'))
                            {{ translate('dashboard_bookings.all_bookings') }}
                        @else
                            {{ translate('dashboard_bookings.bookings') }}
                        @endif
                    </h2>
                    @can('add-' . str_slug('Booking'))
                        <a class="btn btn-success pull-right" href="{{ url('/booking/booking/create') }}"><i
                                class="icon-plus"></i>
                            {{ translate('dashboard_bookings.add') }}
                            {{ translate('dashboard_bookings.booking') }}</a>
                    @endcan
                    <div class="clearfix"></div>
                </div>
                <div class="d-flex gap-1">
                    <div class="btn_parent">
                        <a href="{{ route('export_bookings_csv', ['search' => request()->search, 'date' => request()->date]) }}"
                            class="btn btn_yellow filter_btn csv_btn_all">
                            <i class="fa fa-download"></i> {{ translate('dashboard_bookings.download_all_csv') }}
                        </a>
                        @if ($status)
                            <a href="{{ route('export_bookings_csv', ['status' => $status, 'search' => request()->search, 'date' => request()->date]) }}"
                                class="btn btn_yellow filter_btn csv_btn">
                                <i class="fa fa-download"></i> {{ translate('dashboard_bookings.download_status_csv', ['status' => ucfirst($status)]) }}
                            </a>
                         
                        @endif
                    </div>


                    <div class="nav_search main booking_search">
                        <!-- Actual search box -->

                        <div class="form-group filter_search has-feedback has-search">
                            <form class="example" action="{{ route('booking.index') }}" method="GET"
                                style="width: 100%;">
                                @if (request()->has('status'))
                                    <input type="hidden" name="status" value="{{ request()->status }}">
                                @endif
                                <div class="d-flex w-100 gap-1">
                                    <button type="submit" class="me-0"><i class="fa fa-search"></i></button>
                                    <input type="text" placeholder="{{ translate('dashboard_bookings.search_placeholder') }}"
                                        id="searchBar" name="search" value="{{ request()->search }}" class="w-100">
                                    <input type="text" class="input-daterange-datepicker w-65"
                                        value="{{ request()->date }}" name="date" placeholder="{{ translate('dashboard_bookings.select_date_range') }}">
                                </div>
                                <a href="{{ route('booking.index', request()->has('status') ? ['status' => request()->status] : []) }}"
                                    class="btn btn-reset mb-0"><i class="fas fa-redo yellow_icon"></i></a>
                                <button type="submit" class="btn btn_trans border-0 m-0"><i class="fas fa-search yellow_icon"></i></button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-12">
                <div class="white-box">
                    <div class="table-responsive">
                        <div class="listing_filters_wrapper filter_btn_wrapper">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <button type="button" data-status="all"
                                        class="btn filter_btn status-filter {{ !in_array($status, ['upcoming', 'completed', 'cancelled', 'ongoing', 'today']) ? 'active' : '' }}">{{ translate('dashboard_bookings.all') }}
                                    </button>
                                    <button type="button" data-status="today"
                                        class="btn filter_btn status-filter {{ $status == 'today' ? 'active' : '' }}">{{ translate('dashboard_bookings.today') }}
                                    </button>
                                    <button type="button" data-status="upcoming"
                                        class="btn filter_btn status-filter {{ $status == 'upcoming' ? 'active' : '' }} ">{{ translate('dashboard_bookings.upcoming') }}</button>
                                    <button type="button" data-status="ongoing"
                                        class="btn filter_btn status-filter {{ $status == 'ongoing' ? 'active' : '' }} ">{{ translate('dashboard_bookings.ongoing') }}</button>
                                    <button type="button" data-status="completed"
                                        class="btn filter_btn status-filter {{ $status == 'completed' ? 'active' : '' }} ">{{ translate('dashboard_bookings.completed') }}</button>
                                    <button type="button" data-status="cancelled"
                                        class="btn filter_btn status-filter {{ $status == 'cancelled' ? 'active' : '' }} ">{{ translate('dashboard_bookings.cancelled') }}</button>
                                </div>
                            </div>
                        </div>
                        <div id="booking-table-container">
                            @include('booking.booking.partials.booking_table', ['booking' => $booking, 'status' => $status,])
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modal --}}

    <section class="booking reject">
        <div class="modal fade" id="booking" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
            <div class="modal-dialog" id="myModal" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="title">{{ translate('dashboard_bookings.booking_detail') }}</h1>
                        <div class="form_field_padding">
                            <table class="table-responsive booking_table">
                                <tbody>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.customer_name') }}</td>
                                        <td> John Doe</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.listing') }}</td>
                                        <td> Car Testing 1</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.listing_basis_type') }}</td>
                                        <td> Hourly</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.date') }}</td>
                                        <td> 10/10/23</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.check_in') }}</td>
                                        <td> 2024-01-17</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.check_out') }}</td>
                                        <td> 2024-01-31</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.guest') }}</td>
                                        <td> 1</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.total_days') }}</td>
                                        <td> 0</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.discount') }}</td>
                                        <td>0</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.subtotal_amount') }}</td>
                                        <td>2250.0</td>
                                    </tr>
                                    <tr>
                                        <td>{{ translate('dashboard_bookings.total_amount') }}</td>
                                        <td class="bold">2250.0</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Modal Cancelation-->
    <div class="modal fade cancelation booking_cancellation amen" id="cancelation" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <form action="{{ route('cancel_booking_admin') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title mx-auto" id="cancelationHeading">{{ translate('dashboard_bookings.booking_cancellation') }}</h1>
                        <input type="hidden" name="booking_id" id="booking_id" value="{{ old('booking_id') }}">
                        <input type="hidden" name="payment_method" id="payment_method"
                            value="{{ old('payment_method') }}">
                        <div class="mb-3 position-relative refundValue_wrapper">
                            <label for="refundValue" class="pb-2">{{ translate('dashboard_bookings.how_much_refunded') }}</label>
                            {{-- <input type="number" name="refundValue" id="refundValue" min="0" max="100"
                                pattern="^[1-9]\d*$" onKeyPress="if(this.value.length==3) return false;"
                                class="form-control refund_value_field" placeholder="e.g., 80%"> --}}
                            <input type="text" name="refundValue" id="refundValue" min="0" max="100"
                                pattern="^[1-9][0-9]{0,1}$|^100$" class="form-control refund_value_field"
                                placeholder="{{ translate('dashboard_bookings.eg_80_percent') }}" oninput="restrictNumberInput(this)">
                            <i class="fas fa-percent percent_icon" style="display: none;"></i>
                            @error('refundValue')
                                <p class="text-danger">
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                        <div class="mb-3">
                            {{-- <input type="checkbox" name="deductProvider" id="deductProvider" class="form-control-checkbox"> --}}
                            <label for="" class="pt-3">{{ translate('dashboard_bookings.deduct_provider') }}</label>
                            <div class="deductProvider d-flex justify-content-between">
                                <input type="radio" name="deductProvider" id="deductProviderYes" value="yes"
                                    class="form-control-checkbox  d-none">
                                <label for="deductProviderYes">{{ translate('dashboard_bookings.yes') }}</label>

                                <input type="radio" name="deductProvider" id="deductProviderNo" value="no"
                                    class="form-control-checkbox d-none" checked>
                                <label for="deductProviderNo">{{ translate('dashboard_bookings.no') }}</label>
                            </div>
                            @error('deductProvider')
                                <p class="text-danger">
                                    {{ $message }}
                                </p>
                            @enderror
                        </div>
                        <div class="modal_btn">
                            <button type="submit" class="btn yellow">{{ translate('dashboard_bookings.submit') }}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('plugins/components/bootstrap-daterangepicker/daterangepicker.js') }}"></script>

    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    <script>
        $(function() {
            // Destroy any existing DataTable first
            if ($.fn.DataTable.isDataTable('#myTable')) {
                $('#myTable').DataTable().destroy();
            }
            
            initializeDataTable();

            // Initialize download links on page load
            let initialStatus = '{{ $status ?? 'all' }}';
            let initialSearch = '{{ request()->search ?? '' }}';
            let initialDate = '{{ request()->date ?? '' }}';
            updateDownloadLinks(initialStatus, initialSearch, initialDate);
        });

        function initializeDataTable() {
            // First, check if DataTable is already initialized and destroy it
            if ($.fn.DataTable.isDataTable('#myTable')) {
                $('#myTable').DataTable().destroy();
            }
            
            // Only initialize DataTable if the table exists and has data rows
            if ($('#myTable').length > 0 && $('#myTable tbody tr').length > 0 && !$('#myTable tbody tr:first').hasClass('no_booking')) {
                try {
                    $('#myTable').DataTable({
                        'aoColumnDefs': [{
                            'bSortable': false,
                            'aTargets': [-1] /* Last column (actions) not sortable */
                        }],
                        "order": [[ 4, "desc" ]], // Sort by the 5th column (From date) in descending order
                        paging: false,
                        info: false
                    });
                } catch (error) {
                    console.warn('DataTable initialization failed:', error);
                }
            }
        }

        function restrictNumberInput(input) {
            // Get the current value
            let value = input.value;

            // Remove non-numeric characters
            value = value.replace(/[^0-9]/g, '');

            // Remove leading zeros
            value = value.replace(/^0+/, '');

            // Limit to 3 characters
            if (value.length > 3) {
                value = value.slice(0, 3);
            }

            // Ensure value is between 0 and 100
            if (value > 100) {
                value = 100;
            }

            // Update the input value
            input.value = value === '' ? '' : Number(value);
        }

        $('.input-daterange-datepicker').daterangepicker({
            buttonClasses: ['btn', 'custom_cal_btn'],
            opens: 'left',
            applyClass: 'btn_yellow',
            cancelClass: 'cancel_btn',
            autoUpdateInput: false,
            locale: {
                cancelLabel: 'Clear',
                format: 'MM/DD/YYYY'
            }
        });

        $('.input-daterange-datepicker').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
        });

        $('.input-daterange-datepicker').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });

        $(document).on("click", ".cancel-booking", function() {
            let bookingId = $(this).data('booking-id');
            let paymentMethod = $(this).data('payment-method');
            $("#booking_id").val(bookingId);
            $("#payment_method").val(paymentMethod);
            $("#cancelation").modal('show');
        });

        @if ($errors->any())
            $("#cancelation").modal('show');
        @endif
        $(".refund_value_field").keyup(function() {
            $(this).siblings(".percent_icon").show();
            // console.log($(this).val());
            if ($(this).val() == '') {
                $(this).siblings(".percent_icon").hide();
            }
        });

        // AJAX filtering functionality
        let currentStatus = '{{ $status ?? '' }}';

        // Handle status filter clicks
        $(document).on('click', '.status-filter', function(e) {
            e.preventDefault();

            let status = $(this).data('status') || ''; // Ensure empty string for "All" button
            let search = $('#searchBar').val();
            let date = $('.input-daterange-datepicker').val();

            // Update active button
            $('.status-filter').removeClass('active');
            $(this).addClass('active');

            // Update current status
            currentStatus = status;

            // Load filtered data
            loadBookingData(status, search, date);
        });

        // Handle form submission with AJAX
        $('.example').on('submit', function(e) {
            e.preventDefault();

            let search = $('#searchBar').val();
            let date = $('.input-daterange-datepicker').val();

            loadBookingData(currentStatus, search, date);
        });

        // Handle pagination clicks
        $(document).on('click', '.pagination a', function(e) {
            e.preventDefault();
            let url = $(this).attr('href');
            let page = url.split('page=')[1];

            let search = $('#searchBar').val();
            let date = $('.input-daterange-datepicker').val();

            loadBookingData(currentStatus, search, date, page);
        });

        // Function to load booking data via AJAX
        function loadBookingData(status = '', search = '', date = '', page = 1) {
            // Show loading state
            $('#booking-table-container').html(
                '<div class="text-center p-4"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');

            // Destroy existing DataTable if it exists
            if ($.fn.DataTable.isDataTable('#myTable')) {
                $('#myTable').DataTable().destroy();
            }

            $.ajax({
                url: '{{ route('booking.index') }}',
                method: 'GET',
                data: {
                    status: status,
                    search: search,
                    date: date,
                    page: page
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    if (response.success) {
                        $('#booking-table-container').html(response.html);

                        // Update download CSV links
                        updateDownloadLinks(status, search, date);

                        // Small delay to ensure DOM is fully updated
                        setTimeout(function() {
                            initializeDataTable();
                        }, 100);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading booking data:', error);
                    $('#booking-table-container').html(
                        '<div class="text-center p-4 text-danger">Error loading data. Please try again.</div>'
                    );
                }
            });
        }

        // Function to update download CSV links
        function updateDownloadLinks(status, search, date) {
            console.log('Updating download links - Status:', status, 'Search:', search, 'Date:', date);

            let baseUrl = '{{ route('export_bookings_csv') }}';
            let params = new URLSearchParams();

            if (search) params.append('search', search);
            if (date) params.append('date', date);

            // Update "Download All CSV" link
            let allCsvUrl = baseUrl + (params.toString() ? '?' + params.toString() : '');
            $('.btn_parent .csv_btn_all').attr('href', allCsvUrl);
            $('.btn_parent .csv_btn_all').html('<i class="fa fa-download"></i> {{ translate("dashboard_bookings.download_all_csv") }}');
            console.log('Updated "Download All CSV" link to:', allCsvUrl);

            // Handle status-specific CSV link with smooth transitions
            let existingStatusBtn = $('.btn_parent .csv_btn');

            if (status && status !== '') {
                params.append('status', status);
                let statusCsvUrl = baseUrl + '?' + params.toString();
                let statusText = status.charAt(0).toUpperCase() + status.slice(1);
                
                // Get the appropriate translation based on status
                let buttonText = '';
                switch(status) {
                    case 'today':
                        buttonText = '<i class="fa fa-download"></i> {{ translate("dashboard_bookings.download_today_csv") }}';
                        break;
                    case 'upcoming':
                        buttonText = '<i class="fa fa-download"></i> {{ translate("dashboard_bookings.download_upcoming_csv") }}';
                        break;
                    case 'ongoing':
                        buttonText = '<i class="fa fa-download"></i> {{ translate("dashboard_bookings.download_ongoing_csv") }}';
                        break;
                    case 'completed':
                        buttonText = '<i class="fa fa-download"></i> {{ translate("dashboard_bookings.download_completed_csv") }}';
                        break;
                    case 'cancelled':
                        buttonText = '<i class="fa fa-download"></i> {{ translate("dashboard_bookings.download_cancelled_csv") }}';
                        break;
                        case 'all':
                        buttonText = '<i class="fa fa-download"></i> {{ translate("dashboard_bookings.download_all_csv") }}';
                        break;
                    default:
                        // For any other status, we'll use a server-side rendered translation with the status
                        buttonText = '<i class="fa fa-download"></i> {{ translate("dashboard_bookings.download_status_csv", ["status" => "__STATUS__"]) }}'.replace('__STATUS__', 'statusText');
                }

                if (existingStatusBtn.length > 0) {
                    // Update existing button smoothly with CSS transitions
                    existingStatusBtn.addClass('fade-out');
                    setTimeout(function() {
                        existingStatusBtn.attr('href', statusCsvUrl)
                                       .html(buttonText)
                                       .removeClass('fade-out')
                                       .addClass('fade-in');
                        setTimeout(function() {
                            existingStatusBtn.removeClass('fade-in');
                        }, 200);
                    }, 100);
                    console.log('Updated existing status button to:', statusText);
                } else {
                    // Create new status-specific download button with smooth fade-in
                    let statusBtn = $('<a href="' + statusCsvUrl + '" class="btn btn_yellow filter_btn csv_btn fade-out">' + 
                        buttonText + '</a>');
                    $('.btn_parent').append(statusBtn);

                    // Trigger fade-in after a brief delay
                    setTimeout(function() {
                        statusBtn.removeClass('fade-out').addClass('fade-in');
                        setTimeout(function() {
                            statusBtn.removeClass('fade-in');
                        }, 200);
                    }, 50);
                    console.log('Created new status button for:', statusText);
                }
            } else {
                // Remove status-specific download button with smooth fade-out
                if (existingStatusBtn.length > 0) {
                    existingStatusBtn.addClass('fade-out');
                    setTimeout(function() {
                        existingStatusBtn.remove();
                    }, 200);
                    console.log('Removed status-specific CSV button (showing All)');
                }
            }
        }
    </script>
@endpush
