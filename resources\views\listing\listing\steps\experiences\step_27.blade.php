@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "languages");
@endphp
<fieldset class="place_amenities_step watercraft_accessibility_step select_categories_step experience_language_step" id="">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    @isset($step_data->sub_title)
                        <div class="content">
                            <p>{{ $step_data->sub_title ?? "" }}</p>
                        </div>
                    @endisset
                    <div class="search_field_wrapper_amenities">
                        <select class="form-control select2 multi_select" multiple="multiple" name="languages[]"
                            id="language_select">
                            <option value="">--Select--</option>
                            @foreach ($category->tour_languages ?? [] as $tour_language)
                                <option value="{{ $tour_language->id }}" data-flag="{{ $tour_language->image }}" @if ($listing?->tour_languages?->contains('id', $tour_language->id) ?? "") selected @endif> {{ $tour_language->country_name }} </option>
                            @endforeach
                            {{--
                            <option value="ab" data-flag="https://flagcdn.com/w40/ge.png">Abkhazian (аҧсуа)</option>
                            <option value="aa" data-flag="https://flagcdn.com/w40/dj.png">Afar (Afaraf)</option>
                            <option value="af" data-flag="https://flagcdn.com/w40/za.png">Afrikaans</option>
                            <option value="ak" data-flag="https://flagcdn.com/w40/gh.png">Akan</option>
                            <option value="sq" data-flag="https://flagcdn.com/w40/al.png">Albanian (Shqip)</option>
                            <option value="am" data-flag="https://flagcdn.com/w40/et.png">Amharic (አማርኛ)</option>
                            <option value="ar" data-flag="https://flagcdn.com/w40/sa.png">Arabic (العربية)</option>
                            <option value="hy" data-flag="https://flagcdn.com/w40/am.png">Armenian (Հայերեն)</option>
                            <option value="as" data-flag="https://flagcdn.com/w40/in.png">Assamese (অসমীয়া)</option>
                            <option value="az" data-flag="https://flagcdn.com/w40/az.png">Azerbaijani (Azərbaycan dili)</option>
                            <option value="eu" data-flag="https://flagcdn.com/w40/es.png">Basque (Euskara)</option>
                            <option value="be" data-flag="https://flagcdn.com/w40/by.png">Belarusian (Беларуская)</option>
                            <option value="bn" data-flag="https://flagcdn.com/w40/bd.png">Bengali (বাংলা)</option>
                            <option value="bs" data-flag="https://flagcdn.com/w40/ba.png">Bosnian (Bosanski)</option>
                            <option value="bg" data-flag="https://flagcdn.com/w40/bg.png">Bulgarian (Български)</option>
                            <option value="my" data-flag="https://flagcdn.com/w40/mm.png">Burmese (မြန်မာဘာသာ)</option>
                            <option value="ca" data-flag="https://flagcdn.com/w40/es.png">Catalan (Català)</option>
                            <option value="zh" data-flag="https://flagcdn.com/w40/cn.png">Chinese (中文)</option>
                            <option value="hr" data-flag="https://flagcdn.com/w40/hr.png">Croatian (Hrvatski)</option>
                            <option value="cs" data-flag="https://flagcdn.com/w40/cz.png">Czech (Čeština)</option>
                            <option value="da" data-flag="https://flagcdn.com/w40/dk.png">Danish (Dansk)</option>
                            <option value="nl" data-flag="https://flagcdn.com/w40/nl.png">Dutch (Nederlands)</option>
                            <option value="en" data-flag="https://flagcdn.com/w40/gb.png">English</option>
                            <option value="eo" data-flag="https://flagcdn.com/w40/eu.png">Esperanto</option>
                            <option value="fi" data-flag="https://flagcdn.com/w40/fi.png">Finnish (Suomi)</option>
                            <option value="fr" data-flag="https://flagcdn.com/w40/fr.png">French (Français)</option>
                            <option value="de" data-flag="https://flagcdn.com/w40/de.png">German (Deutsch)</option>
                            <option value="el" data-flag="https://flagcdn.com/w40/gr.png">Greek (Ελληνικά)</option>
                            <option value="he" data-flag="https://flagcdn.com/w40/il.png">Hebrew (עברית)</option>
                            <option value="hi" data-flag="https://flagcdn.com/w40/in.png">Hindi (हिन्दी)</option>
                            <option value="hu" data-flag="https://flagcdn.com/w40/hu.png">Hungarian (Magyar)</option>
                            <option value="id" data-flag="https://flagcdn.com/w40/id.png">Indonesian (Bahasa Indonesia)</option>
                            <option value="it" data-flag="https://flagcdn.com/w40/it.png">Italian (Italiano)</option>
                            <option value="ja" data-flag="https://flagcdn.com/w40/jp.png">Japanese (日本語)</option>
                            <option value="ko" data-flag="https://flagcdn.com/w40/kr.png">Korean (한국어)</option>
                            <option value="ms" data-flag="https://flagcdn.com/w40/my.png">Malay (Bahasa Melayu)</option>
                            <option value="pt" data-flag="https://flagcdn.com/w40/pt.png">Portuguese (Português)</option>
                            <option value="ru" data-flag="https://flagcdn.com/w40/ru.png">Russian (Русский)</option>
                            <option value="es" data-flag="https://flagcdn.com/w40/es.png">Spanish (Español)</option>
                            <option value="ta" data-flag="https://flagcdn.com/w40/in.png">Tamil (தமிழ்)</option>
                            <option value="tr" data-flag="https://flagcdn.com/w40/tr.png">Turkish (Türkçe)</option>
                            <option value="vi" data-flag="https://flagcdn.com/w40/vn.png">Vietnamese (Tiếng Việt)</option> --}}
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="inner_section_categories_main_col scrollable-section">
                    <div class="row selected_languages_wrapper">
                        {{-- Selected Languages will be added here --}}
                        @forelse ($listing->tour_languages ?? [] as $tour_language)
                            <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 single_category_col" data-id="{{ $tour_language->id }}">
                                <div class="inner_section_single_category">
                                    <input type="checkbox" id="selected_language_1" value="{{ $tour_language->id }}" checked="">
                                    <label for="selected_language_1">
                                        <div class="category_icon_wrapper">
                                            <img src="{{ asset("website") ."/". $tour_language->image }}" alt="">
                                        </div>
                                        <div class="category_title">
                                            <h5> {{ $tour_language->country_name }} </h5>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        @empty
                            
                        @endforelse
                        {{-- Selected Languages will be added here --}}
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    <input type="button" name="next" class="next action-button btn button1 " value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {

            function validateLanguages() {
                var selectedLanguages = $('.experience_language_step .selected_languages_wrapper').children().length;
                if(selectedLanguages == 0){
                    $('.experience_language_step .next').prop('disabled', true);
                }else{
                    $('.experience_language_step .next').prop('disabled', false);
                }
            }

            validateLanguages();

            const languageToCountryMap = {
                "ab": "ge", "aa": "dj", "af": "za", "ak": "gh", "sq": "al", "am": "et", 
                "ar": "sa", "hy": "am", "as": "in", "az": "az", "eu": "es", "be": "by",
                "bn": "bd", "bs": "ba", "bg": "bg", "my": "mm", "ca": "es", "zh": "cn",
                "hr": "hr", "cs": "cz", "da": "dk", "nl": "nl", "en": "gb", "eo": "eo",
                "fi": "fi", "fr": "fr", "de": "de", "el": "gr", "he": "il", "hi": "in",
                "hu": "hu", "id": "id", "it": "it", "ja": "jp", "ko": "kr", "ms": "my",
                "pt": "pt", "ru": "ru", "es": "es", "ta": "in", "tr": "tr", "vi": "vn"
            };


            function setPlaceholder() {
                // Find the search input within the Select2 container and set the placeholder
                var select2SearchField = $('.experience_language_step .select2-container .select2-search__field');

                // Only update if it's not already set
                if (select2SearchField.length && !select2SearchField.prop('placeholder')) {
                    select2SearchField.prop('placeholder', @json(translate('stepper.search_languages')));
                }
            }

            function formatLanguage(lang) {
                if (!lang.id) return lang.text; // Return placeholder text
                
                let countryCode = languageToCountryMap[lang.id] || "default";
                let flagUrl = `assets/flags/${countryCode}.png`;

                console.log("Flag URL:", flagUrl); // Debugging

                return $(`<span><img src="${flagUrl}" class="flag-icon" /> ${lang.text}</span>`);
            }

            $('.experience_language_step .select2').on('select2:open select2:close select2:select select2:unselect select2:clear',
                function() {
                    // $('input.select2-search__field').prop('placeholder', 'Search for amenities');
                    setPlaceholder();
                });

            setPlaceholder();


            $(".experience_language_step .select2").select2({
                closeOnSelect: false,
                placeholder: @json(translate('stepper.search_languages')),
                allowClear: false,
                tags: false
            });

            $(document).on('select2:select', '.experience_language_step .select2', function(e) {
                const selectedData = e.params.data;
                console.log("selectedData", selectedData);
                // console.log("selectedDataValue", e.target.value);

                let flag = selectedData.element.getAttribute("data-flag");
                // let countryCode = languageToCountryMap[selectedData.id] || "default";
                let countryCode = flag.substring(flag.lastIndexOf("/") + 1, flag.lastIndexOf("."));

                let flagUrl = `{{asset('website')}}/images/flags/${countryCode}.png`;

                if ($(`.experience_language_step .selected_languages_wrapper .single_category_col[data-id="${selectedData.id}"]`)
                    .length === 0) {
                    $('.experience_language_step .selected_languages_wrapper').append(
                        `<div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 single_category_col" data-id="${selectedData.id}">
                            <div class="inner_section_single_category">
                                <input type="checkbox"  id="selected_language_${selectedData.id}"
                                    value="${selectedData.id}" checked>
                                <label for="selected_language_${selectedData.id}">
                                    <div class="category_icon_wrapper">
                                        <img src="${flagUrl}" alt="">
                                    </div>
                                    <div class="category_title">
                                        <h5>${selectedData.text}</h5>
                                    </div>
                                </label>
                            </div>
                        </div>`
                    );
                }

                validateLanguages();

            });

            $(document).on('select2:unselect', '.experience_language_step .select2', function(e) {
                const deselectedData = e.params.data;
                $(`.experience_language_step .selected_languages_wrapper .single_category_col[data-id="${deselectedData.id}"]`).remove();
                validateLanguages();
            });

            $(document).on('change', '.selected_languages_wrapper .single_category_col input[type="checkbox"]', function() {
                // if ($(this).val()) {
                    const itemId = $(this).val();
                    $(this).closest('.single_category_col').remove();
                    const select2Element = $('.select2');
                    const selectedValues = select2Element.val().map(value => value.toString());
                    console.log("Before filtering:", selectedValues);
                    const updatedValues = selectedValues.filter(value => value !== itemId);
                    console.log("After filtering:", updatedValues);
                    select2Element.val(updatedValues).trigger('change');
                // } else {
                //     $(this).parent().remove();
                // }
                validateLanguages();
            });

            // $('[name="languages[]"]').change(function() {
            //     setTimeout(function(){
            //     var selected_language = $('.selected_languages_wrapper .single_category_col').length;
            //     if(selected_language >= 1 ){
            //         $('.experience_language_step .next').prop('disabled', false);    
            //     } else{
            //         $('.experience_language_step .next').prop('disabled', true);
            //     }
            //     }, 200);
            // });

        });
    </script>
@endpush
