@push('css')
    <link rel="stylesheet" href="{{ asset('plugins/components/dropify/dist/css/dropify.min.css') }}">
@endpush
@php($cms_steppers = $category->cms_steppers)
<div class="steps_parent">
    <div id="accordion">
        <div class="accordion-item">
            <div class="card-header" id="headingOne">
                <h5 class="m-0">
                    <a class="btn btn-link" data-toggle="collapse" data-target="#ownership" aria-expanded="true"
                        aria-controls="ownership">
                        {{ translate('content_management_system.first_step') }}
                    </a>
                </h5>
            </div>
            <div id="ownership" class="accordion-body collapse in" aria-labelledby="headingOwnership"
                data-parent="#accordion">
                <div class="steps step1">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '0',
                        "step_name" => 'ownership',
                        'input_index' => '0',
                        'step_heading' => translate('content_management_system.ownership_step'),
                        'image' => 'yes',
                        'url' => 'yes',
                    ])
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <div class="card-header" id="headingStep1">
                <h5 class="m-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#step1" aria-expanded="false"
                        aria-controls="step1">
                        {{ translate('content_management_system.listing_general_detail') }}
                    </a>
                </h5>
            </div>
            <div id="step1" class="accordion-body collapse" aria-labelledby="step1" data-parent="#accordion">
                <div class="steps step2 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '1',
                        "step_name" => 'step-1-intro',
                        'input_index' => '1',
                        'step_heading' => translate('content_management_system.step_1'),
                        'image' => 'yes',
                    ])
                </div>

                <div class="steps step3 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '1.1',
                        "step_name" => 'type',
                        'input_index' => '2',
                        'step_heading' => translate('content_management_system.types_step'),
                    ])
                </div>

                <div class="steps step4 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '1.2',
                        "step_name" => 'map',
                        'input_index' => '3',
                        'step_heading' => translate('content_management_system.map_step'),
                    ])
                </div>

                <div class="steps step5 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '1.3',
                        "step_name" => 'address-detail',
                        'input_index' => '4',
                        'step_heading' => translate('content_management_system.address_step'),
                    ])
                </div>

                <div class="steps step6">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '1.4',
                        "step_name" => 'detail-step',
                        'input_index' => '5',
                        'step_heading' => translate('content_management_system.detail_step'),
                    ])
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <div class="card-header" id="headingThree">
                <h5 class="m-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#step2" aria-expanded="false"
                        aria-controls="step2">
                        {{ translate('content_management_system.step_2_initialization') }}
                    </a>
                </h5>
            </div>
            <div id="step2" class="accordion-body collapse" aria-labelledby="headingStep2" data-parent="#accordion">
                <div class="steps step7 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2',
                        "step_name" => 'step-2-intro',
                        'input_index' => '6',
                        'step_heading' => translate('content_management_system.step_2'),
                        "image" => 'yes',
                    ])
                </div>

                <div class="steps step8 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2.1',
                        "step_name" => 'amenities',
                        'input_index' => '7',
                        'step_heading' => translate('content_management_system.amenities_step'),
                    ])
                </div>

                <div class="steps step9 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2.2',
                        'input_index' => '8',
                        "step_name" => 'key-feature',
                        'step_heading' => translate('content_management_system.key_feature_step'),
                    ])
                </div>

                <div class="steps step10 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2.3',
                        'input_index' => '9',
                        "step_name" => 'rules',
                        'step_heading' => translate('content_management_system.rules_step'),
                    ])
                </div>

                <div class="steps step11 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2.4',
                        'input_index' => '10',
                        "step_name" => 'notes',
                        'step_heading' => translate('content_management_system.notes_step'),
                    ])
                </div>

                <div class="steps step12 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2.5',
                        'input_index' => '11',
                        "step_name" => 'image-upload',
                        'step_heading' => translate('content_management_system.photos_step'),
                    ])
                </div>

                <div class="steps step13 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2.6',
                        'input_index' => '12',
                        "step_name" => 'file-upload',
                        'step_heading' => translate('content_management_system.document_step'),
                        'url' => 'yes',
                    ])
                </div>

                <div class="steps step14 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2.7',
                        'input_index' => '13',
                        "step_name" => 'title',
                        'step_heading' => translate('content_management_system.title_step'),
                    ])
                </div>

                <div class="steps step15">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '2.5',
                        'input_index' => '14',
                        "step_name" => 'description',
                        'step_heading' => translate('content_management_system.description_step'),
                    ])
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <div class="card-header" id="headingStep3">
                <h5 class="m-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#step3" aria-expanded="false"
                        aria-controls="step1">
                        {{ translate('content_management_system.finalize_listing') }}
                    </a>
                </h5>
            </div>
            <div id="step3" class="accordion-body collapse" aria-labelledby="headingStep3" data-parent="#accordion">
                <div class="steps step16 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '3',
                        'input_index' => '15',
                        "step_name" => 'step-3-intro',
                        'step_heading' => translate('content_management_system.step_3'),
                        'image' => 'yes',
                    ])
                </div>

                <div class="steps step17 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '3.1',
                        'input_index' => '16',
                        "step_name" => 'daily-price',
                        'step_heading' => translate('content_management_system.price_step'),
                        'url' => 'yes',
                        ])
                </div>

                <div class="steps step18 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '3.2',
                        'input_index' => '17',
                        "step_name" => 'cancellation',
                        'step_heading' => translate('content_management_system.cancellation_step'),
                        ])
                </div>
                
                <div class="steps step19 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '3.3',
                        'input_index' => '18',
                        "step_name" => 'minimum-stay',
                        'step_heading' => translate('content_management_system.stay_step'),
                    ])
                </div>

                <div class="steps step20 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '3.4',
                        'input_index' => '19',
                        "step_name" => 'availability',
                        'step_heading' => translate('content_management_system.book_step'),
                    ])
                </div>

                <div class="steps step21 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '3.5',
                        'input_index' => '20',
                        "step_name" => 'advance-booking',
                        'step_heading' => translate('content_management_system.preparation_step'),
                    ])
                </div>

                <div class="steps step22 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '22',
                        'input_index' => '21',
                        "step_name" => 'cut-off-time',
                        'step_heading' => translate('content_management_system.cut_off_time_step'),
                    ])
                </div>

                <div class="steps step23 divider">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '23',
                        'input_index' => '22',
                        "step_name" => 'season-price',
                        'step_heading' => translate('content_management_system.seasonal_step'),
                    ])
                </div>

                <div class="steps step24">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '24',
                        'input_index' => '23',
                        "step_name" => 'discount',
                        'step_heading' => translate('content_management_system.discount_step'),
                    ])
                </div>

            </div>
        </div>
        <div class="accordion-item">
            <div class="card-header" id="headingLastStep">
                <h5 class="m-0">
                    <a class="btn btn-link collapsed" data-toggle="collapse" data-target="#lastStep"
                        aria-expanded="false" aria-controls="lastStep">
                        {{ translate('content_management_system.last_step') }}
                    </a>
                </h5>
            </div>
            <div id="lastStep" class="accordion-body collapse" aria-labelledby="headingLastStep"
                data-parent="#accordion">
                <div class="steps step25">
                    @include('listingCMS.templates.cardInput', [
                        'step_no' => '25',
                        'input_index' => '24',
                        "step_name" => 'review',
                        'step_heading' => translate('content_management_system.review_step_last'),
                    ])
                </div>
            </div>
        </div>
    </div>
</div>

<div class="form-group">
    <div class="col-md-4">
        <input class="btn btn_yellow" type="submit" value="{{ $submitButtonText ?? translate('content_management_system.update') }}">
    </div>
</div>
@push('js')
    <script src="{{ asset('plugins/components/dropify/dist/js/dropify.min.js') }}"></script>

    <script>
        $('.dropify').dropify();
    </script>
@endpush
