@push('css')
    <style>
        .key_features_step .key_features_fields_wrapper .ck-editor__editable_inline {
            min-height: calc(1.5em * 5);
        }

        .key_features_step .key_features_fields_wrapper .ck .ck-placeholder:before,
        .key_features_step .key_features_fields_wrapper .ck.ck-placeholder:before {
            color: #A9AEC0;
            font-weight: 400
        }
    </style>
@endpush

@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "key-feature");
@endphp

<div class="col-md-12">
    <div class="inner_section_main_col">
        <div class="main_step_title">
            <h2>{{ $step_data->title ?? "" }}</h2>
            @isset($step_data->sub_title)
                <p class="sub_title">{{ $step_data->sub_title ?? "" }}</p>
            @endisset
        </div>
        <div class="key_features_fields_wrapper scrollable-section">
            <div class="features_add_btn_wrapper">
                <label for="">{{ translate('stepper.key_features') }} ({{ translate('stepper.optional') }})</label>
                <a class="add_feature_btn cust_add" href="javascript:void(0)">{{ translate('stepper.add') }}</a>
            </div>
            <div class="txt_field">
                <input class="form-control key_features_title repeater1 no_validate" type="text"
                    placeholder="{{ $titlePlaceholder }}">
            </div>
            <textarea class="ck_editor no_validate" id="editor" placeholder="{{ $descriptionPlaceholder }}"></textarea>
            <div class="saved_features_list_wrapper mt-4">
                @forelse ($listing->key_features ?? [] as $key_feature)
                    <div class="single_saved_feature">
                        <div class="content_cross_wrapper">
                            <div class="title_description_wrapper w-100">
                                <div class="feature_title">
                                    <h6>{{ $key_feature->title }}</h6>
                                    <input type="hidden" id="key_feature_title_{{ $loop->index }}"
                                        name="key_features[{{ $loop->index }}][title]" class="add_input no_validate"
                                        value="{{ $key_feature->title }}">
                                </div>
                                <div class="feature_description">
                                    <p>
                                        <input type="hidden" id="key_feature_desc_{{ $loop->index }}"
                                            name="key_features[{{ $loop->index }}][description]"
                                            class="add_input no_validate" value="{{ $key_feature->description }}">
                                        {!! $key_feature->description !!}
                                    </p>
                                </div>
                            </div>
                            <div class="cross_icon_wrapper">
                                <a class="remove_key_feature_btn" href="javascript:void(0)">
                                    <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                @endforelse
            </div>
        </div>
    </div>
</div>

@push('js')
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script>


        function validateKeyFeaturesFields() {
            const inputs = $('.highlight_key_features_step .key_features_fields_wrapper .key_features_title');
            const keyFeaturesData = editorInstance.getData().trim(); // Get CKEditor data

            let isAnyFieldFilled = keyFeaturesData.length > 0; // Check if CKEditor has content

            // Check if any input field has data
            inputs.each(function() {
                if ($(this).val().trim().length > 0) {
                    isAnyFieldFilled = true;
                    return false; // Exit loop early
                }
            });

            // Disable .next button when any field is filled, enable when all are empty
            $('.highlight_key_features_step .next').prop("disabled", isAnyFieldFilled);
        }

        $(document).on('input blur', '.highlight_key_features_step .key_features_fields_wrapper .key_features_title', function() {
            validateKeyFeaturesFields();
        });



        let editorInstance;
        ClassicEditor
            .create(document.querySelector('#editor'), {
                enterMode: 'paragraph',
                shiftEnterMode: 'softBreak',
                toolbar: false,
            })
            .then(editor => {
                console.log('Editor was initialized', editor);
                editorInstance = editor;

                editorInstance.model.document.on('change:data', () => {
                    validateKeyFeaturesFields();
                });

            })
            .catch(error => {
                console.error('There was a problem initializing the editor.', error);
            });


        $(document).ready(function() {
            var counter =
                {{ isset($listing->key_features) ? count($listing->key_features) : 0 }}; // Ensure counter is declared with 'var' to limit its scope
            $(document).on('click',
                '.highlight_key_features_step .features_add_btn_wrapper .add_feature_btn',
                function() {
                    var title = $(this).closest('fieldset').find('.key_features_title').val().trim();
                    // var description = $(this).closest('fieldset').find('.key_features_description').val().trim();
                    var description = editorInstance.getData();
                    var plainText = $('<div>').html(description).text();

                    if (title !== "") {
                        var featureHTML = `
                        <div class="single_saved_feature">
                            <div class="content_cross_wrapper">
                                <div class="title_description_wrapper w-100">
                                    <div class="feature_title">
                                        <h6>${title}</h6>
                                        <input type="hidden" id="key_feature_title_${counter}" name="key_features[${counter}][title]" class="add_input no_validate" value="${title}" />
                                    </div>
                                    <div class="feature_description">
                                        <p class=" fs-14 m-0">
                                            <input type="hidden" id="key_feature_desc_${counter}" name="key_features[${counter}][description]" class="add_input no_validate" value="${description || @json(translate('stepper.no_description'))}" />
                                            ${plainText || @json(translate('stepper.no_description'))}
                                        </p>
                                    </div>
                                </div>
                                <div class="cross_icon_wrapper">
                                    <a class="remove_key_feature_btn" href="javascript:void(0)">
                                        <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                    </a>
                                </div>
                            </div>
                        </div>`;

                        $('.highlight_key_features_step .saved_features_list_wrapper').append(
                            featureHTML);

                        $(this).closest('fieldset').find('.key_features_title').val('');
                        // $(this).closest('fieldset').find('.key_features_description').val('');
                        if (editorInstance) {
                            editorInstance.setData(''); // Set content to an empty string
                        }

                        validateKeyFeaturesFields();

                        counter++;
                    } else {
                        Swal.fire({
                            title: @json(translate('stepper.error')),
                            text: @json(translate('stepper.fill_fields_first')),
                            icon: "error"
                        });
                    }
                });
            $(document).on('click', '.highlight_key_features_step .remove_key_feature_btn',
                function() {
                    $(this).closest('.single_saved_feature').remove();
                    reindexName();

                    if ($('input[name="itineraries[0][title]"]').length > 0) {
                        console.log('Input exists.');
                    } else {
                        $('.itinerary_parent .txt_field input').removeClass('no_validate');
                        $('.itinerary_parent .txt_field textarea').removeClass('no_validate');
                    }
                });

            function reindexName() {
                $('.highlight_key_features_step .saved_features_list_wrapper .single_saved_feature')
                    .each(function(index) {
                        $(this).find('input').each(function() {
                            var name = $(this).attr('name');
                            var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                            $(this).attr('name', newName);

                            var id = $(this).attr('id');
                            var baseId = id.substring(0, id.lastIndexOf('_') + 1);
                            var newId = baseId + index;
                            $(this).attr('id', newId);
                        });
                    });
                counter = $(
                    '.highlight_key_features_step .saved_features_list_wrapper .single_saved_feature'
                ).length;
            }
        });
    </script>
@endpush
