name: <PERSON><PERSON>I/CD

on:
  push:
    branches:
      - production  # Adjust if your default branch is different

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout Code
      uses: actions/checkout@v3

    - name: Set up SSH
      uses: webfactory/ssh-agent@v0.5.3
      with:
        ssh-private-key: ${{ secrets.FRAY_PRIVATE_KEY }}

    - name: Set Permissions
      run: |
        ssh -o StrictHostKeyChecking=no luxustar@${{ secrets.FRAY_IP }} << 'EOF'
        sudo chown -R luxustar:luxustar /var/www/html
        EOF

    - name: Sync Project to Server
      run: |
        rsync -avz \
        --exclude '.git/' \
        --exclude '.gitignore' \
        --exclude '.env' \
        --exclude '.env_prod' \
        --exclude '.env_staging' \
        --exclude 'storage/' \
        --exclude 'bootstrap/' \
        --exclude 'public/AdminDashboard/' \
        --exclude 'public/website/about-us/' \
        --exclude 'public/website/amenities-images/' \
        --exclude 'public/website/amenity-options/' \
        --exclude 'public/website/attachments/' \
        --exclude 'public/website/category/' \
        --exclude 'public/website/faq-image/' \
        --exclude 'public/website/listing-file/' \
        --exclude 'public/website/listing-image/' \
        --exclude 'public/website/listing-type/' \
        --exclude 'public/website/user-profile/' \
        --exclude 'resources/lang/' \
        --exclude 'resources/lang/' \
        -e "ssh -o StrictHostKeyChecking=no" \
        ./ luxustar@${{ secrets.FRAY_IP }}:/var/www/html

    - name: Change Permissions
      run: |
        ssh -o StrictHostKeyChecking=no luxustar@${{ secrets.FRAY_IP }} << 'EOF'
        sudo chown -R www-data:www-data /var/www/html
        sudo chmod -R 775 /var/www/html
        EOF
