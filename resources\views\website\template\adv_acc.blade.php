<section class="Residence-modal">
    <div class="modal fade" id="filter_category" tabindex="-1" aria-labelledby="filterCategoryLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="filterCategoryLabel">{{ translate('advance_filters_modal.filters') }}</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.price_range') }}</h6>
                        <input type="text" class="js-range-slider" name="price_range" value="" data-skin="round"
                            data-type="double" data-min="1" data-step="5" data-max="1000000" data-grid="false" />
                    </div>
                    <div class="bg_white resident-list">
                        <h6 class="title">{{ translate('advance_filters_modal.resident_type') }}</h6>
                        <select class="js-example-basic-multiple"  name="listing_types[]" multiple="multiple" data-placeholder="{{ translate('advance_filters_modal.select_type') }}">
                            {{-- <option value="" disabled>Type Search Field</option> --}}
                            @foreach ($categories->where('id', 4)->first()->listing_types as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                        </select>
                        {{-- <ul class="icheck-list">
                            @foreach ($listing_types as $key => $listing_type)
                                @if ($listing_type->category_id == 4)
                                    <li>
                                        <div class="checked" style="position: relative;">
                                            <input type="checkbox" class="check" id="{{ $key }}-listing-type"
                                                data-checkbox="icheckbox_flat-yellow" name="types[]"
                                                style="position: absolute; opacity: 0;">
                                        </div>
                                        <label for="{{ $key }}-listing-type"
                                            class="">{{ $listing_type->name ?? '' }}</label>
                                    </li>
                                @endif
                            @endforeach
                        </ul> --}}
                    </div>
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.super_star_status') }}</h6>
                        <div class="custom-radio">
                            {{-- <input type="checkbox" class="rating_radio_btn" id="exp-one-plus" name="ratings" checked>
                            <label for="exp-one-plus">1+</label>
                            <input type="checkbox" class="rating_radio_btn" id="exp-two-plus" name="ratings">
                            <label for="exp-two-plus">2+</label> --}}
                            <input type="checkbox" class="rating_radio_btn" id="exp-three-plus" name="ratings" value="3">
                            <label for="exp-three-plus" class="ms-0">3+</label>
                            <input type="checkbox" class="rating_radio_btn" id="exp-four-plus" name="ratings" value="4">
                            <label for="exp-four-plus">4+</label>
                        </div>
                    </div>
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.amenities') }}</h6>
                        <select class="js-example-basic-multiple" name="amenities[]" multiple="multiple" data-placeholder="{{ translate('advance_filters_modal.select_amenities') }}">
                            {{-- <option value="" disabled>Type Search Field</option> --}}
                            @foreach ($categories->where('id', 4)->first()->amenities as $amenity)
                            <option value="{{ $amenity->id }}">{{ $amenity->name }}</option>
                        @endforeach
                        </select>
                    </div>
                    <div class="bg_white">
                        <div class="d-flex justify-content-between">
                            <div class="rooms_parent">
                                <h6 class="title">{{ translate('advance_filters_modal.bedrooms') }}</h6>
                                <div class="guest_wrapper d-flex gap-2 align-items-center justify-content-between">
                                    <button class="minus_btn p-0 " type="button"
                                        onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                        <i class="fa fa-minus" aria-hidden="true"></i>
                                    </button>
                                    <input type="number" name="bedroom" class="form-control border-0 text-center m-0 p-0"
                                        id="bedroom" min="0" max="" value="1" readonly />
                                    <button class="plus_btn p-0" type="button"
                                        onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                        <i class="fa fa-plus" aria-hidden="true"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="rooms_parent">
                                <h6 class="title">{{ translate('advance_filters_modal.bathrooms') }}</h6>
                                <div class="guest_wrapper d-flex gap-2 align-items-center justify-content-between">
                                    <button class="minus_btn p-0 " type="button"
                                        onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                        <i class="fa fa-minus" aria-hidden="true"></i>
                                    </button>
                                    <input type="number" name="bathroom" class="form-control border-0 text-center m-0 p-0"
                                        id="bathroom" min="0" max="" value="1" readonly />
                                    <button class="plus_btn p-0" type="button"
                                        onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                        <i class="fa fa-plus" aria-hidden="true"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg_white rules-list">
                        <h6 class="title">{{ translate('advance_filters_modal.rules') }}</h6>
                        <div class="custom-radio">
                            <input type="radio" class="rating_radio_btn" id="pets_allowed" name="pet" value="yes">
                            <label for="pets_allowed" class="ms-0">{{ translate('advance_filters_modal.pets_allowed') }}</label>
                            <input type="radio" class="rating_radio_btn" id="pets_not_allowed" name="pet" value="no">
                            <label for="pets_not_allowed">{{ translate('advance_filters_modal.pets_not_allowed') }}</label>
                        </div>
                    </div>
                    {{-- <div class="bg_white">
                        <h6 class="title">discounts</h6>
                        <div class="custom-radio">
                            <input type="radio" class="discount_radio_btn" id="yes" name="discounts_id"
                                checked>
                            <label for="yes">yes</label>
                            <input type="radio" class="discount_radio_btn" id="no" name="discounts_id">
                            <label for="no">no</label>
                        </div>
                    </div>
                    
                    <div class="bg_white">
                        <h6 class="title">free cancellation</h6>
                        <div class="custom-radio">
                            <input type="radio" class="free_cancellation_radio_btn" id="free_cancellation_yes"
                                name="free_cancellation_id" checked>
                            <label for="free_cancellation_yes">yes</label>
                            <input type="radio" class="free_cancellation_radio_btn" id="free_cancellation_no"
                                name="free_cancellation_id">
                            <label for="free_cancellation_no">no</label>
                        </div>
                    </div> --}}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn yellow-btn adv_apply">{{ translate('advance_filters_modal.apply') }}</button>
                    <button type="button" class="btn white-btn adv_reset">{{ translate('advance_filters_modal.reset') }}</button>
                </div>
            </div>
        </div>
    </div>
</section>
