@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "minimum-stay");
@endphp

<fieldset class="select_categories_step min_stay_requirement_step" id="">
    <div class="inner_section_fieldset h_100">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>

                    <div class="step_description">
                        <p>{{ $step_data->sub_title ?? "" }}</p>
                    </div>

                    {{-- <div class="categories_search_field">
                        <div class="txt_field">
                            <input class="form-control no_validate categories_search" type="search" name="categories_search" id="" placeholder="Search categories">
dden="true"></i>                            <i class="fa fa-search" aria-hi
                        </div>
                    </div> --}}

                </div>
            </div>
        </div>

        <div class="row">

            <div class="col-md-12">
                <div class="inner_section_categories_main_col scrollable-section">
                    <div class="row">
                        {{-- Loop to be insert here --}}
                        {{-- @foreach ($category->listing_types as $key => $type) --}}
                        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-4 single_category_col">
                            <div class="inner_section_single_category">
                                <input type="radio" name="minimum_stay_length" id="min_stay_req_1" value="1"
                                    @if (($listing->detail->minimum_stay_length ?? '') == 1 || !isset($listing->detail->minimum_stay_length)) checked @endif>
                                <label for="min_stay_req_1">
                                    <div class="category_icon_wrapper">
                                        <img src="{{ asset('website') }}/images/numeric_1.png" alt="">
                                    </div>
                                    <div class="category_title">
                                        <h5>{{ translate('stepper.one_night') }}</h5>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-4 single_category_col">
                            <div class="inner_section_single_category">
                                <input type="radio" name="minimum_stay_length" id="min_stay_req_2" value="2"
                                    {{ ($listing->detail->minimum_stay_length ?? '') == 2 ? 'checked' : '' }}>
                                <label for="min_stay_req_2">
                                    <div class="category_icon_wrapper">
                                        <img src="{{ asset('website') }}/images/numeric_2.png" alt="">
                                    </div>
                                    <div class="category_title">
                                        <h5>{{ translate('stepper.two_nights') }} </h5>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-4 single_category_col">
                            <div class="inner_section_single_category">
                                <input type="radio" name="minimum_stay_length" id="min_stay_req_3" value="3"
                                    {{ ($listing->detail->minimum_stay_length ?? '') == 3 ? 'checked' : '' }}>
                                <label for="min_stay_req_3">
                                    <div class="category_icon_wrapper">
                                        <img src="{{ asset('website') }}/images/numeric_3.png" alt="">
                                    </div>
                                    <div class="category_title">
                                        <h5>{{ translate('stepper.three_nights') }}</h5>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-4 single_category_col">
                            <div class="inner_section_single_category">
                                <input type="radio" name="minimum_stay_length" id="min_stay_req_4" value="4"
                                    {{ ($listing->detail->minimum_stay_length ?? '') == 4 ? 'checked' : '' }}>
                                <label for="min_stay_req_4">
                                    <div class="category_icon_wrapper">
                                        <img src="{{ asset('website') }}/images/numeric_4.png" alt="">
                                    </div>
                                    <div class="category_title">
                                        <h5>{{ translate('stepper.four_nights') }}</h5>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-4 single_category_col">
                            <div class="inner_section_single_category">
                                <input type="radio" name="minimum_stay_length" id="min_stay_req_5" value="5"
                                    {{ ($listing->detail->minimum_stay_length ?? '') == 5 ? 'checked' : '' }}>
                                <label for="min_stay_req_5">
                                    <div class="category_icon_wrapper">
                                        <img src="{{ asset('website') }}/images/numeric_5.png" alt="">
                                    </div>
                                    <div class="category_title">
                                        <h5>{{ translate('stepper.five_nights') }} </h5>
                                    </div>
                                </label>
                            </div>
                        </div>

                        {{-- @endforeach --}}
                        {{-- Loop ends here --}}
                    </div>
                </div>
            </div>
        </div>

    </div>
    {{-- <div class="progress">
        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0"
            aria-valuemax="100"></div>
    </div> --}}
    <input type="button" name="next" class="next action-button btn button1 " value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>

@push('js')
    <script>
        $(document).on('keyup', '.categories_search', function() {
            let input = $(this).val().toLowerCase();

            $('.single_category_col').each(function() {
                if ($(this).find('.category_title h5').text().toLowerCase().includes(input)) {
                    $(this).css('display', 'block');
                } else {
                    $(this).css('display', 'none');
                }
            });
        });
    </script>
@endpush
