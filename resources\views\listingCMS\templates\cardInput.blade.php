@php
    $image = $image ?? 'no';
    $url = $url ?? 'no';
    $step_name = $step_name ?? '';
    $step_data = $category?->cms_steppers->firstWhere('step_name', $step_name);
@endphp

<div class="row m-0">
    <input type="hidden" name="step[{{ $input_index ?? 0 }}][step_name]" value="{{ $step_name }}">
    <div class="col-md-12">
        <h2 class="heading">{{ $step_heading }}</h2>
    </div>
    @if ($image == 'yes')
        <div class="col-md-7">
            <div class="form-group">
                <label for="image{{ $step_no }}" class="label_heading">{{ translate('content_management_system.upload_image') }}</label>
                <div class="form_field_padding">
                    <input type="file" id="image{{ $step_no }}" name="step[{{ $input_index ?? 0 }}][image]" class="dropify"
                        accept="image/*" data-height="150"
                        data-default-file="{{ asset("website") . "/" . ($step_data?->image) }}" />
                </div>
            </div>
        </div>
    @endif
    <div class="col-md-6">
        <div class="form-group ">
            <label for="step{{ $step_no }}-en-title" class="label_heading">{{ translate('content_management_system.enter_title_in_english') }}</label>
            <div class="form_field_padding">
                <input class="form-control" name="step[{{ $input_index ?? 0 }}][en][title]" type="text"
                    id="step{{ $step_no }}-en-title" placeholder="{{ translate('content_management_system.enter_title_in_english') }}"
                    value="{{ isset($step_data) ? @$step_data?->translate("en")->title : "" }}">
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group ">
            <label for="step{{ $step_no }}-es-title" class="label_heading">{{ translate('content_management_system.enter_title_in_spanish') }}</label>
            <div class="form_field_padding">
                <input class="form-control" name="step[{{ $input_index ?? 0 }}][es][title]" type="text"
                    id="step{{ $step_no }}-es-title" placeholder="{{ translate('content_management_system.enter_title_in_spanish') }}"
                    value="{{ isset($step_data) ? @$step_data->translate("es")->title : "" }}">
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group ">
            <label for="step{{ $step_no }}-en-sub" class="label_heading">{{ translate('content_management_system.sub_heading_in_english') }}</label>
            <div class="form_field_padding">
                <textarea class="form-control" name="step[{{ $input_index ?? 0 }}][en][sub_title]" type="text"
                    id="step{{ $step_no }}-en-sub" placeholder="{{ translate('content_management_system.enter_sub_heading_in_english') }}" rows="5"
                    value="">{{ isset($step_data) ? @$step_data->translate("en")->sub_title : "" }}</textarea>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group ">
            <label for="step{{ $step_no }}-es-sub" class="label_heading">{{ translate('content_management_system.sub_heading_in_spanish') }}</label>
            <div class="form_field_padding">
                <textarea class="form-control" name="step[{{ $input_index ?? 0 }}][es][sub_title]" type="text"
                    id="step{{ $step_no }}-es-sub" placeholder="{{ translate('content_management_system.enter_sub_heading_in_spanish') }}" rows="5"
                    value="">{{ isset($step_data) ? @$step_data->translate("es")->sub_title : "" }}</textarea>
            </div>
        </div>
    </div>
    @if ($url == 'yes')
        <div class="col-md-12">
            <div class="form-group ">
                <label for="doc_link" class="label_heading">{{ 'URL' }}</label>
                <div class="form_field_padding">
                    <input class="form-control" name="step[{{ $input_index ?? 0 }}][url]" type="url" id="doc_link"
                        placeholder="Enter URL" value="{{ @$step_data->url ?? "" }}">
                </div>
            </div>
        </div>
    @endif
    <input type="hidden" value="{{ $step_no }}" name="step[{{ $input_index ?? 0 }}][step_no]">
</div>
