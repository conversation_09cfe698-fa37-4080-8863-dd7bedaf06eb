<?php

use Carbon\Carbon;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Http;

function api_response($status, $message, $data = null)
{
    return ["status" => $status, "message" => $message, "data" => $data];
}

function get_auth_token($login = "fc52c42e1817e90bde42839d5687a6f6", $tranKey = "3lkldtyiA863H76n")
{
    $nonce = random_bytes(16);
    $seed = date('c');
    $digest = base64_encode(hash('sha256', $nonce . $seed . $tranKey, true));
    return [
        'login' => $login,
        'tranKey' => $digest,
        'nonce' => base64_encode($nonce),
        'seed' => $seed,
    ];
}

function replacePhoneNumbers($text)
{
    $pattern = '/\b(1\s?)?(\(\d{3}\)|\d{3})?([-.\s]?\d{3}[-.\s]?\d{4}|\d{5,11})\b/';
    $replacedText = preg_replace($pattern, 'xxxxxxxxx', $text); // replace phone number with xxxxxxxx
    return $replacedText;
}

function admins_notification($notification_class)
{
    $admins = App\Models\User::get();
    foreach ($admins as $admin) {
        if ($admin->hasRole(['user', "sub_admin"]) || $admin->hasRole("sub_admin")) {
            $admin->notify($notification_class);
        }
    }
}

function google_map_key()
{
    return "AIzaSyAbi9IUF4TBu58oC9iGZexb045rMaQr2AQ";
    //return "AIzaSyD0ypp0864A7OFyrLPMOy60MyGA58M-eyc";
}
function skyflow_bearer_token()
{
    $creds = config("constant.skyflow_token_creds");
    $claims = [
        "iss" => $creds["clientID"],
        "key" => $creds["keyID"],
        "aud" => $creds["tokenURI"],
        "exp" => time() + 3600, // JWT expires in Now + 60 minutes
        "sub" => $creds["clientID"]
    ];

    $privateKey = $creds["privateKey"];
    $signedJWT = JWT::encode($claims, $privateKey, 'RS256');
    $body = [
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $signedJWT
    ];

    $response = Http::post($creds["tokenURI"], $body);
    if ($response->successful()) {
        return $response->json();
    } else {
        throw new \Exception("Failed to obtain Bearer token: " . $response->body());
    }
}
function skyflow_acc_id()
{
    return config('constant.skyflow_acc_id');
}
function skyflow_workspace_id()
{
    return config('constant.skyflow_workspace_id');
}
function skyflow_vault_url()
{
    return config('constant.skyflow_vault_url');
}
function skyflow_vault_id()
{
    return config('constant.skyflow_vault_id');
}
function front_end_bearer_token()
{
    $creds = config("constant.frontend_token_creds");
    $claims = [
        "iss" => $creds["clientID"],
        "key" => $creds["keyID"],
        "aud" => $creds["tokenURI"],
        "exp" => time() + 3600, // JWT expires in Now + 60 minutes
        "sub" => $creds["clientID"]
    ];

    $privateKey = $creds["privateKey"];
    $signedJWT = JWT::encode($claims, $privateKey, 'RS256');
    $body = [
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $signedJWT
    ];

    $response = Http::post($creds["tokenURI"], $body);
    if ($response->successful()) {
        return $response->json();
    } else {
        throw new \Exception("Failed to obtain Bearer token: " . $response->body());
    }
}
function skyflow_stripe_bearer_token()
{
    $creds = config("constant.skyflow_stripe_token_creds");
    $claims = [
        "iss" => $creds["clientID"],
        "key" => $creds["keyID"],
        "aud" => $creds["tokenURI"],
        "exp" => time() + 3600, // JWT expires in Now + 60 minutes
        "sub" => $creds["clientID"]
    ];

    $privateKey = $creds["privateKey"];
    $signedJWT = JWT::encode($claims, $privateKey, 'RS256');
    $body = [
        'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        'assertion' => $signedJWT
    ];

    $response = Http::post($creds["tokenURI"], $body);
    if ($response->successful()) {
        return $response->json();
    } else {
        throw new \Exception("Failed to obtain Bearer token: " . $response->body());
    }
}

function skyflow_payment_method($card, $cvc)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "https://dbfd4d880008.gateway.skyflowapis.com/v1/gateway/outboundRoutes/a97d1989c5664e4bbd898b7bc3fd8146/v1/payment_methods");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
        'type' => 'card',
        'card' => [
            'number' => $card["card_number"],
            'exp_month' => $card["expiry_date"],
            'exp_year' => $card["expiry_date"],
            'cvc' => $cvc,
        ]
    ]));
    $headers = [
        'X-Skyflow-Authorization: ' . skyflow_bearer_token()["accessToken"],
        'Authorization: Basic ' . base64_encode(secret_key()),
        'Content-Type: application/x-www-form-urlencoded'
    ];

    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        echo 'Error:' . curl_error($ch);
    }

    curl_close($ch);

    $response = json_decode($response, true);
    return $response;
}
function stripe_key()
{
    return config("constant.STRIPE_KEY");
}
function secret_key()
{
    return config("constant.STRIPE_SECRET");
}
function generate_otp($length = 6)
{
    $length = 6;
    $numeric = '123456789';
    $alpha = '123456789';
    $otp = '';
    for ($i = 0; $i < $length; $i++) {
        $set = ($i % 2 == 0) ? $alpha : $numeric;
        $otp .= $set[random_int(0, strlen($set) - 1)];
    }
    return $otp;
}

// function check_listing_price($listing_id, $total_amount)
// {
//     $listing = App\Listing::find($listing_id);
//     if ($listing) {
//         $price = 0;
//         switch ($listing->detail->basis_type) {
//             case 'Tour':
//                 $price = $listing->detail->adult_price;
//                 break;
//             case 'Hourly':
//                 $price = $listing->detail->per_hour;
//                 break;
//             case 'Daily':
//                 $price = $listing->detail->per_day;
//                 break;
//         }
//         if ($total_amount < $price) {
//             return false;
//             // return api_response(false, "Total Amount is not valid");
//         } else {
//             return true;
//         }
//     }
// }

function common_setting(){
    return App\CommonSetting::first();
}
function get_percentage($discount_percentage, $total_amount)
{
    $discount_value = ($total_amount * $discount_percentage) / 100;
    return $discount_value;
}
function base_price($listing_detail)
{
    if ($listing_detail["basis_type"] == "Daily") {
        return $listing_detail['per_day'];
    } elseif($listing_detail["basis_type"] == "Hourly") {
        return $listing_detail['per_hour'];
    }else{
        return $listing_detail['adult_price'];
    }
}
function get_paypal_percentage($discount_percentage, $total_amount)
{
    $discount_value = ($total_amount * $discount_percentage) / 100;
    return $discount_value;
}
function weeklyMonthlyDiscount($totalDays, $total_amount, $discount)
{
    $discount_name = null;
    $discount_percentage = null;
    $discount_amount = null;
    $weekly_days = 7;
    $monthly_days = 28;
    $weekly_discount_percentage = $discount['weekly_discount'] ?? 0;
    $monthly_discount_percentage = $discount['monthly_discount'] ?? 0;
    if ($totalDays >= $weekly_days && $totalDays < $monthly_days && $weekly_discount_percentage != 0) {
        $weekly_discount = get_percentage($total_amount, $weekly_discount_percentage);
        $total_amount -= $weekly_discount;
        $discount_amount = $weekly_discount;
        $discount_name = "Weekly Discount";
        $discount_percentage = $weekly_discount_percentage;
    } elseif ($totalDays >= $monthly_days && $monthly_discount_percentage != 0) {
        $monthly_discount = get_percentage($total_amount, $monthly_discount_percentage);
        $discount_amount = $monthly_discount;
        $total_amount -= $monthly_discount;
        $discount_name = "Monthly Discount";
        $discount_percentage = $monthly_discount_percentage;
    }
    return [
        'total_amount' => $total_amount,
        'discount_name' => $discount_name,
        'discount_percentage' => $discount_percentage,
        "discount_amount" => $discount_amount
    ];
}
function newListingDiscount($total_amount, $listing_booking_count, $discount)
{
    $discount_name = null;
    $discount_percentage = null;
    $discount_amount = null;
    $new_listing_discount_percentage = $discount['new_listing_discount'] ?? null;
    if (isset($new_listing_discount_percentage) && $listing_booking_count < 3) {
        $new_listing_discount = get_percentage($total_amount, $new_listing_discount_percentage);
        $discount_amount = $new_listing_discount;
        $total_amount -= $new_listing_discount;
        $discount_name = "New Listing Discount";
        $discount_percentage = $new_listing_discount_percentage;
    }
    return [
        'total_amount' => $total_amount,
        'discount_name' => $discount_name,
        'discount_percentage' => $discount_percentage,
        "discount_amount" => $discount_amount
    ];
}
function season_price($start_date, $base_price, $seasons)
{
    $listingPrice = $base_price;
    $today = Carbon::parse($start_date)->format('m/d/Y');
    foreach ($seasons as $season) {
        if ($today >= $season['start_date'] && $today <= $season['end_date']) {
            if ($season['type'] === 'Increase') {
                $listingPrice += ($listingPrice * $season['percentage'] / 100);
            } elseif ($season['type'] === 'Decrease') {
                $listingPrice -= ($listingPrice * $season['percentage'] / 100);
            }
        }
    }
    return $listingPrice;
}
    // Function to fetch translation based on the key
function translate($key)
{
    $locale = app()->getLocale(); // Get the current locale (e.g., 'en', 'fr', 'es')
    $path = resource_path("lang/{$locale}.json"); // Path to the language JSON file

    if (file_exists($path)) {
        // Read the JSON file and decode it into an array
        $translations = json_decode(file_get_contents($path), true);
        return data_get($translations, $key, $key);  // Return the translation or the key if not found
    }
    return $key; // Return the key if translation file is missing
}