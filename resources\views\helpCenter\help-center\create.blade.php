@extends('layouts.master')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">{{ translate('dashboard_help_center.create_new_category') }}
                    {{-- {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }} --}}
                    </h3>
                    @can('view-'.str_slug('HelpCenter'))
                    <a  class="btn btn_yellow pull-right" href="{{url('/helpCenter/help-center')}}">
                        {{-- <i class="icon-arrow-left-circle"></i>  --}}
                        {{ translate('dashboard_help_center.back') }}</a>
                    @endcan

                    <div class="clearfix"></div>
                    <hr>
                    @if ($errors->any())
                        <ul class="alert alert-danger">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif

                    <form method="POST" action="{{ url('/helpCenter/help-center') }}" accept-charset="UTF-8"
                          class="form-horizontal" enctype="multipart/form-data">
                        {{ csrf_field() }}

                        @include ('helpCenter.help-center.form')
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
