@extends('website.layout.master')

@push('css')
    <style>
        body:has(.error-page) .footer, body:has(.error-page) .navbar>div *:not(.navbar-brand, .navbar-brand > *) { display: none !important;}
        .full-height {  height: 80vh;}
        .fs-100 { font-size: 100px;}
        body:has(.error-page) .navbar>div { justify-content: center; align-items: center;}
    </style>
@endpush

@section('content')
    <section class="full-height error 500-error error-page">
        <div class="container h-100">
            <div class="row h-100 justify-content-center align-items-center">
                <div class="col-md-6 text-center d-flex gap-4 flex-column">
                    <h1 class="semi-bold fs-100">{{ translate('500_page.oops') }}</h1>
                    <h4 class="light-bold">{{ translate('500_page.internal_server_error') }}</h4>
                    <p>{{ translate('500_page.something_went_wrong') }}</p>
                    <div class="back_btn">
                        <a href="{{ '/' }}" class="button button1 semi-bold">{{ translate('500_page.go_back_home') }}</a>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
    