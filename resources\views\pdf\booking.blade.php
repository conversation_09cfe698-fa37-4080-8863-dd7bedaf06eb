<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Booking Detail</title>
</head>
<style>
    @import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
    @import url("{{ asset('website/css/style.css') }}");
</style>

<body style="max-width: 1200px; margin-inline: auto;">
    <div style="padding:35px;">
        <div style="padding: 30px;">
            <div style="display: inline-block; width: 48%;">
                <h1 style="font-size: 30px; font-weight:600; padding-bottom: 20px;">{{ $listing->name }}</h1>
            </div>
            <div style="display: inline-block; width: 48%; text-align: right;">
                <h3 style="font-size: 20px; font-weight: 500; padding-bottom: 20px; text-align: right;">{{ translate('booking_pdf.booking_id') }}:
                    <span style="color: #626262; text-decoration: underline;">{{ $booking->booking_number }}</span>
                </h3>
            </div>
            <div style="padding: 10px 0;">
                <div style="display: inline-block; vertical-align: top;">
                    <div>
                        <span>{{ $listing->type->name ?? '' }} |</span>
                        <span>
                            {{ $listing->address?->state ?? '' }},
                            {{ $listing->address?->city ?? '' }},
                            {{ $listing->address?->country ?? '' }}
                        </span>
                    </div>
                </div>
            </div>
            <div style="padding: 25px 0;">
                <div style="display: inline-block; vertical-align: top;">
                    <img src="{{ asset('website') . '/' . $provider->avatar }}" alt="Host"
                        style="border-radius: 50%; object-fit: cover;  height: 60px; width: 60px;">
                </div>
                <div style="display: inline-block; width: 80%; vertical-align: middle; padding-left: 15px;">
                    <h5 style="color: #000; font-size: 20px; font-weight: 400; vertical-align: middle;">
                        {{ translate('booking_pdf.hosted_by') }} <span>{{ $provider->name }}</span>
                    </h5>
                </div>
            </div>
            <img src="{{ asset('website') . '/' . ($listing->thumbnail_image->url ?? '') }}" alt="Lisiting"
                style="border-radius: 20px; object-fit: cover; height:400px; width: 100%;">
            <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB;">
                <div style="color: #626262;">
                    {!! $listing->description !!}
                </div>
            </div>


            {{-- <div style="border-bottom: 2px solid #DBDBDB;">
                <ul style="list-style: none; padding: 0;">
                    <li
                        style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                        <img src="http://127.0.0.1:8000/website/images/user.svg"
                            style="vertical-align:bottom; margin-right: 5px; height: 20px; width: 20px;">
                        1 Guests
                    </li>
                    <li
                        style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                        <img src="http://127.0.0.1:8000/website/images/bedroom.svg"
                            style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                        1 Bedrooms
                    </li>
                    <li
                        style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                        <img src="http://127.0.0.1:8000/website/images/bath.svg"
                            style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                        1 Bathrooms
                    </li>
                    <li
                        style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                        <img src="http://127.0.0.1:8000/website/images/check-in.png"
                            style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                        Check-in After 0
                    </li>
                    <li
                        style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                        <img src="http://127.0.0.1:8000/website/images/check-out.png"
                            style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                        Check-out after 0
                    </li>
                </ul>
            </div>
            <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB;">
                <div class="amenities-box">
                    <h3 style="font-size: 22px; margin-top: 0;">Everything included in this place</h3>
                    <div style="padding: 10px 0;">
                        <div style="display: inline-block; margin: 10px 20px 10px 0; border-radius: 10px; width: 45%;">
                            <img src="http://127.0.0.1:8000/website/amenity-options/3P8S5N52f3NdTw8E7RwKb3SVFUx7MafFmAC4s83K.png"
                                style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 30px; width: 30px;">
                            <div style="display: inline-block;">
                                <span style="font-size: 14px;">Testing wifi</span>
                                <p style="font-size: 12px; color:#000;">
                                    I'm a paragraph. Click here to add your own text and edit me. It's easy.
                                </p>
                            </div>
                        </div>
                        <div style="display: inline-block; margin: 10px 20px 10px 0; border-radius: 10px; width: 45%;">
                            <img src="http://127.0.0.1:8000/website/amenity-options/3P8S5N52f3NdTw8E7RwKb3SVFUx7MafFmAC4s83K.png"
                                style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 30px; width: 30px;">
                            <div style="display: inline-block;">
                                <span style="font-size: 14px;">Testing wifi</span>
                                <p style="font-size: 12px; color:#000;">
                                    I'm a paragraph. Click here to add your own text and edit me. It's easy.
                                </p>
                            </div>
                        </div>
                        <div style="display: inline-block; margin: 10px 20px 10px 0; border-radius: 10px; width: 45%;">
                            <img src="http://127.0.0.1:8000/website/amenity-options/3P8S5N52f3NdTw8E7RwKb3SVFUx7MafFmAC4s83K.png"
                                style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 30px; width: 30px;">
                            <div style="display: inline-block;">
                                <span style="font-size: 14px;">Testing wifi</span>
                                <p style="font-size: 12px; color:#000;">
                                    I'm a paragraph. Click here to add your own text and edit me. It's easy.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB;">
                <div class="amenities-box">
                    <h3 style="font-size: 22px; margin-top: 0;">Key Features</h3>
                    <div style="padding: 10px 0;">
                        <div
                            style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                            <div style="display: inline-block;">
                                <span style="font-size: 14px;">Key 1</span>
                                <p style="font-size: 12px; color:#000;">
                                    I'm a paragraph. Click here to add your own text and edit me. It's easy.
                                </p>
                            </div>
                        </div>
                        <div
                            style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                            <div style="display: inline-block;">
                                <span style="font-size: 14px;">Key 2</span>
                                <p style="font-size: 12px; color:#000;">
                                    I'm a paragraph. Click here to add your own text and edit me. It's easy.
                                </p>
                            </div>
                        </div>
                        <div
                            style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                            <div style="display: inline-block;">
                                <span style="font-size: 14px;">Key 3</span>
                                <p style="font-size: 12px; color:#000;">
                                    I'm a paragraph. Click here to add your own text and edit me. It's easy.
                                </p>
                            </div>
                        </div>
                        <div
                            style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                            <div style="display: inline-block;">
                                <span style="font-size: 14px;">Key 4</span>
                                <p style="font-size: 12px; color:#000;">
                                    I'm a paragraph. Click here to add your own text and edit me. It's easy.
                                </p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB;">
                <div class="amenities-box">
                    <h3 style="font-size: 22px; margin-top: 0;">Rules</h3>
                    <div style="padding: 10px 0;">
                        <div
                            style="display: inline-block; margin: 5px 0 5px 0; border-radius: 12px; padding: 12px 20px; border: 2px solid #DBDBDB;">
                            <div style="display: inline-block;">
                                <img src="https://luxustars.com/website/images/square-check.svg"
                                    style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;">
                                <span style="font-size: 14px;">Rule 1</span>
                            </div>
                        </div>
                        <div
                            style="display: inline-block; margin: 5px 0 5px 0; border-radius: 12px; padding: 12px 20px; border: 2px solid #DBDBDB;">
                            <div style="display: inline-block;">
                                <img src="https://luxustars.com/website/images/square-check.svg"
                                    style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;">
                                <span style="font-size: 14px;">Rule 2</span>
                            </div>
                        </div>
                    </div>
                    <div style="padding: 10px 0;">
                        <div
                            style="display: inline-block; margin: 5px 0 5px 0; border-radius: 12px; padding: 12px 20px; border: 2px solid #DBDBDB;">
                            <div style="display: inline-block;">
                                <img src="https://luxustars.com/website/images/ticksquare.svg"
                                    style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;">
                                <span style="font-size: 14px;">Rule 3</span>
                            </div>
                        </div>
                        <div
                            style="display: inline-block; margin: 5px 0 5px 0; border-radius: 12px; padding: 12px 20px; border: 2px solid #DBDBDB;">
                            <div style="display: inline-block;">
                                <img src="https://luxustars.com/website/images/ticksquare.svg"
                                    style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;">
                                <span style="font-size: 14px;">Rule 4</span>
                            </div>
                        </div>
                        <div
                            style="display: inline-block; margin: 5px 0 5px 0; border-radius: 12px; padding: 12px 20px; border: 2px solid #DBDBDB;">
                            <div style="display: inline-block;">
                                <img src="https://luxustars.com/website/images/ticksquare.svg"
                                    style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;">
                                <span style="font-size: 14px;">Rule 5</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB;">
                <div class="amenities-box">
                    <h3 style="font-size: 22px; margin-top: 0;">Things that you need to know! </h3>
                    <ol style="padding-top: 10px;">
                        <li style="padding-bottom: 10px; font-size: 14px;">
                            <span>E.g., There’s a grocery store just a 5-minute walk from the property. 4</span>
                        </li>
                        <li style="padding-bottom: 10px; font-size: 14px;">
                            <span>E.g., There’s a grocery store just a 5-minute walk from the property. 3</span>
                        </li>
                        <li style="padding-bottom: 10px; font-size: 14px;">
                            <span>E.g., There’s a grocery store just a 5-minute walk from the property. 2</span>
                        </li>
                        <li style="padding-bottom: 10px; font-size: 14px;">
                            <span>E.g., There’s a grocery store just a 5-minute walk from the property.</span>
                        </li>
                    </ol>
                </div>
            </div> --}}

            {{-- --------------------- Tour detail 1 --------------------- --}}
            @includeWhen(
                $listing->category->id == 1,
                'website.template.listing-detail.tour',
                compact('listing'))
            {{-- --------------------- Tour detail 1 end --------------------- --}}
            {{-- --------------------- boat detail 2 --------------------- --}}
            @if ($listing->category->id == 2)
                <div style="border-bottom: 2px solid #DBDBDB; padding-top: 25px; page-break-after: auto;">
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li
                            style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                            {{ $listing->detail->capacity ?? '0' }} {{ translate('booking_pdf.passengers') }}
                        </li>
                        @if ($listing->detail->boat_length ?? 0 == 0)
                            <li
                                style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                                {{ $listing->detail->boat_length ?? '0' }} {{ translate('booking_pdf.ft') }}
                            </li>
                        @endif
                        @if ($listing->detail->basis_type == 'Daily')
                            @isset($listing->detail->check_in_time)
                                <li
                                    style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                                    {{ translate('booking_pdf.starts_at') }} {{ $listing->detail->check_in_time ?? '- -' }}
                                </li>
                            @endisset
                            @isset($listing->detail->check_out_time)
                                <li
                                    style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                                    {{ translate('booking_pdf.ends_at') }} {{ $listing->detail->check_out_time ?? '- -' }}
                                </li>
                            @endisset
                        @endif
                        @isset($listing->activity)
                            @foreach ($listing->activity as $activity)
                                <li
                                    style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                                    {{ $activity->name }}
                                </li>
                            @endforeach
                        @endisset
                    </ul>
                </div>

                {{-- ================================================== --}}
                @if (isset($listing->amenity_detail[0]))
                    <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                        <div class="amenities-box">
                            <h3 style="font-size: 22px; margin-top: 0;">{{ translate('booking_pdf.everything_included') }}</h3>
                            <div style="padding: 20px 0 0;">
                                @foreach ($listing->amenity_detail ?? [] as $amenities)
                                    <div
                                        style="display: inline-block; margin: 10px 20px 10px 0; border-radius: 10px; width: 45%;">
                                        <img src="{{ asset('website') . '/' . $amenities->image }}"
                                            style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 30px; width: 30px;">
                                        <div style="display: inline-block;">
                                            <span style="font-size: 14px;">{{ ucfirst($amenities->name) ?? '' }}</span>
                                            <p style="font-size: 12px; color:#000;">
                                                {{ $amenities->description ?? '' }}
                                            </p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                {{-- ================================================== --}}
                {{-- ================================================== --}}
                @if (isset($listing->key_features[0]))
                    <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                        <div class="amenities-box">
                            <h3 style="font-size: 22px; margin-top: 0; padding-bottom: 10px;">{{ translate('booking_pdf.key_features') }}</h3>
                            <div style="padding: 10px 0 0;">
                                @foreach ($listing->key_features as $key_feature)
                                    <div
                                        style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                                        <div style="display: inline-block;">
                                            <span style="font-size: 14px;">{{ $key_feature->title }}</span>
                                            <p style="font-size: 12px; color:#000;">
                                                {!! $key_feature->description !!}
                                            </p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                {{-- ================================================== --}}
                {{-- ================================================== --}}
                @if (isset($listing->includes[0]))
                    <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                        <div class="amenities-box">
                            <h3 style="font-size: 22px; margin-top: 0;">{{ translate('booking_pdf.whats_included') }}</h3>
                            <div style="padding: 10px 0;">
                                @foreach ($listing->includes as $include)
                                    <div
                                        style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                                        <div style="display: inline-block;">
                                            <img src="{{ asset('website/images/square-check.svg') }}"
                                                style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;"
                                                alt="">
                                            <span style="font-size: 14px;">
                                                {{ $include->name }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                {{-- ================================================== --}}
                {{-- ================================================== --}}
                @if (isset($listing->not_includes[0]))
                    <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                        <div class="amenities-box">
                            <h3 style="font-size: 22px; margin-top: 0;">{{ translate('booking_pdf.not_included') }}</h3>
                            <div style="padding: 10px 0;">
                                @foreach ($listing->not_includes as $not_include)
                                    <div
                                        style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                                        <div style="display: inline-block;">
                                            <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;"
                                                alt="">
                                            <span style="font-size: 14px;">
                                                {{ $not_include->name }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                {{-- ================================================== --}}
            @endif
            {{-- --------------------- boat detail 2 end --------------------- --}}
            {{-- --------------------- car detail 3 --------------------- --}}
            @if ($listing->category_id == 3)
                <div style="padding-top: 25px; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li
                            style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                            <img src="{{ asset('website/images/car-front.svg') }}"
                                style="vertical-align:bottom; margin-right: 5px; height: 20px; width: 20px;">
                            {{ $listing->detail->seats }} {{ translate('booking_pdf.seats') }}
                        </li>
                        <li
                            style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                            <img src="{{ asset('website/images/car-transmission.png') }}"
                                style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                            {{ $listing->detail->transmission ?? '-' }}
                        </li>

                        <li
                            style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                            <img src="{{ asset('website/images/car-engine.png') }}"
                                style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                            {{ $listing->detail->engine_type ?? '-' }}
                        </li>
                    </ul>
                </div>
                {{-- ================================================== --}}
                @if (isset($listing->key_features[0]))
                    <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                        <div class="amenities-box">
                            <h3 style="font-size: 22px; margin-top: 0; padding-bottom: 10px;">{{ translate('booking_pdf.key_features') }}</h3>
                            <div style="padding: 10px 0 0;">
                                @foreach ($listing->key_features as $key_feature)
                                    <div
                                        style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                                        <div style="display: inline-block;">
                                            <span style="font-size: 14px;">{{ $key_feature->title }}</span>
                                            <p style="font-size: 12px; color:#000;">
                                                {!! $key_feature->description !!}
                                            </p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                {{-- ================================================== --}}
                {{-- ================================================== --}}
                @if (isset($listing->includes[0]))
                    <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                        <div class="amenities-box">
                            <h3 style="font-size: 22px; margin-top: 0;">{{ translate('booking_pdf.whats_included') }}</h3>
                            <div style="padding: 10px 0;">
                                @foreach ($listing->includes as $include)
                                    <div
                                        style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                                        <div style="display: inline-block;">
                                            <img src="{{ asset('website/images/square-check.svg') }}"
                                                style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;"
                                                alt="">
                                            <span style="font-size: 14px;">
                                                {{ $include->name }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                {{-- ================================================== --}}
                {{-- ================================================== --}}
                @if (isset($listing->not_includes[0]))
                    <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                        <div class="amenities-box">
                            <h3 style="font-size: 22px; margin-top: 0;">{{ translate('booking_pdf.not_included') }}</h3>
                            <div style="padding: 10px 0;">
                                @foreach ($listing->not_includes as $not_include)
                                    <div
                                        style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB;">
                                        <div style="display: inline-block;">
                                            <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;"
                                                alt="">
                                            <span style="font-size: 14px;">
                                                {{ $not_include->name }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                {{-- ================================================== --}}
            @endif
            {{-- --------------------- car detail 3 end --------------------- --}}
            {{-- --------------------- house detail --------------------- --}}
            @if ($listing->category->id == 4)
                <div style="padding-top: 25px; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li
                            style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                            <img src="{{ asset('website/images/user.svg') }}"
                                style="vertical-align:bottom; margin-right: 5px; height: 20px; width: 20px;">
                            {{ $listing->detail->guests ?? '0' }} {{ translate('booking_pdf.guests') }} 
                        </li>
                        <li
                            style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                            <img src="{{ asset('website/images/bedroom.svg') }}"
                                style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                            {{ $listing->detail->bedrooms ?? '0' }} {{ translate('booking_pdf.bedrooms') }}
                        </li>
                        <li
                            style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                            <img src="{{ asset('website/images/bath.svg') }}"
                                style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                            {{ $listing->detail->bathrooms ?? '0' }} {{ translate('booking_pdf.bathrooms') }}
                        </li>
                        <li
                            style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                            <img src="{{ asset('website/images/check-in.png') }}"
                                style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                            {{ translate('booking_pdf.check_in_after') }} {{ $listing->detail->check_in_time ?? '0' }}
                        </li>
                        <li
                            style="display: inline-block; border: 1px solid #aaaaaa; margin: 10px 10px 10px 0; font-size: 12px; padding: 12px 20px; border-radius: 10px;">
                            <img src="{{ asset('website/images/check-out.png') }}"
                                style="vertical-align:bottom; margin-right: 5px;  height: 20px; width: 20px;">
                            {{ translate('booking_pdf.check_out_after') }} {{ $listing->detail->check_out_time ?? '0' }}
                        </li>
                    </ul>
                </div>
                {{-- ================================================== --}}
                @if (isset($listing->amenity_detail[0]))
                    <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                        <div class="amenities-box">
                            <h3 style="font-size: 22px; margin-top: 0;"></h3>
                            <div style="padding: 20px 0 0;">
                                @foreach ($listing->amenity_detail ?? [] as $amenities)
                                    <div
                                        style="display: inline-block; margin: 10px 20px 10px 0; border-radius: 10px; width: 45%;">
                                        <img src="{{ asset('website') . '/' . $amenities->image }}"
                                            style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 30px; width: 30px;">
                                        <div style="display: inline-block;">
                                            <span
                                                style="font-size: 14px;">{{ ucfirst($amenities->name) ?? '' }}</span>
                                            <p style="font-size: 12px; color:#000;">
                                                {{ $amenities->description ?? '' }}
                                            </p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                {{-- ================================================== --}}
                @if (isset($listing->key_features[0]))
                    <div style="padding: 25px 0; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                        <div class="amenities-box">
                            <h3 style="font-size: 22px; margin-top: 0; padding-bottom: 10px;">{{ translate('booking_pdf.key_features') }}</h3>
                            <div style="padding: 10px 0 0;">
                                @foreach ($listing->key_features as $key_feature)
                                    <div
                                        style="display: inline-block; margin: 5px 0 5px 0; border-radius: 10px; width: 27%; padding: 15px 20px; border: 2px solid #DBDBDB; vertical-align: top;">
                                        <div style="display: inline-block;">
                                            <span style="font-size: 14px;">{{ $key_feature->title }}</span>
                                            <p style="font-size: 12px; color:#000;">
                                                {!! $key_feature->description !!}
                                            </p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
                {{-- ================================================== --}}
            @endif
            {{-- --------------------- house detail end --------------------- --}}
            {{-- ==============  RULE  =================== --}}

            @if (isset($listing->detail->pet) || isset($listing->rules))
                <div style="padding-top: 25px; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                    <div class="amenities-box">
                        <h3 style="font-size: 22px; margin-top: 0;">{{ translate('booking_pdf.rules') }}</h3>
                        <div style="padding: 10px 0;">
                            @if ($listing->detail->pet == 'yes')
                                <div
                                    style="display: inline-block; margin: 5px 0 5px 0; border-radius: 12px; padding: 12px 20px; border: 2px solid #DBDBDB;">
                                    <div style="display: inline-block;">
                                        <img src="{{ asset('website/images/square-check.svg') }}"
                                            style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;">
                                        <span style="font-size: 14px;">{{ translate('booking_pdf.pets') }}</span>
                                    </div>
                                </div>
                            @endif
                            @foreach ($listing->rules as $rule)
                                @if (isset($rule->title) && $rule->allow == 'yes')
                                    <div
                                        style="display: inline-block; margin: 5px 0 5px 0; border-radius: 12px; padding: 12px 20px; border: 2px solid #DBDBDB;">
                                        <div style="display: inline-block;">
                                            <img src="{{ asset('website/images/square-check.svg') }}"
                                                style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;">
                                            <span style="font-size: 14px;">{{ ucwords($rule->title) }}</span>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                        <div style="padding: 10px 0;">
                            @if ($listing->detail->pet == 'no')
                                <div
                                    style="display: inline-block; margin: 5px 0 5px 0; border-radius: 12px; padding: 12px 20px; border: 2px solid #DBDBDB;">
                                    <div style="display: inline-block;">
                                        <img src="{{ asset('website/images/ticksquare.svg') }}"
                                            style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;">
                                        <span style="font-size: 14px;">{{ translate('booking_pdf.pets') }}</span>
                                    </div>
                                </div>
                            @endif
                            @foreach ($listing->rules as $rule)
                                @if (isset($rule->title) && $rule->allow == 'no')
                                    <div
                                        style="display: inline-block; margin: 5px 0 5px 0; border-radius: 12px; padding: 12px 20px; border: 2px solid #DBDBDB;">
                                        <div style="display: inline-block;">
                                            <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                style="display: inline-block; margin-right: 10px; vertical-align: top;  height: 20px; width: 20px;">
                                            <span style="font-size: 14px;">{{ ucwords($rule->title) }}</span>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
            {{-- ==============  RULE END  =================== --}}

            {{-- ==============  NOTE  =================== --}}
            @if (isset($listing->notes) && !$listing->notes->isEmpty())
                <div style="padding-top: 25px; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                    <div class="amenities-box">
                        <h3 style="font-size: 22px; margin-top: 0;">{{ translate('booking_pdf.things_that_you_need_to_know') }} </h3>
                        <ol style="padding-top: 10px;">
                            @foreach ($listing->notes as $note)
                                <li style="padding-bottom: 10px; font-size: 14px;">
                                    <span>{{ $note->name }}</span>
                                </li>
                            @endforeach
                        </ol>
                    </div>
                </div>
            @endif
            {{-- ==============  NOTE END   =================== --}}


            <div style="padding: 25px 0 20px; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                <div style="display: inline-block; width: 100%;">
                    <h4 style="color: #000; font-size: 22px; font-weight: 600; margin-block: 0; line-height: 1.2;">
                        Booking Details</h4>
                    <div style="padding: 25px 0; ">
                        <div style="display: inline-block; width: 48%; vertical-align: top;">
                            <div>
                                <div style="display: inline-block; width: 48%; padding-right: 20px; ">
                                    <h4
                                        style="color: #000; font-size: 18px; font-weight: 600; margin-block: 0; line-height: 1.2;">
                                        {{ $listing->category_id == 3 ? translate('booking_pdf.pick_up') : translate('booking_pdf.check_in') }}
                                    </h4>
                                    <h5
                                        style="color: #000; font-size: 16px; font-weight: 400; margin-block: 3px; line-height: 1.2; color: #626262;">
                                        {{ date(config('constant.date_format'), strtotime($booking->check_in ?? '-')) }}
                                    </h5>
                                </div>
                                <div style="display: inline-block; ">
                                    <h4
                                        style="color: #000; font-size: 18px; font-weight: 600; margin-block: 0; line-height: 1.2;">
                                        {{ $listing->category_id == 3 ? translate('booking_pdf.drop_off') : translate('booking_pdf.check_out') }}</h4>
                                    <h5
                                        style="color: #000; font-size: 16px; font-weight: 400; margin-block: 3px; line-height: 1.2; color: #626262;">
                                        {{ date(config('constant.date_format'), strtotime($booking->check_out ?? '-')) }}
                                    </h5>
                                </div>
                            </div>
                            <div style="padding: 25px 0; ">
                                <div style="display: inline-block;">
                                    <h4
                                        style="color: #000; font-size: 15px; font-weight: 500; margin-block: 8px; line-height: 1.2;">
                                        {{ translate('booking_pdf.booking_name') }}: <span
                                            style="color: #626262; font-weight: 400;">{{ $booking->customer->name }}</span>
                                    </h4>
                                    @if ($booking->guest)
                                        <h4
                                            style="color: #000; font-size: 15px; font-weight: 500; margin-block: 8px; line-height: 1.2;">
                                            {{ translate('booking_pdf.guests') }}: <span
                                                style="color: #626262; font-weight: 400;">{{ $booking->guest }}</span>
                                        </h4>
                                    @endif
                                    @if ($booking->listing_basis == 'Hourly')
                                        <div class="checkin">
                                            <h6 class="fs-16 light-bold">{{ translate('booking_pdf.hour_slots') }}</h6>
                                            @forelse ($booking->hourly_slots as $hourly_slot)
                                                <p class="fs-14 text-black-50">{{ $hourly_slot->slot }}</p>
                                            @empty
                                            @endforelse
                                        </div>
                                    @endif
                                    <h4
                                        style="color: #000; font-size: 15px; font-weight: 500; margin-block: 8px; line-height: 1.5;">
                                        {{ translate('booking_pdf.booking_address') }}: <span
                                            style="color: #626262; font-weight: 400;">{{ $listing->address->address }}</span>
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div style="display: inline-block; width: 48%;  vertical-align: top;">
                            <h4
                                style="color: #000; font-size: 22px; font-weight: 600; margin-block: 0; line-height: 1.2;">
                                {{ translate('booking_pdf.pricing_details') }}</h4>
                            @php
                                $currency = $booking->currency;
                                $conversion_rate = $booking->conversion_rate;
                            @endphp
                            <table style="padding: 25px 0; width: 100%;">
                                <tbody>
                                    <tr>
                                        <td style="vertical-align: top;">
                                            {{ translate('booking_pdf.listing_price') }}
                                        </td>
                                        <td style="vertical-align: top; text-align: right;">
                                            {{ $currency . ' ' . number_format($booking->listing_price * $conversion_rate, 0) }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="vertical-align: top; text-align: left;">
                                            {{ translate('booking_pdf.basis_type') }}
                                        </td>
                                        <td style="vertical-align: top; text-align: right;">
                                            {{ $booking->listing_basis == 'Days' ? 'Night' : $booking->listing_basis }}
                                            </th>
                                    </tr>
                                    @if ($booking->listing_basis == 'Hourly')
                                        <tr>
                                            <td style="vertical-align: top; text-align: left;">
                                                {{ translate('booking_pdf.total_hours') }}(s)
                                            </td>
                                            <td style="vertical-align: top; text-align: right;">
                                                {{ $booking->detail->total_hours }}
                                            </td>
                                        </tr>
                                    @elseif ($booking->listing_basis == 'Daily')
                                        <tr>
                                            <td style="vertical-align: top; text-align: left;">
                                                {{ translate('booking_pdf.total') }} {{ $listing->category_id == 4 ? 'Night' : 'Day' }}(s)
                                            </td>
                                            <td style="vertical-align: top; text-align: right;">
                                                {{ $booking->detail->total_days }}
                                            </td>
                                        </tr>
                                    @endif
                                    <tr>
                                        <td style="vertical-align: top; text-align: left;">
                                            {{ translate('booking_pdf.total_price') }}
                                        </td>
                                        <td style="vertical-align: top; text-align: right;">
                                            {{ $currency . ' ' . number_format($booking->total_amount * $conversion_rate, 0) }}
                                        </td>
                                    </tr>
                                    @forelse ($booking->discounts as $discount)
                                        <tr>
                                            <td style="vertical-align: top; text-align: left;">
                                                {{ $discount->type }} ({{ $discount->percent }}%)
                                            </td>
                                            <td style="vertical-align: top; text-align: right;color:green">
                                                -
                                                {{ $currency . ' ' . number_format($discount->amount * $conversion_rate, 0) }}
                                            </td>
                                        </tr>
                                    @empty
                                    @endforelse
                                    <tr>
                                        <td
                                            style="vertical-align: top; text-align: left; border: solid #8D8D8D; border-width: 1px 0 1px; padding: 10px 0;">
                                            {{ translate('booking_pdf.sub_total') }} ({{ $currency }})
                                        </td>
                                        <td
                                            style="vertical-align: top; text-align: right; border: solid #8D8D8D; border-width: 1px 0 1px; padding: 10px 0;">
                                            {{ $currency . ' ' . number_format($booking->sub_total * $conversion_rate, 0) }}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="vertical-align: top; text-align: left;">
                                            {{ translate('booking_pdf.payment_will_be_proceeded_in_usd') }}
                                        </td>
                                        <td style="vertical-align: top; text-align: right;">USD
                                            {{ $booking->total_usd_amount }}</td>
                                    </tr>
                                    <tr>
                                        <td style="vertical-align: top; text-align: left;">
                                           {{ translate('booking_pdf.booking_status') }}
                                        </td>
                                        <td style="vertical-align: top; text-align: right;">
                                            <span>{{ translate('booking_pdf.booking_status') }}</span>
                                            @if ($booking->status == 0)
                                                <span class="bold flex-shrink-0 text-success">{{ translate('booking_pdf.confirmed') }}</span>
                                            @elseif($booking->status == 7)
                                                <span class="bold flex-shrink-0 text-danger">
                                                    {{ translate('booking_pdf.cancelled') }}
                                                </span>
                                            @elseif($booking->status == 3)
                                                <span class="bold flex-shrink-0 text-primary">{{ translate('booking_pdf.completed') }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="vertical-align: top; text-align: left;">
                                            {{ translate('booking_pdf.payment_status') }}
                                        </td>
                                        <td style="vertical-align: top; text-align: right;">
                                            @if ($booking->status == 0)
                                                <span class="bold flex-shrink-0 text-success">{{ translate('booking_pdf.paid') }}</span>
                                            @elseif($booking->status == 7)
                                                <span class="bold flex-shrink-0 text-danger">
                                                    {{-- Cancelled --}}
                                                    @if ($booking->refund_percentage == 100)
                                                        {{ translate('booking_pdf.fully_refunded') }}
                                                    @elseif($booking->refund_percentage < 100 && $booking->refund_percentage > 0)
                                                        {{ translate('booking_pdf.partially_refunded') }}
                                                    @else
                                                        {{ translate('booking_pdf.no_refund') }}
                                                    @endif
                                                </span>
                                            @elseif($booking->status == 3)
                                                <span class="bold flex-shrink-0 text-primary">{{ translate('booking_pdf.completed') }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="vertical-align: top; text-align: left;">
                                            {{ translate('booking_pdf.payment_method') }}
                                        </td>
                                        <td style="vertical-align: top; text-align: right;">
                                            @if ($booking->payment_method != 'Card' || $booking->payment_method != 'card')
                                                {{ ucwords($booking->payment_method) }}
                                            @else
                                                {{ $booking->card_brand ?? '' }} - {{ $booking->last_4_digits ?? '' }}
                                            @endif
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div style="padding: 25px 0 35px; border-bottom: 2px solid #DBDBDB; page-break-after: auto;">
                <div style="display: inline-block; width: 100%;">
                    <h3 style="font-size: 22px; margin-block: 0; line-height: 1.2;">
                        {{ translate('booking_pdf.luxustars_secure_payment_assurance') }}
                    </h3>
                    <div class="payment_content">
                        <p>💫 <i>{{ translate('booking_pdf.your_peace_of_mind_our_priority') }}</i> 💫</p>
                        <p>{{ translate('booking_pdf.at_luxustars_all_payments_are_securely_processed') }}</p>
                        <ul>
                            <li>
                                <p>
                                    <b>{{ translate('booking_pdf.24_7_support') }}</b>
                                    {{ translate('booking_pdf.assistance_whenever_you_need_it') }}
                                </p>
                            </li>
                            <li>
                                <p>
                                    <b>{{ translate('booking_pdf.payment_protection') }}:</b>
                                    {{ translate('booking_pdf.your_funds_are_held_safely_until_the_booking_is_completed') }}
                                </p>
                            </li>
                            <li>
                                <p>
                                    <b>{{ translate('booking_pdf.cancellation_coverage') }}:</b>
                                    {{ translate('booking_pdf.enjoy_policies_tailored_to_your_needs') }}
                                </p>
                            </li>
                        </ul>
                        <p>✨ <b>{{ translate('booking_pdf.experience_luxury_with_confidence') }}</b></p>
                    </div>
                </div>
            </div>
            <!-- Footer Section -->
            <div style="padding: 15px 0; text-align: center;">
                <div style="padding: 10px 0; display: inline-block; position: relative; top: 10px; ">
                    <img src="https://luxustars.com/AdminDashboard/R0cjgq5Gnpb8JBuKcHiibOonMM8iXkPYcTFmDT2g.png"
                        alt="LuxuStars" style="height:60px; width: 300px; object-fit: contain;">
                </div>
                <span
                    style="border-top: 2px solid #FFBD19; height: 6px; color: #fff; display: inline-block; width: 80%; margin: 10px 15px;"></span>
                {{-- <div style="display: inline-block; position: relative; top: -10px; padding: 10px 0;">
                    <a href="https://luxustars.com"
                        style="border: 2px solid #FFBD19; border-radius:50%; margin: 0 8px;  padding-top: 6px; display: inline-block; height: 31px;  width: 35px;  text-align: center;"><i
                            class="fab fa-facebook-f" style="color: #FFBD19;"></i></a>
                    <a href="https://luxustars.com"
                        style="border: 2px solid #FFBD19; border-radius:50%; margin: 0 8px;  padding-top: 6px; display: inline-block; height: 31px;  width: 35px;  text-align: center;"><i
                            class="fab fa-twitter" style="color: #FFBD19;"></i></a>
                    <a href="https://luxustars.com"
                        style="border: 2px solid #FFBD19; border-radius:50%; margin: 0 8px;  padding-top: 6px; display: inline-block; height: 31px;  width: 35px;  text-align: center;"><i
                            class="fab fa-instagram" style="color: #FFBD19;"></i></a>
                </div> --}}
            </div>
            <div>
                <p style="color: #8D8D8D; text-align: center; text-transform: capitalize;"><i
                        class="far fa-copyright"></i>
                    {{ date('Y') }} {{ translate('booking_pdf.copyright') }}</p>
            </div>
        </div>
    </div>
</body>

</html>
