@extends('layouts.master')
@push('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/18.2.1/css/intlTelInput.css" />
    <style>
        /* Initially hide the Admin Role buttons */
        .adminRoleBtn {
            display: none;
        }

        .head_btn .custom_btns:has(li.active .adminBtn) #add-admin-btn {
            display: block;
        }

        .active>.page-link {
            background-color: #fdd751 !important;
            border-color: #fdd751 !important;
        }
    </style>
@endpush
@section('content')
    {{-- Tab Pane btn --}}
    <section class="head_btn">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <h1>{{ translate('user_management.user_management') }} </h1>
                    <div class="custom_btns">
                        <div class="head_bt">
                            <ul class="nav nav-pills">
                                <li class="active"><a data-toggle="pill" data-target="#customers"
                                        href="#customers">{{ translate('user_management.customers') }} </a></li>
                                <li><a data-toggle="pill" data-target="#service"
                                        href="#service">{{ translate('user_management.service_providers') }}</a></li>
                                @if (auth()->user()->hasRole('user'))
                                    <li><a data-toggle="pill" data-target="#admins" class="adminBtn"
                                            href="#admins">{{ translate('user_management.admins') }}</a></li>
                                @endif
                            </ul>
                        </div>
                        <div class="nav_search main d-flex">
                            <a class="btn_yellow adminRoleBtn" id="add-admin-btn" data-toggle="modal"
                                data-target="#addAdmin">{{ translate('user_management.add_admin') }}</a>
                            <!-- Actual search box -->
                            <div class="d-inline-block form-group has-feedback has-search">
                                <form class="example" action="/action_page.php" style="width: 100%;">
                                    <button type="button"><i class="fa fa-search"></i></button>
                                    <input type="text" placeholder="Search.." id="searchBar" name="search2" />
                                    {{-- <i class="fa-solid fa-filter" style="color: #000000;"></i> --}}
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {{-- Tab Pane btn end --}}

    {{-- tab pane body --}}
    <div class="tab-content">
        {{-- user --}}
        <div id="customers" class="tab-pane fade in active">
            <section class="table_sect">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="tabel_hdng_icon">
                                <h1>{{ translate('user_management.customers') }}</h1>
                                <!-- <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i> -->
                            </div>
                            <div class="table_wrraper">
                                <div class="table-responsive">
                                    <table id="myTable" class="table custom-table">
                                        <thead>
                                            <tr>
                                                <th scope="col">{{ translate('user_management.profile_name') }}</th>
                                                <th scope="col">{{ translate('user_management.email') }}</th>
                                                <th scope="col">{{ translate('user_management.phone_number') }}</th>
                                                <th scope="col">{{ translate('user_management.total_bookings') }}</th>
                                                <th scope="col">{{ translate('user_management.total_expenditure') }}</th>
                                                <th scope="col">{{ translate('user_management.last_active') }}</th>
                                                <th scope="col">{{ translate('user_management.ratings') }}</th>
                                                <th scope="col">{{ translate('user_management.status') }}</th>
                                                <th scope="col">{{ translate('user_management.action') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($customers as $customer)
                                                @if ($customer->hasRole('customer'))
                                                    <tr>
                                                        <td>
                                                            <div class="user_profile">
                                                                <a
                                                                    href="{{ route('customer_details', $customer->ids) }}">
                                                                    <img src="{{ asset('website') . '/' . $customer->avatar }}"
                                                                        width="50" style="border-radius:50%"
                                                                        alt="">
                                                                    {{ $customer->name }}
                                                                </a>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <p class="email">{{ $customer->email }}</p>
                                                        </td>
                                                        <td>{{ $customer->phone ?? '-' }}</td>
                                                        <td>{{ count($customer->customer_bookings) }}</td>
                                                        <td>${{ $customer->total_spent ?? 0 }}</td>
                                                        <td>{{ $customer->last_active_at ? $customer->last_active : 'Not Logged In' }}
                                                        </td>
                                                        <td>{{ count($customer->customer_rating) }}</td>
                                                        <td>
                                                            @if ($customer->status == 1)
                                                                <span class="text-success">{{ translate('user_management.approved') }}</span>
                                                            @elseif ($customer->status == 5)
                                                                <span class="text-danger">{{ translate('user_management.suspended') }}</span>
                                                            @endif
                                                        </td>
                                                        <td class="form_btn ">
                                                            <div class="dropdown">
                                                                <button class=" dropdown-toggle" type="button"
                                                                    id="dropdownMenuButton" data-toggle="dropdown"
                                                                    aria-haspopup="true" aria-expanded="false">
                                                                    <i class="fa-solid fa-ellipsis"
                                                                        style="color: #a0aec0;"></i>
                                                                </button>
                                                                <div class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton">
                                                                    <a class="dropdown-item"
                                                                        href="{{ route('customer_details', $customer->ids) }}"
                                                                        data-target="#">{{ trans('view') }}</a>
                                                                    <a class="dropdown-item edit_btn"
                                                                        data-user-id="{{ $customer->id }}"
                                                                        data-toggle="modal"
                                                                        data-target="#userEdit">{{ trans('edit') }}</a>
                                                                    @if ($customer->status == 5)
                                                                        <a class="dropdown-item  unsuspend-btn"
                                                                            data-type="customer"
                                                                            data-user-id="{{ encrypt($customer->id) }}">{{ translate('user_management.unsuspend') }}</a>
                                                                    @else
                                                                        <a class="dropdown-item text-danger customer_active_bookings"
                                                                            data-toggle="modal"
                                                                            data-target="#customer_suspend"
                                                                            data-user-id="{{ encrypt($customer->id) }}">{{ translate('user_management.suspend') }}</a>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endif
                                            @endforeach
                                        </tbody>
                                    </table>
                                    {{ $customers->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        {{-- user end --}}

        {{-- service --}}
        <div id="service" class="tab-pane fade">
            <section class="table_sect">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="tabel_hdng_icon">
                                <h1>{{ translate('user_management.service_providers') }}</h1>
                                <!-- <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i> -->
                            </div>
                            <div class="table_wrraper">
                                <div class="table-responsive">
                                    <table id="service_table" class="table  custom-table">
                                        <thead>
                                            <tr><th scope="col">{{ translate('user_management.profile_name') }}</th>
                                                <th scope="col">{{ translate('user_management.email') }}</th>
                                                {{-- <th scope="col">{{ translate('user_management.phone_number') }}</th> --}}
                                                <th scope="col">{{ translate('user_management.total_listings') }}</th>
                                                <th scope="col">{{ translate('user_management.total_bookings') }}</th>
                                                <th scope="col">{{ translate('user_management.revenue_generated') }}</th>
                                                <th scope="col">{{ translate('user_management.last_active') }}</th>
                                                <th scope="col">{{ translate('user_management.ratings') }}</th>
                                                <th scope="col">{{ translate('user_management.status') }}</th>
                                                <th scope="col">{{ translate('user_management.action') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($service_users as $service_provider)
                                                @if ($service_provider->hasRole('service'))
                                                    <tr>
                                                        <td>
                                                            <div class="user_profile">
                                                                <a
                                                                    href="{{ route('service_provider', $service_provider->ids) }}">
                                                                    <img src="{{ asset('website') }}/{{ $service_provider->avatar }}"
                                                                        width="50" style="border-radius:50%"
                                                                        alt="">
                                                                    {{ $service_provider->name }}
                                                                </a>
                                                            </div>

                                                        </td>
                                                        <td>{{ $service_provider->email }}</td>
                                                        {{-- <td>{{ $service_provider->phone }}</td> --}}
                                                        <td>{{ count($service_provider->listings) }}</td>
                                                        <td>{{ count($service_provider->provider_bookings) }}</td>
                                                        <td>${{ $service_provider->total_revenue }}</td>
                                                        <td>{{ $service_provider->last_active }}</td>
                                                        <td>{{ count($service_provider->provider_rating) }}</td>
                                                        <td>
                                                            @if ($service_provider->status == 1)
                                                                <span class="text-success">{{ trans('approved') }}</span>
                                                            @elseif ($service_provider->status == 5)
                                                                <span class="text-danger">{{ trans('suspended') }}</span>
                                                            @endif
                                                        </td>
                                                        <td class="form_btn ">
                                                            <div class="dropdown">
                                                                <button class=" dropdown-toggle" type="button"
                                                                    id="dropdownMenuButton" data-toggle="dropdown"
                                                                    aria-haspopup="true" aria-expanded="false">
                                                                    <i class="fa-solid fa-ellipsis"
                                                                        style="color: #a0aec0;"></i>
                                                                </button>
                                                                <div class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton">
                                                                    <a class="dropdown-item"
                                                                        href="{{ route('service_provider', $service_provider->ids) }}"
                                                                        data-target="#">{{ translate('user_management.view') }}</a>
                                                                    <a class="dropdown-item edit_btn"
                                                                        data-user-id="{{ $service_provider->id }}"
                                                                        data-toggle="modal"
                                                                        data-target="#userEdit">{{ translate('user_management.edit') }}</a>
                                                                    @if ($service_provider->status == 5)
                                                                        <a class="dropdown-item unsuspend-btn"
                                                                            data-type="service-provider"
                                                                            data-user-id="{{ encrypt($service_provider->id) }}">{{ translate('user_management.unsuspend') }}</a>
                                                                    @else
                                                                        <a class="dropdown-item text-danger service_provider_active_bookings"
                                                                            data-target="#service_provider_modal"
                                                                            data-toggle="modal"
                                                                            data-user-id="{{ encrypt($service_provider->id) }}">{{ translate('user_management.suspend') }}</a>
                                                                    @endif
                                                                    @if ($service_provider->hold_payment == 0)
                                                                    <a href="{{ url('service-provider-payment'.'/'.$service_provider->ids.'/'.'1') }}" class="dropdown-item unsuspend-btn"
                                                                       >{{ translate('user_management.active_payment') }}</a>
                                                                @else
                                                                    <a href="{{ url('service-provider-payment'.'/'.$service_provider->ids.'/'.'0') }}" class="dropdown-item text-danger service_provider_active_bookings">{{ translate('user_management.hold_payment') }}</a>
                                                                @endif
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endif
                                            @endforeach
                                        </tbody>
                                    </table>
                                    {{ $service_users->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        {{-- service end --}}

        {{-- Admin --}}
        @if (auth()->user()->hasRole('user'))
            <div id="admins" class="tab-pane fade">
                <section class="table_sect">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="tabel_hdng_icon">
                                    <h1>{{ trans('admins') }}</h1>
                                    <!-- <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i> -->
                                </div>
                                <div class="table_wrraper">
                                    <div class="table-responsive">
                                        <table id="service_table" class="table  custom-table">
                                            <thead>
                                                <tr>
                                                    <th scope="col">{{ translate('user_management.profile_name') }}</th>
                                                    <th scope="col">{{ translate('user_management.email') }}</th>
                                                    <th scope="col">{{ translate('user_management.role') }}</th>
                                                    <th scope="col">{{ translate('user_management.last_active') }}</th>
                                                    <th scope="col">{{ translate('user_management.action') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($sub_admins as $admin)
                                                    @if ($admin->hasRole(['user', 'sub_admin']))
                                                        <tr>
                                                            <td>
                                                                <div class="user_profile">
                                                                    <a
                                                                        href="{{ route('admin_details', $admin->ids) }}">
                                                                        <img src="{{ asset('website') }}/{{ $admin->avatar }}"
                                                                            width="50" style="border-radius:50%"
                                                                            alt="">
                                                                        {{ $admin->name }}
                                                                    </a>
                                                                </div>
                                                            </td>
                                                            <td>{{ $admin->email }}</td>
                                                            <td>
                                                                @if ($admin->hasRole('user'))
                                                                    {{ trans('super_admin') }}
                                                                @elseif ($admin->hasRole('sub_admin'))
                                                                    {{ trans('sub_admin') }}
                                                                @endif
                                                            </td>
                                                            <td>{{ $admin->last_active }}</td>
                                                            <td class="form_btn ">
                                                                <div class="dropdown">
                                                                    <button class=" dropdown-toggle" type="button"
                                                                        id="dropdownMenuButton" data-toggle="dropdown"
                                                                        aria-haspopup="true" aria-expanded="false">
                                                                        <i class="fa-solid fa-ellipsis"
                                                                            style="color: #a0aec0;"></i>
                                                                    </button>
                                                                    <div class="dropdown-menu"
                                                                        aria-labelledby="dropdownMenuButton">
                                                                        <a class="dropdown-item"
                                                                            href="{{ route('admin_details', $admin->ids) }}"
                                                                            data-target="#">
                                                                            {{ translate('user_management.view') }}
                                                                        </a>
                                                                        <a class="dropdown-item admin-edit-btn"
                                                                            data-admin-id="{{ $admin->id }}"
                                                                            data-toggle="modal" data-target="#addAdmin">
                                                                            {{ translate('user_management.edit') }}
                                                                        </a>
                                                                        @if ($admin->id != 2)
                                                                            <a class="dropdown-item text-danger admin-delete-btn"
                                                                                data-admin-id="{{ $admin->id }}">
                                                                                {{ translate('user_management.delete') }}
                                                                            </a>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    @endif
                                                @endforeach
                                            </tbody>
                                        </table>
                                        {{ $sub_admins->links() }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        @endif
    </div>
    <section class="amen reject">
        <div class="modal fade" id="userEdit" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
            <div class="modal-dialog" id="amenModal" role="document">
                <div class="modal-content">
                    <div class="modal-body">

                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="title">{{ trans('edit_user') }}</h1>
                        <div id="sign-up-error" style="display: none" class="alert alert-danger text-start "
                            role="alert">
                            <ul>
                            </ul>
                        </div>
                        <div class="form_field_padding">
                            <div class="mb-3 mod_cust_text">
                                <form id="user_update_modal">
                                    <div class="form_field_padding">
                                        @csrf
                                        <input type="hidden" name="id" id="user_id">
                                        <div class="mb-3">
                                            <label for="first_name">{{ trans('first_name') }}</label>
                                            <input class="form-control" name="first_name" id="first_name"
                                                placeholder="First Name" />
                                        </div>
                                        <div class="mb-3">
                                            <label for="last_name">{{ trans('last_name') }}</label>
                                            <input class="form-control" name="last_name" id="last_name"
                                                placeholder="Last Name" />
                                        </div>
                                        <div class="mb-3">
                                            <label for="user_email">{{ trans('email') }}</label>
                                            <input class="form-control" name="email" id="user_email"
                                                placeholder="Email" disabled />
                                        </div>
                                        <div class="mb-3">
                                            <label for="user_phone">{{ trans('phone') }}</label>
                                            <div class="d-block">
                                                <input type="hidden" class="country_code" name="country_code"
                                                    value="+1">
                                                <input class="form-control" name="phone" id="user_phone"
                                                    placeholder="Phone" />
                                            </div>

                                        </div>
                                        {{-- <div class="mb-3">
                                            <input class="form-control" name="password" id="user_password"
                                                placeholder="Password" />
                                        </div> --}}
                                    </div>
                                    <div class=" modal_btn  ">
                                        <button type="submit" class="btn yellow">{{ trans('update_save') }}</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {{-- Edit Modal end --}}

    {{-- Suspend Modal --}}
    @include('modals.customer-suspend')
    {{-- Suspend Modal end --}}
    {{-- Suspend Modal --}}
    @include('modals.service-provider-suspend')
    {{-- Suspend Modal end --}}

    {{-- Admin Modal --}}
    @include('modals.admin-create-update')
@endsection
@push('js')
    <script src="{{ asset('js/db1.js') }}"></script>
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/18.2.1/js/intlTelInput-jquery.min.js"></script>

    <script>
        $(document).ready(function() {

            // get user detail 
            $(".edit_btn").on("click", function() {
                let user_id = $(this).data("user-id");
                $.ajax({
                    url: `{{ route('user_detail', '') }}/${user_id}`,
                    type: "GET",
                    success: function(response) {
                        if (response.status == true) {
                            $("#user_password").val(response.data.password);
                            $("#user_email").val(response.data.email);
                            $("#first_name").val(response.data.first_name);
                            $("#last_name").val(response.data.last_name);
                            $("#user_phone").val(response.data.phone);
                            $("#user_id").val(response.data.id);
                        }
                    }
                })
            })
            $("#user_update_modal").on("submit", function(e) {
                e.preventDefault();
                let form = $(this).serialize();
                $.ajax({
                    url: "{{ route('admin_user_update') }}",
                    method: "POST",
                    data: form,
                    success: function(response) {
                        if (response.status == true) {
                            Swal.fire(
                                `User Update!`,
                                'success'
                            ).then(function() {
                                location.reload();
                            })
                        } else {
                            $("#sign-up-error").slideDown();
                            $("#sign-up-error ul").empty();
                            $("#sign-up-error ul").append(`<li>${response.message}</li>`);
                            setTimeout(() => {
                                $("#sign-up-error").slideUp();
                            }, 2000);
                        }
                    }
                })
            })
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            // service provider suspend 
            $(".service_provider_active_bookings").on("click", function() {
                let user_id = $(this).data("user-id");
                $(".spinner").show();
                $("#service-provider-suspense-modal-table").empty();
                if (user_id) {
                    $.ajax({
                        url: `{{ route('service_provider_active_bookings', '') }}/${user_id}`,
                        type: "GET",
                        success: function(response) {
                            $(".spinner").hide();
                            $("#service-provider-suspense-modal-table").html(response.data);
                        }
                    })
                }
            })
            // suspend btn 
            $(".customer_active_bookings").on("click", function() {
                let user_id = $(this).data("user-id");
                $(".spinner").show();
                $("#customer-suspense-modal-table").empty();
                if (user_id) {
                    $.ajax({
                        url: `{{ route('customer_active_bookings', '') }}/${user_id}`,
                        type: "GET",
                        success: function(response) {
                            $(".spinner").hide();
                            $("#customer-suspense-modal-table").html(response.data);
                        }
                    })
                }
            })
            $(".unsuspend-btn").on("click", function() {
                let user_id = $(this).data("user-id");
                let type = $(this).data("type");
                if (user_id) {
                    Swal.fire({
                       title: @json(translate('user_management.confirm_action')),
                        text: @json(translate('user_management.unsuspend_user_confirmation')),
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#3085d6",
                        cancelButtonColor: "#d33",
                       confirmButtonText: @json(translate('user_management.yes_unsuspend'))
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $.ajax({
                                url: "{{ route('unsuspend_user') }}",
                                type: "GET",
                                data: {
                                    user_id: user_id,
                                    type: type
                                },
                                success: function(response) {
                                    if (response.status == true) {
                                        Swal.fire(
                                            'User Unsuspended',
                                            `${response.message}`,
                                            'success'
                                        ).then(function() {
                                            location.reload();
                                        })
                                    } else {
                                        Swal.fire(
                                            'Error',
                                            `${response.message}`,
                                            'error'
                                        )
                                    }
                                }
                            })
                        }
                    });
                }
            })
        });
        var $telephone = $("#user_phone");
        $telephone.intlTelInput({
            separateDialCode: true,
        });
        $telephone.on("countrychange", function() {
            var country_code = $(".iti__selected-dial-code").html();
            $(".country_code").val(country_code);
        });

        $(document).ready(function() {
            function showTabByHash() {
                var currentHash = window.location.hash || '#user';
                var $targetTab = $('.nav-pills a[href="' + currentHash + '"]');
                if ($targetTab.length > 0) {
                    $('.nav-pills li').removeClass('active');
                    $('.tab-pane').removeClass('active show in');
                    $targetTab.closest('li').addClass('active');
                    $(currentHash).addClass('active show in');
                    sessionStorage.setItem('activeTab', currentHash);
                    setTimeout(function() {
                        sessionStorage.removeItem('activeTab');
                    }, 3000);
                }
            }
            var activeTab = sessionStorage.getItem('activeTab');
            if (activeTab) {
                window.location.hash = activeTab;
                showTabByHash();
            } else {
                showTabByHash();
            }
            $(window).on('hashchange', showTabByHash);
            $('.nav-pills a').on('click', function(e) {
                e.preventDefault();
                window.location.hash = this.hash;
                showTabByHash();
            });
            $('form').on('submit', function(e) {
                var currentHash = window.location.hash || '#user';
                window.location.hash = currentHash;
                showTabByHash();
            });

            // admin create
            $("#add-admin-btn").on("click", function() {
                $("#admin-modal-title").html(`Add Admin`);
                $("#admin-modal-btn").html(`Create`);
                $("#admin_email").val("");
                $("#admin_email").prop('disabled', false);
                $("#admin_first_name").val("");
                $("#admin_last_name").val("");
                $("#admin_phone").val("");
                $("#admin_id").val("");
                $("#admin-form").attr("action", "{{ route('sub-admin.store') }}");
                $("#admin-form input[name='_method']").prop('disabled', true);
            })

            // admin edit
            $(".admin-edit-btn").on("click", function() {
                let admin_id = $(this).attr("data-admin-id");
                if (admin_id) {
                    $.ajax({
                        url: `{{ route('user_detail', '') }}/${admin_id}`,
                        type: "GET",
                        success: function(response) {
                            if (response.status == true) {
                                $("#admin_email").val(response.data.email);
                                $("#admin_email").prop('disabled', true);
                                $("#admin_first_name").val(response.data.first_name);
                                $("#admin_last_name").val(response.data.last_name);
                                $("#admin_phone").val(response.data.phone);
                                $("#admin_role").val(response.role);
                                $("#admin_id").val(response.data.id);
                                $("#admin-form input[name='_method']").prop('disabled', false);
                                $("#admin-modal-title").html(`Edit Admin`);
                                $("#admin-modal-btn").html(`Update`);
                                $("#admin-form").attr("action",
                                    `{{ route('sub-admin.update', '') }}/${response.data.id}`
                                );
                            }
                        }
                    })
                }
            })
            // admin delete
            $(".admin-delete-btn").on("click", function() {
                let admin_id = $(this).attr("data-admin-id");
                if (admin_id) {
                    Swal.fire({
                        title: @json(translate('user_management.confirm_delete_admin'))
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#3085d6",
                        cancelButtonColor: "#d33",
                        confirmButtonText: "Delete"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $.ajax({
                                url: `{{ route('sub-admin.destroy', '') }}/${admin_id}`,
                                type: "DELETE",
                                data: admin_id,
                                success: function(res) {
                                    if (res.status == true) {
                                        Swal.fire(
                                            'Admin Delete',
                                            `${res.message}`,
                                            'success'
                                        ).then(function() {
                                            location
                                                .reload();
                                        })
                                    } else {
                                        Swal.fire(
                                            'Something went wrong',
                                            `${res.message}`,
                                            'error'
                                        )
                                    }
                                }
                            });
                        }
                    });
                }
            })
        });
        // show modal here
        @if ($errors->any())
            $('#addAdmin').modal('show');
            setTimeout(() => {
                $("#form-error").hide();
            }, 5000);
        @endif
    </script>
@endpush
