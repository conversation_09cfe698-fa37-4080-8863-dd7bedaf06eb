@extends('website.layout.master')
@push('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.css">
    <style>
        .sec-2-detail .inner_detail .list_desc.show {
            -webkit-line-clamp: unset;
        }
    </style>
@endpush
@section('content')
    <div id="pdf-content">
        <div class="printableArea">
            <section class="sec1_detail py-md-3 py-1 pb-0 overflow-hidden">
                <div class="container p-0">
                    <div class="row listing_meta align-items-center">
                        <div class="col-lg-12 col-sm-12 py-3" data-aos="fade">
                            <div class="d-flex justify-content-between align-items-center gap-2">
                                <h2 data-aos="fade-down">{{ $listing->name ?? '' }}</h2>
                                <h3 class="booking-id fs-18">{{ translate('user_booking_details.booking_id') }}:
                                    <span class="bold text-decoration-underline">{{ $booking->booking_number }} </span>
                                </h3>
                            </div>
                            <div class="d-flex justify-content-between align-items-center gap-2">
                                <div>
                                    <span data-aos="fade-down" class="pe-3">{{ $listing->type->name ?? 'Office' }}</span>
                                    <span class="px-sm-3 px-1 m-0 v_divide">
                                        {{ $listing->address?->city ?? '' }},
                                        {{ $listing->address?->state ?? '' }},
                                        {{ $listing->address?->country ?? '' }}
                                    </span>
                                </div>
                                <a href="#!" class="report-listing" data-bs-toggle="modal"
                                    data-bs-target="#report_modal">
                                    <img src="{{ asset('website/images/flag.svg') }}" alt="">
                                    <span>{{ translate('user_booking_details.report_listing') }} </span>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-12 d-flex justify-content-between  gap-md-0 gap-3">
                            <div class="d-flex align-items-center" data-aos="fade-right">
                                <div class="user_img me-md-3 me-2">
                                    <img class="img-fluid" src="{{ asset('website') . '/' . $listing->user->avatar }}"
                                        alt="profile image">
                                    {{-- src="{{ asset('website/images/plcaeholderListingImg.png') }}" alt="profile image"> --}}
                                </div>
                                <div class="user_info">
                                    <h6 class="">{{ translate('user_booking_details.hosted_by') }} {{ $listing->user->first_name ?? '' }}</h6>
                                    <p> <i class="fas fa-star"></i>
                                        @if (count($listing->user->provider_review ?? []) < 5)
                                            {{ translate('user_booking_details.new_host') }}
                                        @else
                                            {{ count($listing->user->provider_review ?? []) }} {{ translate('user_booking_details.reviews') }}
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div class="btn_info detail_contact_host d-flex align-items-center" data-aos="fade-left">
                                {{-- @auth
                                @isset($booking_check) --}}
                                <a href="{{ route('inboxChat', $listing->user_id ?? '1') }}"
                                    class="button detail_contact_host_btn">{{ translate('user_booking_details.contact_host') }}</a>
                                {{-- @endisset
                            @endauth --}}
                            </div>
                        </div>
                    </div>
                    <div class="row py-3 pb-0 listing_info">
                        <div class="col-xl-8 col-lg-12 listing_gallery">
                            <div style="--swiper-navigation-color: #fff; --swiper-pagination-color: #fff"
                                class="swiper mySwiper2">
                                <div class="swiper-wrapper pb-3">
                                    @forelse ($listing->gallery_images ?? [] as $gallery_image)
                                        @if ($gallery_image->type == 'image' || $gallery_image->type == 'video')
                                            <div class="swiper-slide">
                                                @if ($gallery_image->type == 'image')
                                                    <div class="slide_img">
                                                        <img class="img-fluid"
                                                            src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                            onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                                    </div>
                                                @elseif ($gallery_image->type == 'video')
                                                    <div class="slide_img">
                                                        <video width="100%" height="100%">
                                                            <source
                                                                src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                                type="video/mp4">
                                                            {{ __('website.your_browser_not_support_the_html_video') }}
                                                        </video>
                                                    </div>
                                                @endif
                                            </div>
                                        @endif
                                    @empty
                                        <div class="slide_img">
                                            <img class="img-fluid" src="{{ asset('website') }}"
                                                onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                        </div>
                                    @endforelse
                                </div>
                                <div class="swiper-button-next"><i class="fas fa-chevron-right"></i></div>
                                <div class="swiper-button-prev"><i class="fas fa-chevron-left"></i></div>
                            </div>
                            <div thumbsSlider="" class="swiper mySwiper">
                                <div class="swiper-wrapper">
                                    @foreach ($listing->gallery_images ?? [] as $gallery_image)
                                        @if ($gallery_image->type == 'image')
                                            <div class="swiper-slide">
                                                <div class="slides_img">
                                                    <img class="img-fluid"
                                                        src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                        onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                                </div>
                                            </div>
                                        @elseif ($gallery_image->type == 'video')
                                            <div class="swiper-slide">
                                                <div class="slides_img">
                                                    <video width="100%" height="100%">
                                                        <source src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                            type="video/mp4">
                                                        {{ __('website.your_browser_not_support_the_html_video') }}
                                                    </video>
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-12 ">
                            <div class="row listing_map">
                                <div class="col-md-12">
                                    <div class="map pb-4" data-aos="fade-left">
                                        <div id="map_location" style="height:620px;border-radius: 20px"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row listing_review">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="sec-2-detail">
                <div class="container p-0">
                    <div class="row g-0">
                        {{-- description --}}
                        <div class="col-md-12  inner_detail listing_description divider listing_data">
                            <div class="list_desc">
                                {!! $listing->description ?? '' !!}
                            </div>
                            <a href="#!" class="button read-more">{{ translate('user_booking_details.read_more') }}</a>
                        </div>
                        {{-- description end --}}
                        {{-- --------------------- Tour detail 1 --------------------- --}}
                        @includeWhen(
                            $listing->category->id == 1,
                            'website.template.listing-detail.tour',
                            compact('listing'))
                        {{-- --------------------- Tour detail 1 end --------------------- --}}
                        {{-- --------------------- boat detail 2 --------------------- --}}
                        @includeWhen(
                            $listing->category->id == 2,
                            'website.template.listing-detail.boat',
                            compact('listing'))
                        {{-- --------------------- boat detail 2 end --------------------- --}}
                        {{-- --------------------- car detail 3 --------------------- --}}
                        @includeWhen(
                            $listing->category->id == 3,
                            'website.template.listing-detail.car',
                            compact('listing'))
                        {{-- --------------------- car detail 3 end --------------------- --}}
                        {{-- --------------------- house detail --------------------- --}}
                        @includeWhen(
                            $listing->category->id == 4,
                            'website.template.listing-detail.house',
                            compact('listing'))
                        {{-- --------------------- house detail end --------------------- --}}
                        {{-- Not for Medical Rules and Cancelation --}}
                        @if ($category->id != 5)
                            {{-- Rule --}}
                            @if (isset($listing->detail->pet) || isset($listing->rules))
                                <div class="col-lg-12 divider listing_data listing_rule">
                                    <div class="amenities-box">
                                        <h3 class="fs-22 listing_data_heading">{{ translate('user_booking_details.rules') }}</h3>
                                        {{-- @if (isset($listing->rules)) --}}
                                        <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                            {{-- for pets --}}
                                            @if ($listing->detail->pet == 'yes')
                                                <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                                                    <img src="{{ asset('website/images/square-check.svg') }}"
                                                        height="20px" width="20px" alt="Allowed">
                                                    <span>{{ translate('user_booking_details.pets') }}</span>
                                                </div>
                                            @endif
                                            @forelse ($listing->rules as $rule)
                                                @if (isset($rule->title))
                                                    @if ($rule->allow == 'yes')
                                                        <div class="box allowed d-flex gap-2 align-items-center"
                                                            data-aos="fade">
                                                            <img src="{{ asset('website/images/square-check.svg') }}"
                                                                height="20px" width="205px" alt="Allowed">
                                                            <span>{{ $rule->title }}</span>
                                                        </div>
                                                    @endif
                                                @endif
                                            @endforeach
                                        </div>
                                        {{-- @endif --}}
                                        {{-- @if (isset($listing->detail->rule)) --}}
                                        <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                            {{-- for pets --}}
                                            @if ($listing->detail->pet == 'no')
                                                <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                                                    <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                        height="20px" width="20px" alt="Not Allowed">
                                                    <span>{{ translate('user_booking_details.pets') }}</span>
                                                </div>
                                            @endif
                                            @forelse ($listing->rules as $rule)
                                                @if (isset($rule->title))
                                                    @if ($rule->allow == 'no')
                                                        <div class="box not-allowed d-flex gap-2 align-items-center"
                                                            data-aos="fade">
                                                            <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                                height="20px" width="20px" alt="Not Allowed">
                                                            <span>{{ $rule->title }}</span>
                                                        </div>
                                                    @endif
                                                @endif
                                            @endforeach
                                        </div>
                                        {{-- @endif --}}
                                    </div>
                                </div>
                            @endif
                            {{-- Rule end --}}
                            {{-- Notes --}}
                            @if (isset($listing->notes) && !$listing->notes->isEmpty())
                                <div class="col-lg-12 divider listing_data listing_notes">
                                    <div class="amenities-box">
                                        <h3 class="fs-22 listing_data_heading">{{ translate('user_booking_details.things_that_you_need_to_know') }}
                                            {{-- {{ $category->id == 4 ? 'guests' : 'renters' }} --}}
                                        </h3>
                                        <ol class="parent-box pt-2">
                                            @forelse ($listing->notes as $note)
                                                <li class="pb-3 fs-14" data-aos="fade">
                                                    <span>{{ $note->name }}</span>
                                                </li>
                                            @endforeach
                                        </ol>
                                    </div>
                                </div>
                            @endif
                            {{-- Notes end --}}
                            {{-- Cancelation  --}}
                            <div class="col-md-12 main_col_listing_cancellation" style="display: none;">
                                <div class="row detail-rw g-0 divider listing_data pt-5 listing_cancelation">
                                    <div class="col-lg-12">
                                        <div class="details-box">
                                            <div class="box d-flex gap-2 align-items-start">
                                                <div class="icon">
                                                    <img src="{{ asset('website/images/ban.svg') }}" alt="">
                                                </div>
                                                <div class="content w-100">
                                                    <h6>{{ translate('user_booking_details.cancellation_policy') }}</h6>
                                                </div>
                                            </div>
                                            <div id="cancellation_data"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{-- End Cancelation  --}}
                        @endif
                    </div>
                    {{-- URL --}}
                    @if (in_array($category->id, [5]))
                        <div class="row py-3 listing_url g-0">
                            <div class="col-md-12">
                                <div class="info py-2">
                                    <h5 class="ps-0 pb-2" data-aos="fade">URL</h5>
                                    <a href="#!" class="fs-16 blue">{{ $listing->url ?? translate('user_booking_details.not_provided') }} </a>
                                </div>
                            </div>
                        </div>
                    @endif
                    {{-- End URL --}}
                </div>
            </section>
            <section class="book_details booking_detail my_booking_details_sec pb-2 pt-2 mb-0">
                <div class="container">
                    <div class="row">
                        <div class="col-md-12 booking-detail">
                            <div class="book_box">
                                <div class="row g-0 justify-content-between trip-detail pt-3 gap-lg-0 gap-3 divider listing_data"
                                    data-aos="fade">
                                    <div class="col-lg-5">
                                        <h6 class="fs-20 light-bold">{{ translate('user_booking_details.booking_details') }}</h6>
                                        @if ($booking->listing_basis == 'Tour')
                                            <div class="trip-box pt-2 pb-1 d-flex align-items-center gap-5">
                                                <div class="checkin">
                                                    <h6 class="fs-16 light-bold mb-0">
                                                        {{ $booking->detail->tour_duration_type == 'multiple_days' ? translate('user_booking_details.start_day') : translate('user_booking_details.end_day') }}
                                                    </h6>
                                                    <p class="fs-14 text-black-50 mb-0">
                                                        {{-- {{ $booking->check_in }} --}}
                                                        {{ date(config('constant.date_format'), strtotime($booking->check_in ?? '-')) }}
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="trip-box pt-2 pb-1 d-flex align-items-center gap-5">
                                                <div class="checkin">
                                                    <h6 class="fs-16 light-bold mb-0">{{ translate('user_booking_details.tour_duration_type') }}</h6>
                                                    <p class="fs-14 text-black-50 mb-0">
                                                        {{ $booking->detail->tour_duration_type }}
                                                    </p>
                                                </div>
                                            </div>
                                            {{-- <div class="checkin">
                                                <h6 class="fs-16 light-bold">Time</h6>
                                                @forelse ($booking->tour_durations as $tour_duration)
                                                    @if ($loop->first)
                                                        @if (count($booking->tour_durations) == 1)
                                                            <p class="fs-14 text-black-50 mb-0">{{ $tour_duration->start_time . " - " . $tour_duration->end_time }}</p>
                                                        @endif
                                                    @else
                                                        <p class="fs-14 text-black-50 mb-0">Day {{ $tour_duration->day ."= ". $tour_duration->start_time . " - " . $tour_duration->end_time }}</p>
                                                    @endif
                                                @empty
                                                @endforelse
                                            </div> --}}
                                        @else
                                            <div class="trip-box pt-2 pb-1 d-flex align-items-center gap-5">
                                                @if ($booking->detail->total_days == 1)
                                                    <div class="checkin">
                                                        <h6 class="fs-16 light-bold">
                                                            @if ($booking->listing->category_id == 2)
                                                                {{ translate('user_booking_details.departure_return_date') }}
                                                            @elseif ($booking->listing->category_id == 3)
                                                                {{ translate('user_booking_details.pickup_dropoff_date') }}
                                                            @elseif ($booking->listing->category_id == 4)
                                                                {{ translate('user_booking_details.checkin_checkout_date') }}
                                                            @endif
                                                        </h6>
                                                        <p class="fs-14 text-black-50">
                                                            {{ date(config('constant.date_format'), strtotime($booking->check_in ?? '-')) }}
                                                        </p>
                                                    </div>
                                                @else
                                                    <div class="checkin">
                                                        <h6 class="fs-16 light-bold">
                                                            @if ($listing->category->id == 3)
                                                               {{translate('user_booking_details.pickup_date')}}
                                                            @elseif ($listing->category->id == 2)
                                                               {{translate('user_booking_details.departure_date')}}
                                                            @else
                                                               {{translate('user_booking_details.checkin_date')}}
                                                            @endif
                                                            {{-- {{ in_array($listing->category->id, [3]) ? 'Pick-up' : 'Check-in' }} --}}
                                                        </h6>
                                                        <p class="fs-14 text-black-50">
                                                            {{ date(config('constant.date_format'), strtotime($booking->check_in ?? '-')) }}
                                                        </p>
                                                    </div>
                                                    <div class="checkout">
                                                        <h6 class="fs-16 light-bold">
                                                            @if ($listing->category->id == 3)
                                                                {{ translate('user_booking_details.dropoff_date') }}
                                                            @elseif ($listing->category->id == 2)
                                                                {{ translate('user_booking_details.return_date') }}
                                                            @else
                                                                {{ translate('user_booking_details.checkout_date') }}
                                                            @endif
                                                            {{-- {{ in_array($listing->category->id, [2, 3]) ? 'Drop-off' : 'Check-out' }} --}}
                                                        </h6>
                                                        <p class="fs-14 text-black-50">
                                                            {{ date(config('constant.date_format'), strtotime($booking->check_out ?? '-')) }}
                                                        </p>
                                                    </div>
                                                @endif
                                            </div>
                                        @endif
                                        @if ($booking->listing_basis == 'Hourly')
                                            <div class="checkin">
                                                <h6 class="fs-16 light-bold">{{ translate('user_booking_details.slots') }}</h6>
                                                @forelse ($booking->hourly_slots as $hourly_slot)
                                                    <p class="fs-14 text-black-50">{{ $hourly_slot->slot }}</p>
                                                @empty
                                                @endforelse
                                            </div>
                                        @elseif ($booking->listing_basis == 'Daily')
                                            <div class="trip-box pt-2 pb-1 d-flex align-items-center gap-5">
                                                <div class="checkin_time">
                                                    @if ($listing->category->id == 3)
                                                        <h6 class="fs-16 light-bold">{{ translate('user_booking_details.pickup_time') }}</h6>
                                                        <p class="fs-14 text-black-50">
                                                            {{ $booking->check_in_time ?? '--:--' }}
                                                        </p>
                                                    @elseif ($listing->category->id == 2)
                                                        <h6 class="fs-16 light-bold">{{ translate('user_booking_details.departure_time') }}</h6>
                                                        <p class="fs-14 text-black-50">
                                                            {{ $listing->detail->check_in_time ?? '--:--' }}
                                                        </p>
                                                    @endif
                                                </div>
                                                <div class="checkout_time">
                                                    @if ($listing->category->id == 3)
                                                        <h6 class="fs-16 light-bold">{{ translate('user_booking_details.dropoff_time') }}</h6>
                                                        <p class="fs-14 text-black-50">
                                                            {{ $booking->check_out_time ?? '--:--' }}
                                                        </p>
                                                    @elseif ($listing->category->id == 2)
                                                        <h6 class="fs-16 light-bold">{{ translate('user_booking_details.return_time') }}</h6>
                                                        <p class="fs-14 text-black-50">
                                                            {{ $listing->detail->check_out_time ?? '--:--' }}
                                                        </p>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif

                                        <div class="trip-box pt-2 pb-1">

                                            @if (($booking->customer->first_name ?? '') != '' || ($booking->customer->last_name ?? '') != '')
                                                <p class="mb-0 fs-16 light-bold">{{ translate('user_booking_details.booking_name') }}: <span
                                                        class="normal text-black-50">{{ $booking->customer->first_name }}
                                                        {{ $booking->customer->last_name }}</span></p>
                                            @endif

                                            @if (($booking->guest ?? '') != '')
                                                <p class="mb-0 fs-16 light-bold">{{ translate('user_booking_details.guests') }}: <span
                                                        class="normal text-black-50">{{ $booking->guest }}</span>
                                                </p>
                                            @endif

                                            @if (($booking->listing->address->address ?? '') != '')
                                                <p class="mb-0 fs-16 light-bold">{{ translate('user_booking_details.booking_address') }}: <span
                                                        class="normal text-black-50">{{ $booking->listing->address->address ?? '' }}</span>
                                                </p>
                                            @endif

                                            @if (($booking->listing->address->zip_code ?? '') != '')
                                                <p class="mb-0 fs-16 light-bold">{{ translate('user_booking_details.zip_code') }}: <span
                                                        class="normal text-black-50">{{ $booking->listing->address->zip_code ?? '' }}</span>
                                                </p>
                                            @endif

                                            @if (($booking->listing->address->suit ?? '') != '')
                                                <p class="mb-0 fs-16 light-bold">{{ translate('user_booking_details.suite') }}: <span
                                                        class="normal text-black-50">{{ $booking->listing->address->suit ?? '' }}</span>
                                                </p>
                                            @endif

                                        </div>
                                        {{-- @if ($listing->category_id == 4)
                                        <div class="trip-box d-flex justify-content-between align-items-end">
                                            <div>
                                                <h6 class="fs-16 light-bold">Guests</h6>
                                                <div class="guest_wrapper d-flex gap-2 align-items-center">
                                                    <button class="btn minus_btn p-0 d_none" type="button"
                                                        onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                                        <i class="fa fa-minus" aria-hidden="true"></i>
                                                    </button>
                                                    <input type="number" min="1"
                                                        max="{{ $listing->detail->guests ?? '1' }}" value="2"
                                                        class="form-control fs-14 text-black-50 border-0 p-0 text-center"
                                                        name="capacity" id="capacity" readonly />
                                                    <button class="btn plus_btn p-0 d_none" type="button"
                                                        onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                                        <i class="fa fa-plus" aria-hidden="true"></i>
                                                    </button>
                                                    <span class="fs-14 text-black-50">Guests</span>
                                                </div>
                                            </div>
                                            <button type="button"
                                                class="edit-trip button text-decoration border-0">Edit</button>
                                        </div>
                                    @endif --}}
                                    </div>
                                    @php
                                        $currency = $booking->currency;
                                        $conversion_rate = $booking->conversion_rate;
                                    @endphp
                                    <div class="col-lg-6">
                                        <div class="price_wraper">
                                            <h6 class="fs-20 light-bold">{{ translate('user_booking_details.pricing_details') }}</h6>
                                            <div class="price_box">

                                                {{-- listing price --}}
                                                @if ($booking->listing_basis == 'Tour')
                                                    @if ($booking->tour_type == 'guests')
                                                        <div class="d-flex justify-content-between gap-1 fs-14">
                                                            <span>{{ translate('user_booking_details.price_x_adult') }}/span>
                                                            <span>
                                                                {{ $currency . ' ' . number_format($booking->adult_price * $conversion_rate, 0) }}
                                                            </span>
                                                        </div>
                                                        @if ($booking->child_price && $booking->detail->child_number && $booking->detail->child_number > 0)
                                                            <div class="d-flex justify-content-between gap-1 fs-14">
                                                                <span>{{ translate('user_booking_details.price_x_child') }}</span>
                                                                <span>
                                                                    {{ $currency }}
                                                                    {{ number_format($booking->child_price * $conversion_rate, 0) }}</span>
                                                            </div>
                                                        @endif
                                                    @else
                                                        <div class="d-flex justify-content-between gap-1 fs-14">
                                                            <span>{{ translate('user_booking_details.private_booking_price') }}</span>
                                                            <span>
                                                                {{ $currency . ' ' . number_format($booking->private_booking_price * $conversion_rate, 0) }}</span>
                                                        </div>
                                                    @endif
                                                @else
                                                    <div class="d-flex justify-content-between gap-1 fs-14">
                                                        <span>{{ translate('user_booking_details.listing_price') }}</span>
                                                        <span>
                                                            {{ $currency }}
                                                            {{ number_format($booking->listing_price * $conversion_rate, 0) }}</span>
                                                    </div>
                                                @endif
                                                {{-- listing price end --}}
                                                {{-- listing Basis --}}
                                                <div class="d-flex justify-content-between gap-1 fs-14">
                                                    <span>{{ translate('user_booking_details.booking_type') }}</span>
                                                    <span>
                                                        @if ($booking->listing_basis == 'Daily')
                                                            {{ $booking->listing->category_id == 4 ? translate('user_booking_details.night') : translate('user_booking_details.day') }}
                                                        @elseif ($booking->listing_basis == 'Tour')
                                                            {{ translate('user_booking_details.experience') }}
                                                        @else
                                                            {{ $booking->listing_basis }}
                                                        @endif
                                                    </span>
                                                </div>
                                                {{-- listing Basis end --}}
                                                {{-- hourly --}}
                                                @if ($booking->listing_basis == 'Hourly')
                                                    <div class="d-flex justify-content-between gap-1 fs-14">
                                                        <span>{{ translate('user_booking_details.total_hours') }}(s)</span>
                                                        <span>
                                                            {{ $booking->detail->total_hours }}
                                                        </span>
                                                    </div>
                                                @elseif($booking->listing_basis == 'Daily')
                                                    <div class="d-flex justify-content-between gap-1 fs-14">
                                                        <span>{{ translate('user_booking_details.total') }}
                                                            {{ $booking->listing->category_id == 4 ? translate('user_booking_details.night') : translate('user_booking_details.day') }}(s)</span>
                                                        <span>
                                                            {{ $booking->detail->total_days }}
                                                        </span>
                                                    </div>
                                                @elseif($booking->listing_basis == 'Tour')
                                                    <div class="d-flex justify-content-between gap-1 fs-14">
                                                        <span>{{ translate('user_booking_details.tour_type') }}</span>
                                                        <span>
                                                            {{ ucwords($booking->tour_type) }}
                                                        </span>
                                                    </div>
                                                    <div class="d-flex justify-content-between gap-1 fs-14">
                                                        <span> {{ translate('user_booking_details.tour_type') }}
                                                            {{ $booking->detail->adult_number == 1 ? translate('user_booking_details.adult') : translate('user_booking_details.adults') }}</span>
                                                        <span>
                                                            {{ $booking->detail->adult_number }}
                                                        </span>
                                                    </div>
                                                    @if ($booking->detail->child_number)
                                                        <div class="d-flex justify-content-between gap-1 fs-14">
                                                            <span>{{ translate('user_booking_details.total') }}
                                                                {{ $booking->detail->child_number == 1 ? translate('user_booking_details.children') : translate('user_booking_details.childrens') }}</span>
                                                            <span>
                                                                {{ $booking->detail->child_number }}
                                                            </span>
                                                        </div>
                                                    @endif
                                                @endif
                                                {{-- hourly end --}}

                                                {{-- total amount --}}
                                                <div class="d-flex justify-content-between gap-1 fs-14">
                                                    <span>{{ translate('user_booking_details.total_price') }}</span>
                                                    <span>
                                                        {{ $currency }}
                                                        {{ number_format($booking->total_amount * $conversion_rate, 0) }}</span>
                                                </div>
                                                {{-- total amount end --}}

                                                {{-- discount --}}
                                                @forelse ($booking->discounts as $discount)
                                                    <div class="d-flex justify-content-between gap-1 fs-14">
                                                        <span>{{ $discount->type }} ({{ $discount->percent }}%)</span>
                                                        <span class="text-success">
                                                            - {{ $currency }}
                                                            {{ number_format($discount->amount * $conversion_rate, 0) }}</span>
                                                    </div>
                                                @empty

                                                @endforelse
                                                {{-- discount end --}}

                                                <hr class="text-dark my-2">
                                                {{-- Subtotal --}}
                                                <div class="d-flex justify-content-between gap-1 fs-14 light-bold">
                                                    <span>{{ translate('user_booking_details.sub_total') }} ({{ $currency }})</span>
                                                    <span>{{ $currency }}
                                                        {{ number_format($booking->sub_total * $conversion_rate, 0) }}
                                                    </span>
                                                </div>
                                                {{-- Subtotal end --}}
                                                <hr class="text-dark my-2">
                                                {{-- USD total --}}
                                                <div class="d-flex justify-content-between gap-1 fs-14 light-bold">
                                                    <span>{{ translate('user_booking_details.total_paid_in_usd') }}</span>
                                                    <span class="bold flex-shrink-0">USD
                                                        {{ $booking->total_usd_amount }}
                                                    </span>
                                                </div>
                                                {{-- USD total end --}}

                                                {{-- Booking Status --}}
                                                <div class="d-flex justify-content-between gap-1 fs-14 light-bold">
                                                    <span>{{ translate('user_booking_details.booking_status') }}</span>
                                                    @if ($booking->status == 0)
                                                        <span class="bold flex-shrink-0 text-success">{{ translate('user_booking_details.confirmed') }}</span>
                                                    @elseif($booking->status == 7)
                                                        <span class="bold flex-shrink-0 text-danger">
                                                            {{ translate('user_booking_details.cancelled') }}
                                                        </span>
                                                    @elseif($booking->status == 3)
                                                        <span class="bold flex-shrink-0 text-primary">{{ translate('user_booking_details.completed') }}</span>
                                                    @endif
                                                </div>
                                                {{-- Payment Status --}}
                                                <div class="d-flex justify-content-between gap-1 fs-14 light-bold">
                                                    <span>{{ translate('user_booking_details.payment_status') }}</span>
                                                    @if ($booking->status == 0)
                                                        <span class="bold flex-shrink-0 text-success">{{ translate('user_booking_details.paid') }}</span>
                                                    @elseif($booking->status == 7)
                                                        <span class="bold flex-shrink-0 text-danger">
                                                            {{-- Cancelled --}}
                                                            @if ($booking->refund_percentage == 100)
                                                                {{ translate('user_booking_details.fully_refunded') }}
                                                            @elseif($booking->refund_percentage < 100 && $booking->refund_percentage > 0)
                                                                {{ translate('user_booking_details.partially_refunded') }}
                                                            @else
                                                                {{ translate('user_booking_details.no_refund') }}
                                                            @endif
                                                        </span>
                                                    @elseif($booking->status == 3)
                                                        <span class="bold flex-shrink-0 text-primary">{{ translate('user_booking_details.completed') }}</span>
                                                    @endif
                                                </div>
                                                {{-- Booking Status --}}
                                                <div class="d-flex justify-content-between gap-1 fs-14 light-bold">
                                                    <span>{{ translate('user_booking_details.payment_method') }}</span>
                                                    <span class="light_bold flex-shrink-0">
                                                        @if ($booking->payment_method != 'card' || $booking->payment_method != 'card')
                                                            {{ ucwords($booking->payment_method) }}
                                                        @else
                                                            {{ ucwords($booking->card_brand ?? '') }} -
                                                            {{ $booking->last_4_digits ?? '' }}
                                                        @endif
                                                    </span>
                                                </div>

                                                @if ($booking->payment_method == 'card' && isset($booking->receipt_url))
                                                    <div class="d-flex justify-content-end gap-1 fs-14 light-bold">
                                                        <a href="{{ $booking->receipt_url }}" target="_blank"
                                                            class="blue text-decoration">{{ translate('user_booking_details.download_invoice') }}</a>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    @if (isset($booking) && $booking->status == 0)
                                        <div class="col-md-12">
                                            <div class="inner_section_booking_buttons_wrapper">
                                                @if (\Carbon\Carbon::parse($booking->check_in)->gte(\Carbon\Carbon::today()))
                                                    <a href="{{ route('cancel_booking', ['booking_id' => $booking->ids]) }}"
                                                        class="btn button_red cancel-btn"
                                                        data-service-provider-amount="{{ $cancellationService['data']['service_provider_amount'] }}"
                                                        data-customer-amount="{{ $cancellationService['data']['customer_amount'] }}"
                                                        data-refund-percentage="{{ $cancellationService['data']['refundPercentage'] }}"
                                                        data-canellation-policy="{{ $cancellationService['data']['cancellation_policy'] }}">
                                                        {{ translate('user_booking_details.cancel_booking') }}
                                                    </a>
                                                @endif
                                                <button type="button" disabled class="btn button">{{ translate('user_booking_details.change_booking_details') }} 
                                                    <i class="fas fa-lock pull-right"></i> </button>
                                                <a class="btn button1 " id="download-pdf" role="status"
                                                    href="javascript:void(0)">
                                                    <span class="download-text">
                                                        {{ translate('user_booking_details.download_booking_confirmation') }}
                                                    </span>
                                                    <span class="spinner-border d-none"></span>
                                                </a>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="secure_payment_assurance_sec">
                <div class="container">
                    <div class="row divider listing_data">
                        <div class="col-md-12">
                            <div class="top_heading">
                                <h3>{{ translate('user_booking_details.luxustars_secure_payment_assurance') }}</h3>
                            </div>
                            <div class="payment_content">
                                <p>💫 <i>{{ translate('user_booking_details.your_peace_of_mind_our_priority') }}</i> 💫</p>
                                <p>{{translate('user_booking_details.at_luxustars_all_payments_are_securely_processed')}}:</p>
                                <ul>
                                    <li>
                                        <p>
                                            <b>{{ translate('user_booking_details.24_7_support') }}</b>
                                            {{ translate('user_booking_details.assistance_whenever_you_need_it') }}.
                                        </p>
                                    </li>
                                    <li>
                                        <p>
                                            <b>{{ translate('user_booking_details.payment_protection') }}:</b>
                                            {{ translate('user_booking_details.your_funds_are_held_safely_until_the_booking_is_completed') }}.
                                        </p>
                                    </li>
                                    <li>
                                        <p>
                                            <b>{{ translate('user_booking_details.cancellation_coverage') }}:</b>
                                            {{ translate('user_booking_details.enjoy_policies_tailored_to_your_needs') }}.
                                        </p>
                                    </li>
                                </ul>
                                <p>✨ <b>{{translate('user_booking_details.experience_luxury_with_confidence')}}.</b>
                                </p>
                                {{-- <ul>
                                <li>
                                    <p>A badge or assurance: "You're covered by LuxuStars' policies"</p>
                                </li>
                            </ul> --}}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <section class="need_assistance_sec mt-3">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="inner_section_need_assistance_col">
                            <div class="top_heading mb-3">
                                <h3>{{ translate('user_booking_details.need_assistance') }}</h3>
                            </div>
                            <div class="assitance_buttons_wrapper d-flex gap-2 align-items-center">
                                <a href="{{ route('inboxChat', 2) }}" class="btn button1" target="_blank">{{ translate('user_booking_details.contact_support') }}</a>
                                <a href="{{ route('help_center') }}" class="btn button btn_black" target="_blank">{{ translate('user_booking_details.go_to_help_center') }}</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <div class="modal fade all-amenties" id="all-amenties" tabindex="-1" aria-modal="true" role="dialog"
            data-bs-backdrop="static">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content pb-0">
                    <div class="modal-header border-0 ">
                        <h4 class="modal-title mx-auto">{{ translate('user_booking_details.everything_included_in_this_place') }} </h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body pb-lg-10 px-lg-10 pt-4 pb-0">
                        <div class="parent-box row g-0 align-items-start">
                            @foreach ($listing->amenity_detail ?? [] as $amenities)
                                <div class="col-md-12 divider pb-3">
                                    <div class="box d-flex gap-3 align-items-center" data-aos="fade">
                                        <img src="{{ asset('website/images/bedroom.svg') }}" alt=""
                                            height="20px" width="20px">
                                        <div class="amenity-data ">
                                            <span>{{ $amenities->name ?? '' }}</span>
                                            <p class="amenties_desc fs-14 m-0 text-black-50">
                                                {{ $amenities->description ?? '' }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Modal -->
        <div class="modal report fade" id="report_modal" tabindex="-1" aria-labelledby="reportHeading"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title mx-auto" id="reportHeading">{{ translate('user_booking_details.report_listing') }} {{ $listing->name ?? '' }} </h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="{{ route('report_form') }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <input type="hidden" name="booking_id" id="booking_id">
                            <div class="mb-3">
                                <input type="text" name="subject" class="form-control" placeholder="{{ translate('user_booking_details.subject') }}">
                            </div>
                            <div class="mb-3">
                                <textarea class="form-control" name="description" rows="7" id="" placeholder="{{ translate('user_booking_details.description') }}"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            {{-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button> --}}
                            <button type="submit" class="bg_black btn button1 white">{{ translate('user_booking_details.submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        $(document).ready(function() {
            $(document).on("click", ".read-more", function() {
                const showMoreText = @json(translate('user_booking_details.show_more'));
                const showLessText = @json(translate('user_booking_details.show_less'));
                var $this = $(this);
                var $content = $('.sec-2-detail .inner_detail .list_desc');
                var linkText = $this.text().toLowerCase().trim();
                // Toggle the 'show' class on $content
                if ($content.hasClass('show')) {
                    $content.removeClass("show");
                    linkText = showMoreText;
                } else {
                    $content.addClass("show");
                    linkText = showLessText;
                }
                // Update the link text
                $this.text(linkText);
            });
        });
    </script>
    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.0.1/angular.min.js"></script>
    <script type="text/javascript">
        function initMap() {
            const myLatLng = {
                lat: {{ $listing->lat ?? 41.29834049999999 }},
                lng: {{ $listing->lng ?? 2.0800095 }}
            };
            const map = new google.maps.Map(document.getElementById("map_location"), {
                zoom: 12,
                center: myLatLng,
            });
            const marker = new google.maps.Marker({
                position: myLatLng,
                map: map,
                //icon: markerIcon,
                title: "{{ $listing->name ?? '' }}",
            });
        }
        window.initMap = initMap;
    </script>
    <script src="{{ url('google-map') }}?callback=initMap"></script>
    <script>
        $(window).on('load', function() {
            var content = $('.sec-2-detail .inner_detail .list_desc');
            var hasEllipsis = false;
            content.each(function() {
                var $this = $(this);
                if ($this[0].scrollHeight > $this.innerHeight()) {
                    hasEllipsis = true;
                    return false;
                }
            });
            if (hasEllipsis) {
                console.log('Text is truncated, showing Read More button.');
                $('.read-more').show();
            } else {
                console.log('Text fits within the clamped lines, hiding Read More button.');
                $('.read-more').hide();
            }
        });
        $(document).ready(function() {
            $(window).on('load', function() {
                $('#booking-form .calendar .pignose-calendar-unit-disabled + .pignose-calendar-unit > a')
                    .trigger('click')
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            $(document).on("click", ".cancel-btn", function(event) {
                event.preventDefault(); // Stop direct navigation

                // Get values from data attributes
                let serviceProviderAmount = $(this).data("service-provider-amount");
                let customerAmount = $(this).data("customer-amount");
                let refundPercentage = $(this).data("refund-percentage");
                let cancelPolicy = $(this).data("canellation-policy");
                let cancelUrl = $(this).attr("href");

                // SweetAlert2 Confirmation
                Swal.fire({
                    title: @json(translate('user_booking_details.are_you_sure')),
                  html: `
                        <p><strong>@json(translate('user_booking_details.refund_amount')): </strong> $${customerAmount}</p>
                        <p><strong>@json(translate('user_booking_details.refund_percentage')): </strong> ${refundPercentage}%</p>
                        <p><strong>@json(translate('user_booking_details.cancellation_policy')): </strong> ${cancelPolicy}</p>
                    `,
                    icon: "warning",
                    showCancelButton: true,
                   confirmButtonText: @json(translate('user_booking_details.cancel_my_booking')),
                    cancelButtonText: @json(translate('user_booking_details.keep_my_booking'))
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Redirect to cancellation link if confirmed
                        window.location.href = cancelUrl;
                    }
                });
            });
        });
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script>
        // $(document).ready(function() {
        //     $("#download-pdf").click(function() {
        //         const {
        //             jsPDF
        //         } = window.jspdf;

        //         // Set up A1 size dimensions (Portrait)
        //         let doc = new jsPDF({
        //             orientation: 'portrait', // Portrait layout
        //             unit: 'mm',
        //             format: [594, 841] // A1 size: 594mm x 841mm
        //         });

        //         // Capture the content without the navbar
        //         html2canvas(document.querySelector("#pdf-content"), {
        //             scale: 2, // Increase scale for better quality
        //             useCORS: true, // Enable cross-origin image rendering
        //             allowTaint: true, // Allows cross-domain images to be used in canvas
        //             logging: true
        //         }).then(canvas => {
        //             let imgData = canvas.toDataURL("image/png");
        //             let imgWidth = 594; // Full width of A1 paper
        //             let imgHeight = (canvas.height * imgWidth) / canvas.width;
        //             let topMargin = 0; // Keep content aligned at the top
        //             let bottomMargin = 120; // Leave some space at the bottom

        //             // Add image to PDF (leaving bottom margin)
        //             doc.addImage(imgData, 'PNG', 0, topMargin, imgWidth, imgHeight - bottomMargin);

        //             // Save the PDF
        //             doc.save(
        //                 `{{ $booking->booking_number . '-' . today()->format(config('constant.date_format')) }}.pdf`
        //             );
        //         });
        //     });
        // });





        // document.getElementById('download-pdf').addEventListener('click', async function() {
        //     const button = this;
        //     const spinner = document.querySelector('.spinner-border');
        //     const downloadText = document.querySelector('.download-text');

        //     button.disabled = true;

        //     if (spinner) spinner.classList.remove('d-none');
        //     if (downloadText) downloadText.classList.add('d-none');

        //     try {
        //         var iframe = document.createElement('iframe');
        //         iframe.src =
        //             "{{ route('my-booking-pdf', ['ids' => $booking->ids, 'booking_number' => $booking->booking_number]) }}";
        //         iframe.style.position = "absolute";
        //         iframe.style.left = "-9999px";
        //         iframe.style.top = "-9999px";
        //         iframe.style.width = "1440px";
        //         // iframe.style.height = "1484px"; // A3 height in pixels (you can change this if needed)

        //         document.body.appendChild(iframe);

        //         iframe.onload = async function() {
        //             try {
        //                 const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;

        //                 const mapDiv = iframeDocument.getElementById("map_location");

        //                 if (mapDiv) {
        //                     const lat = {{ $listing->lat ?? 41.29834049999999 }};
        //                     const lng = {{ $listing->lng ?? 2.0800095 }};

        //                     const staticMapUrl =
        //                         `https://maps.googleapis.com/maps/api/staticmap?center=${lat},${lng}&zoom=12&size=400x600&markers=color:red%7C${lat},${lng}&key=AIzaSyDF1KUigTPIJOGIoRVEV9u7M9Tyx_Wzxj0`;

        //                     const staticMapImg = document.createElement("img");
        //                     staticMapImg.src = staticMapUrl;
        //                     staticMapImg.style.width = "100%";
        //                     staticMapImg.style.height = "100%";
        //                     staticMapImg.style.borderRadius = "20px";
        //                     staticMapImg.style.display = "block";

        //                     const originalMapHTML = mapDiv.innerHTML;
        //                     mapDiv.innerHTML = "";
        //                     mapDiv.appendChild(staticMapImg);
        //                 }

        //                 const canvas = await html2canvas(iframeDocument.body, {
        //                     scale: 1.5,     // (Value was 2 but downgraded to 1.5 due to very large canva size)
        //                     useCORS: true
        //                 });
        //                 // const imgData = canvas.toDataURL('image/webp');
        //                 const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
        //                 const imgData = canvas.toDataURL(isSafari ? 'image/jpeg' : 'image/webp', 1); // (Value was 0.5 now changed to 1)
        //                 console.log(`Image Data: ${imgData}`);

        //                 const {
        //                     jsPDF
        //                 } = window.jspdf;
        //                 const pdf = new jsPDF();

        //                 const imgWidth = canvas.width;
        //                 console.log(`Canva Width: ${imgWidth}`);
        //                 const imgHeight = canvas.height;

        //                 const a4Width = 210;
        //                 const a4Height = 297;

        //                 const scaleFactor = 0.264583;
        //                 const imgWidthMM = imgWidth * scaleFactor;
        //                 const imgHeightMM = imgHeight * scaleFactor;

        //                 let scaledWidth = a4Width;
        //                 let scaledHeight = (imgHeightMM * a4Width) / imgWidthMM;

        //                 if (scaledHeight > a4Height) {
        //                     scaledHeight = a4Height;
        //                     scaledWidth = (imgWidthMM * a4Height) / imgHeightMM;
        //                 }

        //                 if (scaledWidth > a4Width) {
        //                     scaledWidth = a4Width;
        //                     scaledHeight = (imgHeightMM * a4Width) / imgWidthMM;
        //                 }

        //                 const xPos = (a4Width - scaledWidth) / 2;
        //                 const yPos = (a4Height - scaledHeight) / 2;

        //                 // pdf.addImage(imgData, 'WEBP', xPos, yPos, scaledWidth, scaledHeight);
        //                 pdf.addImage(imgData, isSafari ? 'JPEG' : 'WEBP', xPos, yPos, scaledWidth, scaledHeight, null, 'SLOW');

        //                 pdf.save('booking_confirmation.pdf');
        //                 if (spinner) {
        //                     spinner.classList.add('d-none');
        //                     downloadText.classList.remove('d-none');
        //                     this.disabled = false;
        //                 }
        //             } catch (error) {
        //                 console.error('Error during PDF generation:', error);
        //             } finally {
        //                 document.body.removeChild(iframe);
        //             }
        //         };

        //         iframe.onerror = function() {
        //             console.error('Error loading iframe.');
        //             document.body.removeChild(iframe);
        //         };
        //     } catch (error) {
        //         console.error('Error during download PDF process:', error);
        //     }
        // });




        document.getElementById('download-pdf').addEventListener('click', async function() {
            const button = this;
            const spinner = document.querySelector('.spinner-border');
            const downloadText = document.querySelector('.download-text');

            button.disabled = true;

            if (spinner) spinner.classList.remove('d-none');
            if (downloadText) downloadText.classList.add('d-none');

            try {
                var iframe = document.createElement('iframe');
                iframe.src =
                    "{{ route('my-booking-pdf', ['ids' => $booking->ids, 'booking_number' => $booking->booking_number]) }}";
                iframe.style.position = "absolute";
                iframe.style.left = "-9999px";
                iframe.style.top = "-9999px";
                iframe.style.width = "1440px";

                document.body.appendChild(iframe);

                iframe.onload = async function() {
                    try {
                        const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;

                        const mapDiv = iframeDocument.getElementById("map_location");

                        if (mapDiv) {
                            const lat = {{ $listing->lat ?? 41.29834049999999 }};
                            const lng = {{ $listing->lng ?? 2.0800095 }};

                            const staticMapUrl =
                                `https://maps.googleapis.com/maps/api/staticmap?center=${lat},${lng}&zoom=12&size=400x600&markers=color:red%7C${lat},${lng}&key=AIzaSyDF1KUigTPIJOGIoRVEV9u7M9Tyx_Wzxj0`;

                            const staticMapImg = document.createElement("img");
                            staticMapImg.src = staticMapUrl;
                            staticMapImg.style.width = "100%";
                            staticMapImg.style.height = "100%";
                            staticMapImg.style.borderRadius = "20px";
                            staticMapImg.style.display = "block";

                            const originalMapHTML = mapDiv.innerHTML;
                            mapDiv.innerHTML = "";
                            mapDiv.appendChild(staticMapImg);
                        }

                        const canvas = await html2canvas(iframeDocument.body, {
                            scale: 2, // Native resolution to prevent distortion
                            useCORS: true
                        });
                        const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
                        const imgData = canvas.toDataURL(isSafari ? 'image/jpeg' : 'image/webp', 1);
                        // console.log(`Image Data: ${imgData}`);

                        const { jsPDF } = window.jspdf;
                        const pdf = new jsPDF();

                        const imgWidth = canvas.width;
                        const imgHeight = canvas.height;
                        console.log(`Canvas Width: ${imgWidth}, Canvas Height: ${imgHeight}`);

                        const a4Width = 210; // A4 width in mm
                        const a4Height = 297; // A4 height in mm
                        const marginTop = 10;
                        const marginBottom = 10; // Bottom margin in mm
                        const usablePageHeight = a4Height - marginTop - marginBottom; // Adjusted page height
                        const scaleFactor = 0.264583; // Pixels to mm conversion (1 px = 0.264583 mm at 96 DPI)

                        // Calculate image dimensions in mm
                        const imgWidthMM = imgWidth * scaleFactor;
                        const imgHeightMM = imgHeight * scaleFactor;

                        // Fit image to A4 width, maintaining aspect ratio
                        const renderedWidth = a4Width; // Exact A4 width
                        const renderedHeight = (imgHeightMM * a4Width) / imgWidthMM; // Height based on aspect ratio

                        // Calculate number of pages needed
                        const pageHeightMM = usablePageHeight;
                        const pagesNeeded = Math.ceil(renderedHeight / pageHeightMM);

                        for (let page = 0; page < pagesNeeded; page++) {
                            if (page > 0) {
                                pdf.addPage();
                            }

                            // Calculate source rectangle for this page in pixel coordinates
                            const srcHeightPx = Math.min(
                                (pageHeightMM / renderedHeight) * imgHeight, // Height for one page in pixels
                                imgHeight - (page * (pageHeightMM / renderedHeight) * imgHeight)
                            );
                            const srcYPx = page * (pageHeightMM / renderedHeight) * imgHeight;

                            // Create temporary canvas to crop the image
                            const tempCanvas = document.createElement('canvas');
                            tempCanvas.width = imgWidth;
                            tempCanvas.height = srcHeightPx;
                            const tempCtx = tempCanvas.getContext('2d');
                            tempCtx.drawImage(canvas, 0, srcYPx, imgWidth, srcHeightPx, 0, 0, imgWidth, srcHeightPx);

                            const pageImgData = tempCanvas.toDataURL(isSafari ? 'image/jpeg' : 'image/webp', 1);

                            // Add image to PDF, respecting bottom margin
                            pdf.addImage(
                                pageImgData,
                                isSafari ? 'JPEG' : 'WEBP',
                                0, // xPos: Align to left
                                marginTop, // yPos: Start at top
                                renderedWidth, // Width matches A4 width
                                Math.min(pageHeightMM, renderedHeight - (page * pageHeightMM)), // Height for this page
                                null,
                                'SLOW'
                            );
                        }

                        pdf.save('booking_confirmation.pdf');
                        if (spinner) {
                            spinner.classList.add('d-none');
                            downloadText.classList.remove('d-none');
                            this.disabled = false;
                        }
                    } catch (error) {
                        console.error('Error during PDF generation:', error);
                    } finally {
                        document.body.removeChild(iframe);
                    }
                };

                iframe.onerror = function() {
                    console.error('Error loading iframe.');
                    document.body.removeChild(iframe);
                };
            } catch (error) {
                console.error('Error during download PDF process:', error);
            }
        });


    </script>

    <script src="https://cdn.jsdelivr.net/npm/print-area-js@1.0.2/js/jquery.printarea.min.js" type="text/JavaScript">
    </script>
    {{-- <script>
        $(function() {
            $("#download_pdf").on("click", function() {
                var options = {
                    mode: "iframe",
                    popClose: true,
                    standard: "html5",
                    retainAttr: ["id", "class", "style"],
                    extraHead: `
                <style>
                    @page {
                        size: 11in 17in landscape; /* Tabloid size in landscape */
                        margin: 0; /* Minimum margins */
                    }
                    body {
                        margin: 0;
                        padding: 0;
                        width: 100%;
                    }
                    .printableArea {
                        width: 100vw !important;
                        max-width: 100% !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        display: block;
                    }
                    .detail_contact_host_btn, 
                    .report-listing, 
                    .my_booking_details_sec .inner_section_booking_buttons_wrapper { 
                        display: none !important; 
                        visibility: hidden !important;
                        opacity: 0;
                    }
                    .printableArea section {
                        padding: 0;
                    }
                    img {
                        max-width: 100% !important;
                        height: auto !important;
                        display: block;
                    }
                    html, body {
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }
                </style>`
                };

                $("div.printableArea").printArea(options);
            });
        });
    </script> --}}
@endpush
