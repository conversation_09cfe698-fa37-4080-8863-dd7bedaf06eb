<section class="Residence-modal">
    <div class="modal fade" id="car_filter_category" tabindex="-1" aria-labelledby="filterCategoryLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="filterCategoryLabel">{{ translate('advance_filters_modal.filters') }}</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form class="advance_filter">
                    <div class="modal-body">
                        <div class="bg_white">
                            <div class="price-list">
                                <div class="inner-title">
                                    <h6 class="title">{{ translate('advance_filters_modal.price_range') }}</h6>
                                </div>
                                <ul class="icheck-list">
                                    <li>
                                        <label for="car-basis-type-hourly">{{ translate('advance_filters_modal.hourly') }}</label>
                                        <input type="radio" class="check" id="car-basis-type-hourly"
                                            name="basis_type" value="Hourly" data-radio="iradio_flat-yellow">
                                    </li>
                                    <li>
                                        <label for="car-basis-type-daily">{{ translate('advance_filters_modal.daily') }}</label>
                                        <input type="radio" class="check" id="car-basis-type-daily" name="basis_type"
                                            value="Daily" checked data-radio="iradio_flat-yellow">
                                    </li>
                                </ul>
                            </div>
                            <input type="text" class="js-range-slider" name="price_range" value=""
                                data-skin="round" data-type="double" data-min="1" data-max="1000000" data-grid="false" />
                        </div>
                        <div class="bg_white resident-list">
                            <h6 class="title">{{ translate('advance_filters_modal.vehicle_type') }}</h6>
                            <select class="js-example-basic-multiple" name="listing_types[]" multiple="multiple"
                                data-placeholder="{{ translate('advance_filters_modal.select_amenities') }}">
                                {{-- <option value="" disabled>Type Search Field</option> --}}
                                @foreach ($listing_types->where('category_id', 3) as $key => $listing_type)
                                    <option value="{{ $listing_type->id }}">{{ $listing_type->translateOrNew(app()->getLocale())->name ?? '' }}
                                        {{-- {{ ucfirst($category->translateOrNew('en')->display_name) }} --}}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="bg_white">
                            <div class="rooms_parent">
                                <h6 class="title">{{ translate('advance_filters_modal.seats') }}</h6>

                                <div class="guest_wrapper d-flex gap-2 align-items-center justify-content-between">
                                    <button class="minus_btn p-0 " type="button"
                                        onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                        <i class="fa fa-minus" aria-hidden="true"></i>
                                    </button>
                                    <input type="number" name="seats"
                                        class="form-control border-0 text-center m-0 p-0" id="seats" min="0"
                                        max="" value="1" readonly />
                                    <button class="plus_btn p-0" type="button"
                                        onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                        <i class="fa fa-plus" aria-hidden="true"></i>
                                    </button>
                                </div>
                            </div>

                        </div>
                        <div class="bg_white">
                            <h6 class="title">{{ translate('advance_filters_modal.transmission') }}</h6>
                            <select class="js-example-basic-multiple" name="transmission[]" multiple="multiple"
                                data-placeholder="{{ translate('advance_filters_modal.select_transmission') }}">
                                {{-- <option value="" disabled>Type Search Field</option> --}}
                                @php
                                    $listing_transmissions = ['Automatic', 'Manual','CVT','DCT','Semi-Automatic','No Transmission','Other'];
                                @endphp
                                @foreach ($listing_transmissions as $listing_transmission)
                                    <option value="{{ $listing_transmission ?? '' }}">{{ $listing_transmission ?? '' }}
                                    </option>
                                @endforeach
                            </select>

                        </div>
                        <div class="bg_white rules-list">
                            <h6 class="title">{{ translate('advance_filters_modal.engine_type') }}</h6>
                            <select class="js-example-basic-multiple" name="engine_type[]" multiple="multiple"
                                data-placeholder="{{ translate('advance_filters_modal.select_engine') }}">
                                {{-- <option value="" disabled>Type Search Field</option> --}}
                                @php
                                    $listing_engines = ['Gasoline (Petrol)', 'Hybrid', 'Electric', 'Diesel','Hydrogen','No Engine','Other'];
                                @endphp
                                @foreach ($listing_engines as $listing_engine)
                                    <option value="{{ $listing_engine ?? '' }}">{{ $listing_engine ?? '' }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        {{-- <div class="bg_white">
                            <h6 class="title">Air Conditioning</h6>
                            <div class="custom-radio">
                                <input type="radio" class="rating_car_radio_btn" value="yes" id="car_ac_1"
                                    name="ratings" checked>
                                <label for="car_ac_1" class="ms-0">Yes</label>
                                <input type="radio" class="rating_car_radio_btn" value="no" id="car_ac_2"
                                    name="ratings">
                                <label for="car_ac_2">No</label>
                            </div>
                        </div> --}}
                        <div class="bg_white">
                            <h6 class="title">{{ translate('advance_filters_modal.ratings') }}</h6>
                            <div class="custom-radio">
                                <input type="radio" class="rating_car_radio_btn" value="1" id="car_rating_1"
                                    name="ratings">
                                <label for="car_rating_1" class="ms-0">1+</label>
                                <input type="radio" class="rating_car_radio_btn" value="2" id="car_rating_2"
                                    name="ratings">
                                <label for="car_rating_2">2+</label>
                                <input type="radio" class="rating_car_radio_btn" value="3" id="car_rating_3"
                                    name="ratings">
                                <label for="car_rating_3">3+</label>
                                <input type="radio" class="rating_car_radio_btn" value="4" id="car_rating_4"
                                    name="ratings">
                                <label for="car_rating_4">4+</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn yellow-btn adv_apply">{{ translate('advance_filters_modal.apply') }}</button>
                        <button type="button" class="btn white-btn adv_reset">{{ translate('advance_filters_modal.reset') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
