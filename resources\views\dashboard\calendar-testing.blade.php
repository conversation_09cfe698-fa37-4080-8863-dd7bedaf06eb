@extends('layouts.master')

@push('css')
    <style>
        .container-fluid.calendar-container {
            display: flex;
            /* max-width: 1000px; */
            overflow: hidden;
        }

        .names-table {
            flex-shrink: 0;
            /* border-collapse: collapse; */
            border-collapse: separate;
            border-spacing: 8px;
            padding-bottom: 15px;
        }

        .calendar-table {
            /* border-collapse: collapse; */
            flex-grow: 1;
            border-collapse: separate;
            border-spacing: 8px;
            table-layout: fixed;
            width: 100%;
        }

        .scroll-container {
            overflow-x: auto;
            /* scroll-behavior: smooth; */
            /* max-width: 900px; */
        }

        .calendar-cell {
            width: 150px;
            height: 90px;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            text-align: center;
            vertical-align: middle;
        }

        .calendar-cell.selected {
            background-color: #FFCE32;
            color: white;
        }

        .calendar-cell:hover:not(.disabled) {
            background-color: #ffeba8;
        }

        .calendar-cell.disabled {
            background-color: #e5e7eb;
            /* cursor: not-allowed;
                    pointer-events: none; */
        }

        .name-cell {
            width: 200px;
            border: 1px solid #e5e7eb;
            text-align: center;
            padding-left: 8px;
            padding-right: 8px;
            background: white;
            height: 41.15px;
            color: #fff;
            border-radius: 10px;
            /* background-color: #FFCE32; */
            /* background: linear-gradient(rgba(255, 206, 50, 100), rgba(255, 207, 50, 0.479)); */
            font-weight: 500;
            /* border: 2px solid black; */
            font-family: "Poppins";
            display: flex;
            height: 90px;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            position: relative; overflow: hidden;
        }

        .name-cell span:not(.internal_name) {overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; line-clamp: 3; -webkit-box-orient: vertical;}
        .name-cell span.internal_name {overflow: hidden; display: -webkit-box; -webkit-line-clamp: 1; line-clamp: 1; -webkit-box-orient: vertical;}
        .name-cell[style*="background-image:"]:before {content: ""; position: absolute; display: block; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.65);}
        .name-cell:not([style*="background-image:"]) {color: #000}
        .name-cell span {z-index: 10;}

        .date-cell {
            width: 150px;
            border: 1px solid #e5e7eb;
            text-align: center;
            font-weight: bold;
            /* background-color: #FFCE32; */
            font-size: 12px;
        }

        .month-header {
            border: 1px solid #e5e7eb;
            text-align: center;
            font-weight: bold;
            background: #f3f4f6;
        }

        .date-cell,
        .calendar-cell {
            /*padding-inline: 80px;*/
            border-radius: 10px;
        }

        .calendar-cell.booked {
            background-color: #ccffcc;
            cursor: not-allowed;
            opacity: 1;
            font-size: 12px;
            text-align: center;
            padding: 2px;
            white-space: wrap;
            /* overflow: hidden; */
            overflow: visible;
            text-overflow: ellipsis;
            color: black;
            font-family: "Poppins"
        }

        .calendar-cell.blocked:hover:not(.booked) {
            background-color: #ff9999;
        }

        .calendar-cell.blocked.selected {
            background-color: #ff2f2f;
            opacity: 1;
        }

        .calendar-cell .blocked_note { overflow: hidden; display: -webkit-box; -webkit-line-clamp: 1; line-clamp: 1; -webkit-box-orient: vertical; padding-left: 10px; padding-right: 10px;}
        .calendar-cell.blocked:hover span {color: black;}

        .color_key_wrapper {display: flex; gap: 25px; justify-content: flex-end; margin-top: 15px;}
        .color_key_wrapper .key {display: flex; align-items: center; gap: 10px;}
        .color_key_wrapper .key p {margin-bottom: 0; color: black; font-size: 13px; font-family: "Poppins"; font-weight: 500;}
        .color_key_wrapper .color_box {width: 25px; height: 25px; border-radius: 8px;}

        .custom-swal-container .swal2-popup {width: 450px;}
        .custom-swal-container .swal2-popup .swal2-actions .swal2-deny {background-color: var(--primary-color) !important; color: var(--black) !important; border-radius: 7px !important; padding: 13px 40px 13px 41px;}

        [tooltip] {
            position: relative;
        }
 
        [tooltip]::before,
        [tooltip]::after {
            text-transform: none;
            font-size: .9em;
            line-height: 1;
            user-select: none;
            pointer-events: none;
            position: absolute;
            display: none;
            opacity: 0;
        }
 
        [tooltip]::before {
            content: '';
            border: 5px solid transparent;
            z-index: 1001;
        }
 
        [tooltip]::after {
            content: var(--tooltip-content) !important;
            font-family: Helvetica, sans-serif;
            text-align: center;
            min-width: 21em !important;
            max-width: 21em;
            white-space: pre-line !important;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 1ch 1.5ch;
            border-radius: .3ch;
            box-shadow: 0 1em 2em -.5em rgba(0, 0, 0, 0.35);
            background: #333;
            color: #fff;
            z-index: 1000;
            line-height: 1.5 !important;
        }
 
        [tooltip]:hover::before,
        [tooltip]:hover::after {
            display: block;
        }
 
        [tooltip='']::before,
        [tooltip='']::after {
            display: none !important;
        }
 
        [tooltip]:not([flow])::before,
        [tooltip][flow^="up"]::before {
            bottom: 100%;
            border-bottom-width: 0;
            border-top-color: #333;
        }
 
        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::after {
            bottom: calc(100% + 5px);
        }
 
        [tooltip]:not([flow])::before,
        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::before,
        [tooltip][flow^="up"]::after {
            left: 50%;
            transform: translate(-50%, -.5em);
        }
 
        [tooltip][flow^="down"]::before {
            top: 100%;
            border-top-width: 0;
            border-bottom-color: #333;
        }
 
        [tooltip][flow^="down"]::after {
            top: calc(100% + 5px);
        }
 
        [tooltip][flow^="down"]::before,
        [tooltip][flow^="down"]::after {
            left: 50%;
            transform: translate(-50%, .5em);
        }
 
        [tooltip][flow^="left"]::before {
            top: 50%;
            border-right-width: 0;
            border-left-color: #333;
            left: calc(0em - 5px);
            transform: translate(-.5em, -50%);
        }
 
        [tooltip][flow^="left"]::after {
            top: 50%;
            right: calc(100% + 5px);
            transform: translate(-.5em, -50%);
        }
 
        [tooltip][flow^="right"]::before {
            top: 50%;
            border-left-width: 0;
            border-right-color: #333;
            right: calc(0em - 5px);
            transform: translate(.5em, -50%);
        }
 
        [tooltip][flow^="right"]::after {
            top: 50%;
            left: calc(100% + 5px);
            transform: translate(.5em, -50%);
        }
 
        @keyframes tooltips-vert {
            to {
                opacity: .9;
                transform: translate(-50%, 0);
            }
        }
 
        @keyframes tooltips-horz {
            to {
                opacity: .9;
                transform: translate(0, -50%);
            }
        }
 
        [tooltip]:not([flow]):hover::before,
        [tooltip]:not([flow]):hover::after,
        [tooltip][flow^="up"]:hover::before,
        [tooltip][flow^="up"]:hover::after,
        [tooltip][flow^="down"]:hover::before,
        [tooltip][flow^="down"]:hover::after {
            animation: tooltips-vert 300ms ease-out forwards;
        }
 
        [tooltip][flow^="left"]:hover::before,
        [tooltip][flow^="left"]:hover::after,
        [tooltip][flow^="right"]:hover::before,
        [tooltip][flow^="right"]:hover::after {
            animation: tooltips-horz 300ms ease-out forwards;
        }


    </style>
@endpush


@section('content')
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="btn_parent d-flex justify-content-end gap-1 pb-2">
                        <a class="btn_yellow b_radius" id="download_ics_btn" href="{{ route('calendar.download_ics') }}">{{ translate('dashboard_calendar.download_ics_file') }}</a>
                        {{-- <a class="btn_yellow b_radius pink_btn" href="#!" data-toggle="modal" data-target="#ics_url">{{ translate('dashboard_calendar.sync_with_airbnb') }}</a> --}}
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid calendar-container">
            <table id="names-table" class="names-table"></table>
            <div class="scroll-container" id="scroll-container">
                <table id="calendar-table" class="calendar-table"></table>
            </div>            
        </div>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="color_key_wrapper">
                        <div class="key">
                            <div class="color_box" style="background-color: #e5e7eb;"></div>
                            <p>{{ translate('dashboard_calendar.blocked') }}</p>
                        </div>
                        <div class="key">
                            <div class="color_box" style="background-color: #ccffcc;"></div>
                            <p>{{ translate('dashboard_calendar.booked') }}</p>
                        </div>
                        <div class="key">
                            <div class="color_box" style="background-color: #FFCE32;"></div>
                            <p>{{ translate('dashboard_calendar.blocking') }}</p>
                        </div>
                        <div class="key">
                            <div class="color_box" style="background-color: #ff2f2f;"></div>
                            <p>{{ translate('dashboard_calendar.unblocking') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="ics_url" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    {{-- <span class="close" data-dismiss="modal">&times;</span> --}}
                    <h1 class="modal-title" id="exampleModalLabel1">{{ translate('dashboard_calendar.sync_with_airbnb_title') }}</h1>
                    <form method="POST" id="withdraw-form">
                        @csrf
                        <div class="mb-2 form_field_padding">
                            <label for="airbnb_url" class="mb-0">{{ translate('dashboard_calendar.enter_airbnb_url') }}</label>
                            <div class="form_field_padding">
                                <input type="text" min="0" class="form-control cop" name="airbnb_url"
                                    id="airbnb_url" placeholder="{{ translate('dashboard_calendar.paste_url_placeholder') }}">
                            </div>
                            <i class="text-muted">{{ translate('dashboard_calendar.please_note_calendar_synced_every_hour') }}</i>
                        </div>
                        <button type="button" class="btn_yellow mb-4" id="wallet-request-btn">{{ translate('dashboard_calendar.submit') }}</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection


@push('js')
    <script>
    const listings = @json($listings);
    const bookings = {!! json_encode($bookings) !!};
    const initialMonths = 6;
    const maxMonths = 12;
    const currentDate = new Date();
    const minDate = new Date(currentDate);
    const maxDate = new Date(currentDate.getFullYear() + 2, currentDate.getMonth() + 1, 0);
    const disableBeforeDate = new Date(currentDate);
    let currentMonths = [];
    let startCell = null;
    let isHoverSelecting = false;
    let selectionRange = null;
    let selectionMode = 'block'; // 'block' or 'unblock'
    const scrollContainer = document.getElementById("scroll-container");
    let isProgrammaticScroll = false;
    let autoScrollRaf = null;
    
    // ================ reservation data ========================= //
    let blockedDates = new Set();
    @json($reservation_dates).forEach(item => {
        const rowIndex = listings.findIndex(listing => listing.id === item.listing_id);
        if (rowIndex !== -1) {
            const key = `${item.date}:${rowIndex}`;
            blockedDates.add(JSON.stringify({
                date: item.date,
                rowIndex: rowIndex,
                listing_id: item.listing_id,
                note: item.note || 'No note provided'
            }));
        } else {
            console.warn(`Listing with ID ${item.listing_id} not found in listings array`);
        }
    });
    // ================ reservation data end ====================== //
    
    let bookedDates = new Map();
    let foundItem = [];

    bookings.forEach(booking => {
        const listingName = booking.listing.name;
        const rowIndex = listings.findIndex(listing => listing.name === listingName);
        if (rowIndex === -1) {
            console.warn(`Listing "${listingName}" not found in names array`);
            return;
        }

        const checkIn = new Date(booking.check_in);
        const checkOut = new Date(booking.check_out);
        const userName = booking.listing.user.name;
        const bookingNumber = booking.booking_number || 'N/A';

        if (isNaN(checkIn.getTime()) || isNaN(checkOut.getTime())) {
            console.warn(`Invalid dates for booking:`, booking);
            return;
        }

        for (let date = new Date(checkIn); date <= checkOut; date.setDate(date.getDate() + 1)) {
            const dateStr = date.toLocaleDateString("en-CA");
            const key = `${dateStr}:${rowIndex}`;
            const existingBookings = bookedDates.get(key) || [];
            existingBookings.push({
                userName,
                bookingNumber
            });
            bookedDates.set(key, existingBookings);
        }
    });

    for (let i = 0; i < initialMonths; i++) {
        const date = new Date(minDate);
        date.setMonth(minDate.getMonth() + i);
        currentMonths.push({
            date: new Date(date),
            days: null
        });
    }

    function getDaysInMonth(date) {
        return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
    }

    function generateNamesTable() {
        const table = document.getElementById("names-table");
        table.innerHTML = "";
        const thead = document.createElement("thead");
        const tbody = document.createElement("tbody");

        const headerRow = document.createElement("tr");
        const emptyTh = document.createElement("th");
        emptyTh.className = "name-cell";
        emptyTh.innerHTML = "Listing";
        emptyTh.style.fontWeight = "700";
        emptyTh.style.height = "35.88px";
        emptyTh.style.borderRadius = "10px";
        headerRow.appendChild(emptyTh);
        thead.appendChild(headerRow);

        listings.forEach(listing => {
            console.log(listing);
            const tr = document.createElement("tr");
            const td = document.createElement("td");
            td.className = "name-cell";

            if (listing.thumbnail_image && listing.thumbnail_image.url) {
                const imageUrl = `{{ asset('website') }}/${listing.thumbnail_image.url}`;
                td.style.backgroundImage = `url('${imageUrl}')`;
                td.style.backgroundRepeat = "no-repeat";
                td.style.backgroundPosition = "center";
                td.style.backgroundSize = "cover";
            }else{
                const imageUrl = `{{ asset('website') }}/images/dummy.png`;
                td.style.backgroundImage = `url('${imageUrl}')`;
                td.style.backgroundRepeat = "no-repeat";
                td.style.backgroundPosition = "center";
                td.style.backgroundSize = "cover";
            }

            const span = document.createElement('span');
            span.textContent = listing.name || "Listing Name Undefined";

            @if (auth()->user()->hasRole('service'))
                const spanInternalName = document.createElement('span');
                const internalName = listing.internal_name ? ` (${listing.internal_name})` : "";
                spanInternalName.textContent = internalName;
                spanInternalName.style.fontWeight = "400";
                spanInternalName.style.color = "#b5b4b4";
                spanInternalName.className = "internal_name";
            @endif

            td.appendChild(span);

            @if (auth()->user()->hasRole('service'))
                td.appendChild(spanInternalName);
            @endif

            tr.appendChild(td);
            tbody.appendChild(tr);
        });

        table.appendChild(thead);
        table.appendChild(tbody);
    }

    function generateCalendarTable() {
        const table = document.getElementById("calendar-table");
        table.innerHTML = "";
        const thead = document.createElement("thead");
        const tbody = document.createElement("tbody");

        const dateRow = document.createElement("tr");
        const emptyDateTh = document.createElement("th");
        emptyDateTh.style.width = "100px";

        currentMonths.forEach(month => {
            const days = month.days || getDaysInMonth(month.date);
            for (let i = 0; i < days; i++) {
                const date = new Date(month.date);
                date.setDate(date.getDate() + i);

                const day = date.toLocaleString("en-US", {
                    weekday: "long"
                });
                const formattedDate =
                    `${date.getDate()}-${date.toLocaleString("en-US", { month: "long" })}-${date.getFullYear()}`;

                const th = document.createElement("th");
                th.className = "date-cell";
                th.style.width = "180px";
                th.innerHTML = `${day}<br>${formattedDate}`;
                th.dataset.date = date.toISOString();
                dateRow.appendChild(th);
            }
        });

        thead.appendChild(dateRow);

        listings.forEach((listing, rowIndex) => {
            const tr = document.createElement("tr");
            const emptyTd = document.createElement("td");
            currentMonths.forEach(month => {
                const days = month.days || getDaysInMonth(month.date);
                for (let colIndex = 0; colIndex < days; colIndex++) {
                    const date = new Date(month.date);
                    date.setDate(date.getDate() + colIndex);
                    const td = document.createElement("td");
                    td.className = "calendar-cell";
                    td.dataset.row = rowIndex;
                    td.dataset.date = date.toLocaleDateString("en-CA");
                    td.dataset.listing_ids = listing.ids;
                    td.dataset.listing_id = listing.id;
                    const key = `${td.dataset.date}:${rowIndex}`;

                    // Check if the date is blocked by searching blockedDates
                    let blockInfo = null;
                    for (let item of blockedDates) {
                        const parsedItem = JSON.parse(item);
                        if (parsedItem.date === td.dataset.date && parsedItem.rowIndex === rowIndex) {
                            blockInfo = parsedItem;
                            break;
                        }
                    }

                    if (
                        date < disableBeforeDate ||
                        blockInfo ||
                        bookedDates.has(key)
                    ) {
                        td.classList.add("disabled");
                        if (blockInfo) {
                            td.classList.add("blocked");
                            td.innerHTML = `<span class="blocked">{{ translate('dashboard_calendar.blocked') }}</span><br><span class="blocked_note"> <i class="fa fa-sticky-note" aria-hidden="true" style="margin-right: 5px;"></i> ${blockInfo.note}</span>`;
                            td.setAttribute("tooltip", blockInfo.note);
                            td.classList.add("has-tooltip");
                            if (rowIndex <= 1 && colIndex != 0) {
                                td.setAttribute("flow", "down");
                            } else if (rowIndex <= 1 && colIndex === 0) {
                                td.setAttribute("flow", "right");
                            } else if (colIndex === 0) {
                                td.setAttribute("flow", "right");
                            } else {
                                td.setAttribute("flow", "up");
                            }
                            setTimeout(() => {
                                td.style.setProperty('--tooltip-content', `"${blockInfo.note}"`);
                            }, 1500);
                        }
                        if (bookedDates.has(key)) {
                            td.classList.add("booked");
                            const bookings = bookedDates.get(key);
                            const bookingList = Array.isArray(bookings) ? bookings : [bookings];
                            const primaryBooking = bookingList[0];
                            td.innerHTML = `${primaryBooking.userName}<br>(${primaryBooking.bookingNumber})${
                                bookingList.length > 1 ? "<br>(" + bookingList.length + ")" : ""
                            }`;
                            if (bookingList.length > 1) {
                                const tooltipContent = bookingList
                                    .map(b => `${b.userName} (${b.bookingNumber})`)
                                    .join(`\\A `);
                                if (colIndex === 0) {
                                    td.setAttribute("flow", "right");
                                } else if (rowIndex == 0) {
                                    td.setAttribute("flow", "down");
                                }
                                td.setAttribute("tooltip", tooltipContent);
                                td.classList.add("has-tooltip");
                                setTimeout(() => {
                                    td.style.setProperty('--tooltip-content', `"${tooltipContent}"`);
                                }, 1500);
                            }
                        }
                    }
                    tr.appendChild(td);
                }
            });
            tbody.appendChild(tr);
        });

        table.appendChild(thead);
        table.appendChild(tbody);

        const cells = document.querySelectorAll(".calendar-cell");
        cells.forEach(cell => {
            cell.addEventListener("click", handleClick);
            cell.addEventListener("mousemove", handleHover);
        });

        if (selectionRange) {
            restoreSelection();
        }
    }

    function handleClick(e) {
        if (!e.target.classList.contains("calendar-cell")) return;
        e.preventDefault();

        const cell = e.target;
        const isBlocked = cell.classList.contains("blocked");
        const isBooked = cell.classList.contains("booked");

        if (isBooked) {
            console.log('Clicked booked cell, ignoring:', cell.dataset.date);
            return;
        }

        if (startCell) {
            const endCell = cell;
            selectionRange.end = {
                date: new Date(endCell.dataset.date),
                row: parseInt(endCell.dataset.row)
            };
            clearSelection();
            selectRange(startCell, endCell);
            isHoverSelecting = false;
            stopAutoScroll();

            if (selectionMode === 'unblock') {
                const selectedBlockedCells = document.querySelectorAll(".calendar-cell.selected.blocked");
                if (selectedBlockedCells.length === 0) {
                    // No Blocked Dates alert
                    Swal.fire({
                        title: '{{ translate("dashboard_calendar.no_blocked_dates") }}',
                        text: '{{ translate("dashboard_calendar.no_blocked_dates_selected") }}',
                        icon: 'info',
                        timer: 1500,
                        showConfirmButton: false
                    });
                    clearSelection();
                    startCell = null;
                    selectionRange = null;
                    selectionMode = 'block';
                    return;
                }
                
                // Updated unblock confirmation with Edit Note button
                Swal.fire({
                    title: 'Unblock Dates',
                    text: `Do you want to unblock ${selectedBlockedCells.length} date(s)?`,
                    icon: 'question',
                    showDenyButton: true,
                    showCancelButton: true,
                    confirmButtonText: 'Yes, unblock',
                    denyButtonText: 'Edit Note',
                    cancelButtonText: 'No, cancel',
                    buttonsStyling: true,
                    customClass: {
                        container: 'custom-swal-container',
                    },
                }).then((result) => {
                    if (result.isConfirmed) {
                        unblockSelectedDates();
                        Swal.fire({
                            title: 'Unblocked!',
                            text: 'The selected dates have been unblocked.',
                            icon: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        });
                    } else if (result.isDenied) {
                        // Get the first selected blocked cell to show its current note
                        const firstBlockedCell = selectedBlockedCells[0];
                        const existingBlockInfo = [...blockedDates].find(item => {
                            const parsedItem = JSON.parse(item);
                            return parsedItem.date === firstBlockedCell.dataset.date && 
                                   parsedItem.rowIndex === parseInt(firstBlockedCell.dataset.row);
                        });
                        
                        const existingNote = existingBlockInfo ? JSON.parse(existingBlockInfo).note : 'No note provided';
                        
                        // Show edit note dialog
                        Swal.fire({
                            title: 'Edit Note',
                            input: 'textarea',
                            inputValue: existingNote,
                            inputPlaceholder: 'Enter a note',
                            showCancelButton: true,
                            confirmButtonText: 'Update Note',
                            cancelButtonText: 'Cancel',
                            inputValidator: (value) => {
                                if (!value || value.trim() === '') {
                                    return 'Please enter a note';
                                }
                            }
                        }).then((editResult) => {
                            if (editResult.isConfirmed) {
                                updateBlockedDatesNote(editResult.value);
                                Swal.fire({
                                    title: '{{ translate("dashboard_calendar.note_updated") }}',
                                    text: '{{ translate("dashboard_calendar.note_updated_message") }}',
                                    icon: 'success',
                                    timer: 1500,
                                    showConfirmButton: false
                                });
                            }else{
                                clearSelection();
                            }
                        });
                    } else {
                        clearSelection();
                    }
                    startCell = null;
                    selectionRange = null;
                    selectionMode = 'block';
                });
            } else {
                Swal.fire({
                    title: '{{ translate("dashboard_calendar.block_dates") }}',
                    text: '{{ translate("dashboard_calendar.confirm_block") }}',
                    icon: 'warning',
                    input: 'textarea',
                    inputPlaceholder: '{{ translate("dashboard_calendar.enter_note") }}',
                    inputAttributes: {
                        autocapitalize: 'off',
                        autocorrect: 'off'
                    },
                    inputValidator: (value) => {
                        if (!value || value.trim() === '') {
                            return '{{ translate("dashboard_calendar.please_enter_note") }}';
                        }
                    },
                    showCancelButton: true,
                    confirmButtonText: '{{ translate("dashboard_calendar.yes_block") }}',
                    cancelButtonText: '{{ translate("dashboard_calendar.no_cancel") }}',
                    buttonsStyling: true,
                }).then((result) => {
                    if (result.isConfirmed) {
                        const note = result.value;
                        blockSelectedDates(note);
                        $.ajax({
                            url: `{{ route('calendar.block_date') }}`,
                            type: "GET",
                            data: {
                                "start_date": startCell.dataset.date,
                                "end_date": endCell.dataset.date,
                                "user_id": {{ auth()->user()->id }},
                                "listing_ids": window.listingIds,
                                "note": note
                            },
                            success: (response) => {
                                if (response.status == true) {
                                    Swal.fire({
                                        title: '{{ translate("dashboard_calendar.blocked") }}!',
                                        text: '{{ translate("dashboard_calendar.dates_blocked_success") }}',
                                        icon: 'success',
                                        timer: 1500,
                                        showConfirmButton: false
                                    });
                                } else {
                                    Swal.fire({
                                        title: '{{ translate("dashboard_calendar.blocked") }}!',
                                        text: '{{ translate("dashboard_calendar.dates_blocked_success") }}',
                                        icon: 'success',
                                        timer: 1500,
                                        showConfirmButton: false
                                    });
                                }
                            }
                        });
                    } else {
                        clearSelection();
                    }
                    startCell = null;
                    selectionRange = null;
                    selectionMode = 'block';
                });
            }
        } else if (isBlocked && e.shiftKey) {
            selectionMode = 'unblock';
            Swal.fire({
                title: '{{ translate("dashboard_calendar.unblock_date") }}',
                text: '{{ translate("dashboard_calendar.confirm_unblock_single") }}',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '{{ translate("dashboard_calendar.yes_unblock") }}',
                cancelButtonText: '{{ translate("dashboard_calendar.no_cancel") }}',
                buttonsStyling: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    unblockSingleCell(cell);
                    Swal.fire({
                        title: 'Unblocked!',
                        text: 'The date has been unblocked.',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });
                }
                startCell = null;
                selectionMode = 'block';
            });
        } else if (!startCell) {
            selectionMode = isBlocked ? 'unblock' : 'block';
            startCell = cell;
            isHoverSelecting = true;
            selectionRange = {
                start: {
                    date: new Date(cell.dataset.date),
                    row: parseInt(cell.dataset.row)
                }
            };
            clearSelection();
            selectRange(startCell, startCell);
            startAutoScroll(e);
        }
    }

    function updateBlockedDatesNote(newNote) {
        // Get all currently selected blocked cells
        const selectedBlockedCells = document.querySelectorAll(".calendar-cell.selected.blocked");
        
        // If no cells are selected, show error and return
        if (selectedBlockedCells.length === 0) {
            Swal.fire({
                title: 'No Selection',
                text: 'Please select blocked dates to update the note.',
                icon: 'error',
                timer: 2000,
                showConfirmButton: false
            });
            return;
        }

        // Prepare variables for date range and listing IDs
        let minDate = null;
        let maxDate = null;
        const listingIdsSet = new Set();
        const updatedBlockedDates = new Set(blockedDates);

        // Process each selected cell
        selectedBlockedCells.forEach(cell => {
            const cellDate = new Date(cell.dataset.date);
            const cellRow = parseInt(cell.dataset.row);
            
            // Update date range
            if (!minDate || cellDate < minDate) minDate = cellDate;
            if (!maxDate || cellDate > maxDate) maxDate = cellDate;

            // Find and update the existing blocked date
            let foundItem = null;
            blockedDates.forEach(item => {
                const parsedItem = JSON.parse(item);
                if (parsedItem.date === cell.dataset.date && parsedItem.rowIndex === cellRow) {
                    foundItem = item;
                }
            });
            
            if (foundItem) {
                // Remove old entry
                updatedBlockedDates.delete(foundItem);
                
                // Add updated entry
                const blockInfo = {
                    date: cell.dataset.date,
                    rowIndex: cellRow,
                    listing_id: cell.dataset.listing_id,
                    note: newNote
                };
                updatedBlockedDates.add(JSON.stringify(blockInfo));
                
                // Update the cell display
                cell.innerHTML = `<span class="blocked">{{ translate('dashboard_calendar.blocked') }}</span><br><span class="blocked_note"> <i class="fa fa-sticky-note" aria-hidden="true" style="margin-right: 5px;"></i> ${newNote}</span>`;

                cell.setAttribute("tooltip", newNote);
                cell.style.setProperty('--tooltip-content', `"${newNote}"`);
                
                // Collect listing IDs
                const listingIds = cell.dataset.listing_id ? cell.dataset.listing_id.split(',') : [cell.dataset.listing_id];
                listingIds.forEach(id => listingIdsSet.add(id.trim()));
            }
        });

        // Update the global blockedDates
        blockedDates = updatedBlockedDates;
        
        // Send update to server
        const listingIds = Array.from(listingIdsSet);
        $.ajax({
            url: "{{ route('calendar.update_block_date_note') }}",
            type: "POST",
            data: {
                start_date: minDate.toISOString().split('T')[0],
                end_date: maxDate.toISOString().split('T')[0],
                listing_ids: listingIds,
                note: newNote,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (!response.status) {
                    console.error('Failed to update note:', response.message);
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to update note in database',
                        icon: 'error'
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', status, error);
                Swal.fire({
                    title: 'Error',
                    text: 'An error occurred while updating the note',
                    icon: 'error'
                });
            }
        });

        clearSelection();
    }

    function unblockSingleCell(cell) {
        const key = `${cell.dataset.date}:${cell.dataset.row}`;
        const itemToRemove = [...blockedDates].find(item => {
            const parsedItem = JSON.parse(item);
            return parsedItem.date === cell.dataset.date && parsedItem.rowIndex === parseInt(cell.dataset.row);
        });
        if (itemToRemove) {
            blockedDates.delete(itemToRemove);
            cell.classList.remove("blocked", "disabled");
            generateCalendarTable();
        }
    }

    function unblockSelectedDates() {
        if (!selectionRange || !selectionRange.start || !selectionRange.end) {
            console.log('No selection range for unblock');
            return;
        }

        const startDate = new Date(selectionRange.start.date);
        const endDate = new Date(selectionRange.end.date);
        const startRow = selectionRange.start.row;
        const endRow = selectionRange.end.row;

        const minRow = Math.min(startRow, endRow);
        const maxRow = Math.max(startRow, endRow);
        const minDate = new Date(Math.min(startDate, endDate));
        const maxDate = new Date(Math.max(startDate, endDate));

        let unblockedCount = 0;
        const listingIdsSet = new Set();
        const cells = document.querySelectorAll(".calendar-cell.blocked");
        cells.forEach(cell => {
            const cellDate = new Date(cell.dataset.date);
            const cellRow = parseInt(cell.dataset.row);
            if (
                cellDate >= minDate &&
                cellDate <= maxDate &&
                cellRow >= minRow &&
                cellRow <= maxRow
            ) {
                const itemToRemove = [...blockedDates].find(item => {
                    const parsedItem = JSON.parse(item);
                    return parsedItem.date === cell.dataset.date && parsedItem.rowIndex === cellRow;
                });
                if (itemToRemove) {
                    blockedDates.delete(itemToRemove);
                    cell.classList.remove("blocked", "disabled");
                    unblockedCount++;
                    const listingIds = cell.dataset.listing_ids ? cell.dataset.listing_ids.split(',') : [cell.dataset.listing_id];
                    listingIds.forEach(id => {
                        if (id) listingIdsSet.add(id.trim());
                    });
                }
            }
        });

        const listingIds = Array.from(listingIdsSet);

        window.listingIds = listingIds;
        $.ajax({
            url: `{{ route('calendar.unblock_date') }}`,
            type: "GET",
            data: {
                "start_date": minDate.toLocaleDateString("en-CA"),
                "end_date": maxDate.toLocaleDateString("en-CA"),
                "listing_ids": window.listingIds
            },
            success: (response) => {
                if (response.status == true) {
                    Swal.fire({
                        title: 'Unblocked!',
                        text: 'The selected dates have been unblocked in the database.',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire({
                        title: 'Unblocked!',
                        text: 'The selected dates have been unblocked in the database.',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });
                }
            },
            error: (xhr, status, error) => {
                console.error('Unblock AJAX error:', status, error);
                Swal.fire({
                    title: 'Error',
                    text: 'Failed to unblock dates in the database.',
                    icon: 'error',
                    timer: 1500,
                    showConfirmButton: false
                });
            }
        });

        clearSelection();
        generateCalendarTable();
    }

    function blockSelectedDates(note) {
        if (!selectionRange || !selectionRange.start || !selectionRange.end) return;

        const startDate = new Date(selectionRange.start.date);
        const endDate = new Date(selectionRange.end.date);
        const startRow = selectionRange.start.row;
        const endRow = selectionRange.end.row;

        const minRow = Math.min(startRow, endRow);
        const maxRow = Math.max(startRow, endRow);
        const minDate = new Date(Math.min(startDate, endDate));
        const maxDate = new Date(Math.max(startDate, endDate));

        const listingIdsSet = new Set();
        const cells = document.querySelectorAll(".calendar-cell:not(.disabled):not(.blocked):not(.booked)");
        cells.forEach(cell => {
            const cellDate = new Date(cell.dataset.date);
            const cellRow = parseInt(cell.dataset.row);
            if (
                cellDate >= minDate &&
                cellDate <= maxDate &&
                cellRow >= minRow &&
                cellRow <= maxRow
            ) {
                cell.classList.add("disabled", "blocked");
                const blockInfo = {
                    date: cell.dataset.date,
                    rowIndex: cellRow,
                    listing_id: cell.dataset.listing_id,
                    note: note
                };
                blockedDates.add(JSON.stringify(blockInfo));
                const ids = cell.dataset.listing_ids ? cell.dataset.listing_ids.split(',') : [cell.dataset.listing_id];
                ids.forEach(id => listingIdsSet.add(id.trim()));
            }
        });

        window.listingIds = Array.from(listingIdsSet);

        clearSelection();
        generateCalendarTable();
    }

    function handleHover(e) {
        if (!isHoverSelecting || !e.target.classList.contains("calendar-cell")) return;
        if (selectionMode === 'unblock' && !e.target.classList.contains("blocked")) return;

        const endCell = e.target;
        const endDate = new Date(endCell.dataset.date);

        clearSelection();
        selectionRange.end = {
            date: endDate,
            row: parseInt(endCell.dataset.row)
        };
        selectRange(startCell, endCell);

        startAutoScroll(e);
    }

    function startAutoScroll(e) {
        if (!e.target.classList.contains("calendar-cell")) return;
        if (selectionMode === 'unblock' && !e.target.classList.contains("blocked")) return;
        stopAutoScroll();
        const containerRect = scrollContainer.getBoundingClientRect();
        const mouseX = e.clientX;
        const edgeThreshold = 50;
        const scrollSpeed = 200;

        function scrollStep() {
            if (!isHoverSelecting) {
                stopAutoScroll();
                return;
            }

            const scrollLeft = scrollContainer.scrollLeft;
            const scrollWidth = scrollContainer.scrollWidth;
            const clientWidth = scrollContainer.clientWidth;

            if (mouseX < containerRect.left + edgeThreshold) {
                isProgrammaticScroll = true;
                const newScrollLeft = scrollLeft - scrollSpeed;
                scrollContainer.scrollTo({
                    left: newScrollLeft,
                    behavior: "smooth"
                });
                if (newScrollLeft <= 0 && currentMonths[0].date > minDate) {
                    prependMonth(e.target);
                    scrollContainer.scrollLeft += 300;
                }
            } else if (mouseX > containerRect.right - edgeThreshold) {
                isProgrammaticScroll = true;
                const newScrollLeft = scrollLeft + scrollSpeed;
                scrollContainer.scrollTo({
                    left: newScrollLeft,
                    behavior: "smooth"
                });
                if (scrollWidth - newScrollLeft - clientWidth <= 0 && currentMonths[currentMonths.length - 1].date <
                    maxDate) {
                    appendMonth(e.target);
                }
            }
            autoScrollRaf = requestAnimationFrame(scrollStep);
        }

        autoScrollRaf = requestAnimationFrame(scrollStep);
    }

    function stopAutoScroll() {
        if (autoScrollRaf) {
            cancelAnimationFrame(autoScrollRaf);
            autoScrollRaf = null;
            isProgrammaticScroll = false;
        }
    }

    function clearSelection() {
        document.querySelectorAll(".calendar-cell.selected").forEach(cell => {
            cell.classList.remove("selected");
        });
    }

    function selectRange(start, end) {
        if (!start || !end || !start.dataset.date || !end.dataset.date) return;

        const startDate = new Date(start.dataset.date);
        const endDate = new Date(end.dataset.date);
        const startRow = parseInt(start.dataset.row);
        const endRow = parseInt(end.dataset.row);

        const minRow = Math.min(startRow, endRow);
        const maxRow = Math.max(startRow, endRow);
        const minDate = new Date(Math.min(startDate, endDate));
        const maxDate = new Date(Math.max(startDate, endDate));

        const selector = selectionMode === 'unblock' ? ".calendar-cell.blocked" :
            ".calendar-cell:not(.disabled):not(.blocked):not(.booked)";
        const cells = document.querySelectorAll(selector);
        cells.forEach(cell => {
            const cellDate = new Date(cell.dataset.date);
            const cellRow = parseInt(cell.dataset.row);
            if (
                cellDate >= minDate &&
                cellDate <= maxDate &&
                cellRow >= minRow &&
                cellRow <= maxRow
            ) {
                cell.classList.add("selected");
            }
        });
    }

    function restoreSelection() {
        if (!selectionRange || !selectionRange.start || !selectionRange.end) return;

        const selector = selectionMode === 'unblock' ? ".calendar-cell.blocked" :
            ".calendar-cell:not(.disabled):not(.blocked)";
        const cells = document.querySelectorAll(selector);
        cells.forEach(cell => {
            const cellDate = new Date(cell.dataset.date);
            const cellRow = parseInt(cell.dataset.row);
            if (
                cellDate >= selectionRange.start.date &&
                cellDate <= selectionRange.end.date &&
                cellRow >= Math.min(selectionRange.start.row, selectionRange.end.row) &&
                cellRow <= Math.max(selectionRange.start.row, selectionRange.end.row)
            ) {
                cell.classList.add("selected");
            }
        });
    }

    function appendMonth(triggerCell) {
        if (!triggerCell.classList.contains("calendar-cell") || (selectionMode === 'block' && triggerCell.classList
                .contains("disabled") && !triggerCell.classList.contains("blocked")) || (selectionMode === 'unblock' &&
                !triggerCell.classList.contains("blocked"))) return;
        const lastMonth = currentMonths[currentMonths.length - 1];
        const newMonthDate = new Date(lastMonth.date);
        newMonthDate.setMonth(newMonthDate.getMonth() + 1);
        if (newMonthDate > maxDate) return;
        currentMonths.push({
            date: newMonthDate,
            days: null
        });

        if (currentMonths.length > maxMonths) {
            currentMonths.shift();
            scrollContainer.scrollLeft -= 100;
        }

        generateCalendarTable();

        if (isHoverSelecting) {
            const newCells = document.querySelectorAll(selectionMode === 'unblock' ? ".calendar-cell.blocked" :
                ".calendar-cell:not(.disabled):not(.blocked)");
            const newEndCell = Array.from(newCells).find(cell => {
                const cellDate = new Date(cell.dataset.date);
                return cellDate.getDate() === 1 && cellDate.getMonth() === newMonthDate.getMonth() && cell
                    .dataset.row === triggerCell.dataset.row;
            });
            if (newEndCell) {
                selectionRange.end = {
                    date: new Date(newEndCell.dataset.date),
                    row: parseInt(newEndCell.dataset.row)
                };
                selectRange(startCell, newEndCell);
                scrollToCell(newEndCell);
            }
        }
    }

    function prependMonth(triggerCell) {
        if (!triggerCell.classList.contains("calendar-cell") || (selectionMode === 'block' && triggerCell.classList
                .contains("disabled") && !triggerCell.classList.contains("blocked")) || (selectionMode === 'unblock' &&
                !triggerCell.classList.contains("blocked"))) return;
        const firstMonth = currentMonths[0];
        const newMonthDate = new Date(firstMonth.date);
        newMonthDate.setMonth(newMonthDate.getMonth() - 1);
        if (newMonthDate < minDate) return;
        currentMonths.unshift({
            date: newMonthDate,
            days: null
        });

        if (currentMonths.length > maxMonths) {
            currentMonths.pop();
        }

        generateCalendarTable();

        if (isHoverSelecting) {
            const newCells = document.querySelectorAll(selectionMode === 'unblock' ? ".calendar-cell.blocked" :
                ".calendar-cell:not(.disabled):not(.blocked)");
            const daysInNewMonth = getDaysInMonth(newMonthDate);
            const newEndCell = Array.from(newCells).find(cell => {
                const cellDate = new Date(cell.dataset.date);
                return cellDate.getDate() === daysInNewMonth && cellDate.getMonth() === newMonthDate.getMonth() &&
                    cell.dataset.row === triggerCell.dataset.row;
            });
            if (newEndCell) {
                selectionRange.end = {
                    date: new Date(newEndCell.dataset.date),
                    row: parseInt(newEndCell.dataset.row)
                };
                selectRange(startCell, newEndCell);
                scrollToCell(newEndCell);
            }
        }
    }

    function scrollToCell(cell) {
        const cellRect = cell.getBoundingClientRect();
        const containerRect = scrollContainer.getBoundingClientRect();
        const scrollLeft = scrollContainer.scrollLeft + cellRect.left - containerRect.left - 100;
        isProgrammaticScroll = true;
        scrollContainer.scrollTo({
            left: scrollLeft,
            behavior: "smooth"
        });
        setTimeout(() => {
            isProgrammaticScroll = false;
        }, 500);
    }

    scrollContainer.addEventListener("scroll", () => {
        if (isProgrammaticScroll) return;
        const {
            scrollLeft,
            scrollWidth,
            clientWidth
        } = scrollContainer;
        const threshold = 100;

        if (scrollLeft < threshold && currentMonths[0].date > minDate) {
            prependMonth(document.querySelector(selectionMode === 'unblock' ? ".calendar-cell.blocked" :
                ".calendar-cell:not(.disabled):not(.blocked)"));
            scrollContainer.scrollLeft += 100;
        } else if (scrollWidth - scrollLeft - clientWidth < threshold && currentMonths[currentMonths.length - 1]
            .date < maxDate) {
            appendMonth(document.querySelector(selectionMode === 'unblock' ? ".calendar-cell.blocked" :
                ".calendar-cell:not(.disabled):not(.blocked)"));
        }
    });

    scrollContainer.addEventListener("wheel", (e) => {
        if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) {
            e.preventDefault();
            const scrollAmount = e.deltaX * 5;
            isProgrammaticScroll = true;
            scrollContainer.scrollTo({
                left: scrollContainer.scrollLeft + scrollAmount,
            });
            setTimeout(() => {
                isProgrammaticScroll = false;
            }, 500);
        }
    });

    generateNamesTable();
    generateCalendarTable();

    document.addEventListener("mouseup", () => {
        isHoverSelecting = false;
        stopAutoScroll();
    });

    document.getElementById("calendar-table").addEventListener("dragstart", (e) => e.preventDefault());
// code for calendar .ics file download 
    // document.getElementById('download_ics_btn').addEventListener('click', function() {
    //     // Generate ICS content
    //     let icsContent = "BEGIN:VCALENDAR\r\n";
    //     icsContent += "VERSION:2.0\r\n";
    //     icsContent += "PRODID:-//YourCompany//Calendar App//EN\r\n";
    //     icsContent += "CALSCALE:GREGORIAN\r\n";
    //     icsContent += "METHOD:PUBLISH\r\n";
        
    //     // Add blocked dates to ICS
    //     blockedDates.forEach(item => {
    //         const blockInfo = JSON.parse(item);
    //         const listing = listings[blockInfo.rowIndex];
    //         const listingName = listing ? listing.name : 'Unknown Listing';
            
    //         icsContent += "BEGIN:VEVENT\r\n";
    //         icsContent += "UID:" + Math.random().toString(36).substring(2) + "@yourcompany.com\r\n";
    //         icsContent += "DTSTAMP:" + new Date().toISOString().replace(/[-:]/g, '').replace(/\.\d+/g, '') + "\r\n";
    //         icsContent += "DTSTART;VALUE=DATE:" + blockInfo.date.replace(/-/g, '') + "\r\n";
            
    //         // End date is start date + 1 day
    //         const endDate = new Date(blockInfo.date);
    //         endDate.setDate(endDate.getDate() + 1);
    //         const formattedEndDate = endDate.toISOString().split('T')[0].replace(/-/g, '');
            
    //         icsContent += "DTEND;VALUE=DATE:" + formattedEndDate + "\r\n";
    //         icsContent += "SUMMARY:BLOCKED - " + listingName + "\r\n";
    //         icsContent += "DESCRIPTION:" + (blockInfo.note || 'Blocked date') + "\r\n";
    //         icsContent += "STATUS:CONFIRMED\r\n";
    //         icsContent += "END:VEVENT\r\n";
    //     });
        
    //     // Add bookings to ICS
    //     bookedDates.forEach((bookingList, key) => {
    //         const [dateStr, rowIndex] = key.split(':');
    //         const listing = listings[rowIndex];
    //         const listingName = listing ? listing.name : 'Unknown Listing';
            
    //         bookingList.forEach(booking => {
    //             icsContent += "BEGIN:VEVENT\r\n";
    //             icsContent += "UID:" + booking.bookingNumber + "@yourcompany.com\r\n";
    //             icsContent += "DTSTAMP:" + new Date().toISOString().replace(/[-:]/g, '').replace(/\.\d+/g, '') + "\r\n";
    //             icsContent += "DTSTART;VALUE=DATE:" + dateStr.replace(/-/g, '') + "\r\n";
                
    //             // End date is start date + 1 day
    //             const endDate = new Date(dateStr);
    //             endDate.setDate(endDate.getDate() + 1);
    //             const formattedEndDate = endDate.toISOString().split('T')[0].replace(/-/g, '');
                
    //             icsContent += "DTEND;VALUE=DATE:" + formattedEndDate + "\r\n";
    //             icsContent += "SUMMARY:BOOKED - " + listingName + "\r\n";
    //             icsContent += "DESCRIPTION:Booking #" + booking.bookingNumber + "\r\n";
    //             icsContent += "STATUS:CONFIRMED\r\n";
    //             icsContent += "END:VEVENT\r\n";
    //         });
    //     });
        
    //     icsContent += "END:VCALENDAR";
        
    //     // Create and download the file
    //     const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
    //     const link = document.createElement('a');
    //     link.href = URL.createObjectURL(blob);
    //     link.download = 'calendar.ics';
    //     document.body.appendChild(link);
    //     link.click();
    //     document.body.removeChild(link);
    // });
    // end  code for calendar .ics file download 
</script>

<script>
    $(document).on('click', 'span.blocked_note, span.blocked', function() {
        $(this).closest('.calendar-cell').click();
    });
</script>

@endpush
