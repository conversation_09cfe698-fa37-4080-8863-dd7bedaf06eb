@extends('layouts.master')
@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
@endpush
@section('content')
    <section class="listing_type_index_sec">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12">
                    <div class="panel panel-default">
                        {{-- <div class="panel-heading"> Nav Pills Tabs</div> --}}
                        <div class="panel-body">
                            <ul class="nav nav-pills m-b-30 ">
                                <li class="active"><a href="#category_types" data-toggle="tab" aria-expanded="false">{{ translate('content_management_system.types') }}</a></li>
                                <li class=""><a href="#category_cms" data-toggle="tab" aria-expanded="false">{{ translate('content_management_system.cms') }}</a></li>
                            </ul>
                            <div class="tab-content br-n pn">
                                <div id="category_types" class="tab-pane active">
                                    <div class="white-box">
                                        <div class="d-flex justify-content-between">
                                            <h3 class="box-title">
                                                Types of {{ $category->display_name }}
                                            </h3>
                                            <div class="d-flex">
                                                <div class="nav_search main me-2">
                                                    <!-- Actual search box -->
                                                    <div class="form-group has-feedback has-search m-0">
                                                        <form class="example" action="/action_page.php" style="width: 100%">
                                                            <button type="button"><i class="fa fa-search"></i></button>
                                                            <input type="text" placeholder="{{ translate('content_management_system.search') }}.." id="searchBar" class="searchBar"
                                                                name="search">
                                                        </form>
                                                    </div>
                                                </div>
                                                <a class="btn btn_trans topbar me-2" href="{{ url('cms') }}#categories_pane">{{ translate('content_management_system.back') }}</a>
                    
                    
                                                @can('add-' . str_slug('AmenityOption'))
                                                    <a class="btn btn_yellow"
                                                        href="{{ route('listing-type.create', ['category_id' => $category->ids]) }}">
                                                        {{ __('add') }} {{ translate('content_management_system.type') }}
                                                    </a>
                                                @endcan
                                            </div>
                                        </div>
                                        <div class="clearfix"></div>
                                        <hr>
                                        <div class="table-responsive">
                                            <table class="table" id="myTable">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        {{-- <th>{{ __('Listing Type') }}</th> --}}
                                                        <th>{{ translate('content_management_system.image') }}</th>
                                                        <th> {{ translate('content_management_system.type') }} {{ (translate('content_management_system.in_eng')) }}</th>
                                                        <th> {{ translate('content_management_system.type') }} {{ (translate('content_management_system.in_spanish')) }}</th>
                                                        <th>{{ __('actions') }}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @forelse ($listingTypes as $type)
                                                        <tr>
                                                            <td>{{ $loop->iteration ?? $type->id }}</td>
                                                            <td><img src="{{ asset('website') . '/' . $type->image ?? 'images/snowflake.png' }}"
                                                                    height="30px" width="30px" alt="{{ translate('content_management_system.type_icon') }}"></td>
                                                            <td>{{ $type->translate('en')->name }}</td>
                                                            <td>{{ $type->translate('es')->name }}</td>
                                                            <td class="form_btn ">
                                                                <div class="dropdown">
                                                                    <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                        <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                                    </button>
                                                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                        {{-- @can('view-' . str_slug('AmenityOption'))
                                                                            <a href="#!" data-toggle="modal" data-target="#amenityDetail"
                                                                                title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}">
                                                                                <button class="dropdown-item view-detail"
                                                                                    data-amenity-id="{{ $item->id }}">
                                                                                    View
                                                                                </button>
                                                                            </a>
                                                                        @endcan --}}
                                                                        {{-- @can('edit-' . str_slug('AmenityOption')) --}}
                                                                        <a href="{{ route('listing-type.edit', ['category_id' => $category->ids, 'listing_type' => $type->id]) }}"
                                                                            title="Edit ">
                                                                            <button class="dropdown-item">
                                                                                {{ translate('content_management_system.edit') }}
                                                                            </button>
                                                                        </a>
                                                                        {{-- @endcan --}}
                                                                        {{-- @can('delete-' . str_slug('AmenityOption')) --}}
                                                                        <form method="POST"
                                                                            action="{{ route('listing-type.destroy', ['category_id' => $category->ids, 'listing_type' => $type->id]) }}"
                                                                            accept-charset="UTF-8" style="display:inline">
                                                                            {{ method_field('DELETE') }}
                                                                            {{ csrf_field() }}
                                                                            <button type="submit" class="dropdown-item" title="Delete "
                                                                                onclick="return confirm(&quot;Confirm delete?&quot;)">
                                                                                {{ translate('content_management_system.delete') }}
                                                                            </button>
                                                                        </form>
                                                                        {{-- @endcan --}}
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    @empty
                                                        <tr>
                                                            <td colspan="10" style="text-align: center">{{ translate('content_management_system.no_listing_types_in_this_category') }}</td>
                                                        </tr>
                                                    @endforelse
                                                </tbody>
                                            </table>
                                            {{-- <div class="pagination-wrapper"> {!! $amenityoption->appends(['search' => Request::get('search')])->render() !!} </div> --}}
                                        </div>
                                    </div>
                                </div>
                                <div id="category_cms" class="tab-pane">
                                    <div class="white-box">
                                        <h3 class="box-title pull-left">{{ $category->display_name }} CMS</h3>
                                        {{-- @can('view-'.str_slug('Category')) --}}
                                        {{-- <a  class="btn btn_yellow pull-right" href="{{url('listing-cms')}}"> Back</a> --}}
                                        <a class="btn btn_trans pull-right" href="{{ url('cms') }}#categories_pane">
                                            {{ translate('content_management_system.back') }}
                                        </a>
                                        {{-- @endcan --}}
                    
                                        <div class="clearfix"></div>
                                        <hr>
                                        @if ($errors->any())
                                            <ul class="alert alert-danger">
                                                @foreach ($errors->all() as $error)
                                                    <li>{{ $error }}</li>
                                                @endforeach
                                            </ul>
                                        @endif
                                        <form method="POST" action="{{ route('listing_cms_update', ['category_id' => $category->ids]) }}" accept-charset="UTF-8"
                                            class="form-horizontal" enctype="multipart/form-data">
                                            {{ csrf_field() }}
                                            @if($category->id == '1')
                                                @include('listingCMS.form-exp')
                                            @elseif($category->id == '2')
                                                @include('listingCMS.form-watercraft')
                                            @elseif($category->id == '3')
                                                @include('listingCMS.form-vehicle')
                                            @elseif($category->id == '4')
                                                @include('listingCMS.form-accommodation')
                                            @endif
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>
    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    {{-- <script>
        $(document).ready(function() {
            @if (\Session::has('message'))
                $.toast({
                    heading: 'Success!',
                    position: 'top-center',
                    text: '{{ session()->get('message') }}',
                    loaderBg: '#ff6849',
                    icon: 'success',
                    hideAfter: 3000,
                    stack: 6
                });
            @endif
        })
        $(function() {
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }]
            });
        });
        $(document).on("click", ".view-detail", function() {
            var amenityID = $(this).data('amenity-id');
            $.ajax({
                url: "{{ url($amenity->ids . '/amenity-option') }}" + '/' + amenityID,
                type: 'GET',
                success: function(response) {
                    if (response.status) {
                        var data = response.data;
                        // Set default values
                        var nameEn = "N/A";
                        var nameEs = "N/A";
                        var descEn = "N/A";
                        var descEs = "N/A";
                        // Extract translations
                        if (data.translations && data.translations.length > 0) {
                            data.translations.forEach(function(translation) {
                                if (translation.locale === 'en') {
                                    nameEn = translation.name;
                                    descEn = translation.description;
                                }
                                if (translation.locale === 'es') {
                                    nameEs = translation.name;
                                    descEs = translation.description;
                                }
                            });
                        }
                        // Populate modal fields
                        $('#amenityDetail').modal('show');
                        $('#name .info').html(nameEn);
                        $('#amenityNameSpanish .info').html(nameEs);
                        $('#desc_eng .info').html(descEn);
                        $('#desc_spanish .info').html(descEs);
                        $('#amenityDetail .loader').addClass('d-none');
                        $('#amenityDetail .mod_cust_text').removeClass('d-none');
                        // Set image with a fallback
                        var imagePath = data.image ? "{{ asset('website') }}/" + data.image :
                            "{{ asset('website/images/plcaeholderListingImg.png') }}";
                        $('.amen_img img').attr('src', imagePath);
                    } else {
                        alert("Error: Data not found.");
                    }
                },
                error: function(xhr) {
                    console.error("Error fetching amenity details:", xhr);
                }
            });
        });
    </script> --}}
    <script>
         $(document).ready(function() {
            function showTabByHash() {
                var currentHash = window.location.hash || '#category_types';
                var $targetTab = $('.nav-pills a[href="' + currentHash + '"]');
                if ($targetTab.length > 0) {
                    $('.nav-pills li').removeClass('active');
                    $('.tab-pane').removeClass('active show in');
                    $targetTab.closest('li').addClass('active');
                    $(currentHash).addClass('active show in');
                    sessionStorage.setItem('activeTab', currentHash);
                    setTimeout(function() {
                        sessionStorage.removeItem('activeTab');
                    }, 3000);
                }
                $('html, body').animate({
                    scrollTop: 0
                }, 0);
            }
            var activeTab = sessionStorage.getItem('activeTab');
            if (activeTab) {
                window.location.hash = activeTab;
                showTabByHash();
            } else {
                showTabByHash();
            }
            $(window).on('hashchange', showTabByHash);
            $('.nav-pills a').on('click', function(e) {
                e.preventDefault();
                window.location.hash = this.hash;
                showTabByHash();
            });
            $('form').on('submit', function(e) {
                var currentHash = window.location.hash || '#category_types';
                window.location.hash = currentHash;
                showTabByHash();
            });
        });
    </script>
@endpush
