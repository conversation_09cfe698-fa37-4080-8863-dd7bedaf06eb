<div class="col-md-12 listing_custom_meta divider listing_data">
    <ul class="list-unstyled d-flex gap-3 m-0 parent-box">
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/car-front.svg') }}" alt="" height="20px" width="20px">
            <span> {{ $listing->detail->seats }} {{ translate('listing_details.seats') }}</span>
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/car-transmission.png') }}" alt="" height="20px" width="20px">
            <span>{{ $listing->detail->transmission ?? '-' }}</span>
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/car-engine.png') }}" alt="" height="20px" width="25px">
            <span>{{ $listing->detail->engine_type ?? '-' }}</span>
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px" width="20px">
            @if ($listing->detail->pet == 'yes')
                {{ translate('listing_details.pets_allowed') }}
            @else
                {{ translate('listing_details.pets_not_allowed') }}
            @endif
        </li>
    </ul>
</div>
{{-- Key and feature --}}
    @if (isset($listing->key_features[0]))
        <div class="col-lg-12 divider listing_data listing_key_feature">
            <div class="key_features">
                <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.key_features') }}</h3>
                <div class="parent-feature parent-box row g-0 gap-3 align-items-start">
                    @foreach ($listing->key_features as $key_feature)
                        <div class="box col-md-3" data-aos="fade">
                            <h6 class="fs-16 semi-bold">{{ $key_feature->title }}</h6>
                            <p class="fs-12">{!! $key_feature->description !!}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif
{{-- End Key and feature --}}
{{-- Include and not include for Car --}}
@if (isset($listing->includes[0]))
<div class="col-lg-12 divider listing_data listing_include">
    <div class="amenities-box">
        <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.whats_included') }}</h3>
        <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
            @forelse ($listing->includes as $include)
                <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                    <img src="{{ asset('website/images/square-check.svg') }}" height="20px" width="20px"
                        alt="">
                    <span>{{ $include->name }}</span>
                </div>
            @empty
                <p>{{ translate('listing_details.no_include_added') }}</p>
            @endforelse
        </div>
    </div>
</div>
@endif
@if (isset($listing->not_includes[0])) 
<div class="col-lg-12 divider listing_data listing_not_include">
    <div class="amenities-box">
        <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.not_included') }}</h3>
        <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
            @forelse ($listing->not_includes as $not_include)
            <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                <img src="{{ asset('website/images/ticksquare.svg') }}" alt="" height="20px"
                    width="20px">
                <span>{{ $not_include->name }}</span>
            </div>
            @empty
                <p>{{ translate('listing_details.no_not_include_added') }}</p>
            @endforelse
        </div>
    </div>
</div>
@endif
{{-- End Include and not include Car --}}
