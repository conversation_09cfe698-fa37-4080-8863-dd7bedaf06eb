{{-- <fieldset class="review_details_step">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>Let’s Finalize Your Watercraft </h2>
                    </div>
                    <div class="step_description">
                        <p>Here’s what your guests will see. Take a moment to ensure
                            everything is accurate and ready to go live.</p>
                    </div>

                    <div class="row">
                        <div class="col-md-5 col_left">
                            <div class="inner_section_left_col">
                                <div class="review_card_wrapper">
                                    <div class="cover_image">
                                        <img src="{{asset('website')}}/images/product5.png" alt="">
                                    </div>
                                    <div class="review_card_body">
                                        <div class="title_new_wrapper">
                                            <div class="review_title">
                                                <h6>Title</h6>
                                            </div>
                                            <div class="new_label">
                                                <span>New <i class="bi bi-star-fill"></i></span>
                                            </div>
                                        </div>
                                        <div class="review_price">
                                            <span>$45661</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-7 col_right">
                            <div class="inner_section_right_col">
                                <div class="whats_next_title">
                                    <h2>What's Next?</h2>
                                </div>
                                <div class="icon_confirm_details_wrapper">
                                    <div class="edit_icon">
                                        <img src="{{asset('website')}}/images/edit-icon.png" alt="">
                                    </div>
                                    <div class="confirm_detials_content_wrapper">
                                        <div class="confirmation_title">
                                            <h6>Confirm few details and publish</h6>
                                        </div>
                                        <div class="confirmation_description">
                                            <p>We’ll let you know if you need to verify your identity or register with the local government.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="Next" />
    <input type="button" name="previous" class="previous action-button-previous" value="Back" />
</fieldset>


@push('js')

<script>


</script>

@endpush --}}




<fieldset class="review_details_step text-start" id="">
    <div class="inner_section_fieldset h_100">
        {{-- <iframe style="width: 100%; height: 100vh;" src="{{route('detail_slide_iframe')}}" frameborder="0"></iframe> --}}

    </div>
    <input type="button" name="submit" data-status="0" class="save_and_exit_btn action-button btn button1"
        value="{{ translate('stepper.publish') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>

@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "review");
@endphp
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <script>
        $(document).ready(function() {
            function generateLoader() {
                return `
            <div id="list_preview_loader">
                <div class="inner_section_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    @isset($step_data->sub_title)
                        <div class="step_description">
                            <p>{{ $step_data->sub_title }}</p>
                        </div>
                    @endisset
                </div>
                <div class="load">
                    <img src="{{ asset('website') }}/images/luxustar-loader.gif" alt="Loader">
                </div>
                <p>{{ translate('stepper.generating_preview') }}</p>
            </div>
        `;
            }

            function loadData() {
                var id = $('input[name="listing_id"]').val();
                var category = 2;
                $.ajax({
                    type: 'GET',
                    url: `{{ url('detail-slide') }}/${id}/${category}`,
                    data: {
                        id: id,
                        category: category
                    },
                    success: function(data) {
                        setTimeout(() => {
                            if (data) {
                                $(".review_details_step .inner_section_fieldset").html(data);
                            } else {
                                console.log('Error: No view received.');
                            }
                        }, 4500);
                    },
                    error: function() {
                        swal("Error!", @json(translate('stepper.please_select_a_valid_view')));
                    }
                });
            }

            function setLoaderAndFetchData() {
                $(".review_details_step .inner_section_fieldset").html(generateLoader());
                loadData();
            }

            setLoaderAndFetchData();

            $('.final_next_btn').click(function() {
                setLoaderAndFetchData();
            });
        });
    </script>
@endpush
