@extends('layouts.master')
@section('content')
<section class="create_amenity_option_sec update_amenity_option_sec">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="white-box">
                    {{-- <h3 class="box-title pull-left">{{ __('edit') }}
                        {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}
                        #{{ $amenityoption->id }}</h3> --}}
                        <h3 class="box-title pull-left">{{ __('edit') }} {{$amenityoption->name ?? ''}}</h3>
                    @can('view-' . str_slug('AmenityOption'))
                        <a class="btn btn_yellow pull-right" href="{{ route('amenity-option.index', ["amenity_id" => $selected_amenity->ids]) }}">
                            {{-- <i class="icon-arrow-left-circle" aria-hidden="true"></i>  --}}
                            {{ translate('content_management_system.back')  }}</a>
                    @endcan
                    <div class="clearfix"></div>
                    <hr>
                    @if ($errors->any())
                        <ul class="alert alert-danger">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif

                    <form method="POST"
                        action="{{ route('amenity-option.update', ['amenity_id' => $selected_amenity->ids, 'amenity_option' => $amenityoption->id]) }}"
                        accept-charset="UTF-8" class="form-horizontal" enctype="multipart/form-data">
                        {{ method_field('PATCH') }}
                        {{ csrf_field() }}

                        @include ('amenityOption.amenity-option.form', ['submitButtonText' => 'Update'])

                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
