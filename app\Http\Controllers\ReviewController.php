<?php

namespace App\Http\Controllers;

use App\Booking;
use App\Http\Controllers\Controller;
use App\Listing;
use App\Models\ReviewImage;
use App\Models\ReviewReply;
use App\Review;
use App\Services\ReviewService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ReviewController extends Controller
{
    protected $reviewService;
    function __construct(ReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    function review_get(Request $request)
    {
        try {
            $listing = Listing::where("ids", $request->listing_id)->first();
            if ($listing) {
                $reviews = $this->reviewService->getListingReviews($listing->id);
                if (!$reviews->isEmpty()) {
                    $data = (string) view("website.template.user_review", compact("reviews"));
                    return api_response(true, "Review found", [
                        "review_count" => count($reviews),
                        "review_data" => $data
                    ]);
                } else {
                    return api_response(false, "Review not found", []);
                }
            } else {
                return api_response(false, "Listing not found", []);
            }
        } catch (\Throwable $e) {
            return api_response(false, "Something Went Wrong", []);
        }
    }
    function review_update(Request $request)
    {
        $request->validate([
            "review_id" => "required|exists:reviews,id",
            "images.*" => "image|mimes:jpeg,png,jpg|max:10240",
            "rating" => 'required|numeric',
            "communication" => "required",
            "cleanliness" => "required",
            "features" => "required",
            "value_of_money" => "required",
            "accuracy_of_description" => "required",
            "comment" => "nullable|string",
        ]);

        $review_images = ReviewImage::where("review_id", $request->review_id)->count();
        if($request->hasFile('images')){
            $total_images = $review_images + count($request->images);
            if($total_images > 5){
                return back()->with(["message" => "Total 5 images are allowed", "type" => "error", "title" => "Error"]);
            }

        }

        $request_data = $request->all();
        $request_data['images'] = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $request_data['images'][] = $this->storeImageToWebP('review-image', $image);
            }
        }
        $reviewService = $this->reviewService->update($request_data, $request->review_id);
        if ($reviewService["status"] == false) {
            return back()->with(["message" => $reviewService["message"], "type" => "error", "title" => "Error"]);
        }
        return back()->with(["message" => "Review Updated Successfully", "type" => "success", "title" => "Success!"]);
    }
    function delete_review_reply($id)
    {
        if (auth()->user()->hasRole("user")) {
            $review_reply = ReviewReply::where("id", $id)->first();
        } else {
            // $review_reply = ReviewReply::where("id", $id)->where("user_id", auth()->id())->first();
        }
        if (!$review_reply) {
            return back()->with([
                'message' => 'Review reply not found',
                'type' => 'error',
                'title' => 'Error'
            ]);
        }
        $review_reply->delete();
        return back()->with([
            'message' => 'Review reply deleted successfully',
            'type' => 'success',
            'title' => 'Success!'
        ]);
    }
    function review_reply(Request $request, $id)
    {
        $request->validate([
            'reply' => 'required',
        ]);

        if (auth()->user()->hasRole("user")) {
            $review = Review::with('reply')->where('id', $id)->first();
        } else {
            $review = Review::with('reply')->where('provider_id', auth()->id())->where('id', $id)->first();
        }

        if (!$review) {
            if ($request->ajax()) {
                return api_response(false, "Review not found");
            }
            return back()->with([
                'message' => 'Review not found',
                'type' => 'error',
                'title' => 'Error'
            ]);
        }

        $review_reply = $review->reply;
        if ($review_reply) {
            if (!auth()->user()->hasRole("user")) {
                if ($review_reply->is_updated == 1) {
                    if ($request->ajax()) {
                        return api_response(false, "You can only update your reply once.");
                    }
                    return back()->with([
                        'message' => 'You can only update your reply once.',
                        'type' => 'error',
                        'title' => 'Update Restricted'
                    ]);
                }
                $review_reply->is_updated = 1;
            }
            $review_reply->reply = $request->reply;
        } else {
            $review_reply = new ReviewReply();
            $review_reply->user_id = auth()->id();
            $review_reply->review_id = $id;
            $review_reply->reply = $request->reply;
            $review_reply->is_updated = 0;
        }

        $review_reply->save();

        if ($request->ajax()) {
            return api_response(true, "Reply posted successfully", $review_reply);
        }

        return back()->with([
            'message' => 'Reply posted successfully',
            'type' => 'success',
            'title' => 'Success!',
        ]);
    }
    /**
     * Get listings for a specific service provider
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    function getProviderListings(Request $request)
    {
        $providerIds = $request->get('provider_ids', []);
        $user = auth()->user();

        // First filter by user role
        if ($user->hasRole(['user', 'sub_admin'])) {
            // For user and sub_admin roles, get all listings or filter by provider IDs
            if (empty($providerIds)) {
                $listings = Listing::get();
            } else {
                $listings = Listing::whereIn('user_id', $providerIds)->get();
            }
        } elseif ($user->hasRole('service')) {
            // For service role, only get listings owned by the current user
            $listings = Listing::where('user_id', $user->id)->get();
        } else {
            // Default fallback - get all listings or filter by provider IDs
            if (empty($providerIds)) {
                $listings = Listing::get();
            } else {
                $listings = Listing::whereIn('user_id', $providerIds)->get();
            }
        }

        return response()->json([
            'status' => true,
            'listings' => $listings->map(function($listing) {
                return [
                    'id' => $listing->id,
                    'name' => $listing->name
                ];
            })
        ]);
    }

    function review_filter(Request $request)
    {
        try {
            $request_data = $request->all();
            $keyword = $request->get('search');
            $dateRange = $request->get('date');
            $perPage = 15;
            $startDate = null;
            $endDate = null;

            // Handle date range parsing with proper error handling
            if ($dateRange && trim($dateRange) !== '') {
                try {
                    [$startDate, $endDate] = explode(' - ', $dateRange);
                    $startDate = \Carbon\Carbon::createFromFormat('m/d/Y', trim($startDate))->startOfDay()->toDateTimeString();
                    $endDate = \Carbon\Carbon::createFromFormat('m/d/Y', trim($endDate))->endOfDay()->toDateTimeString();
                } catch (\Exception $e) {
                    // If date parsing fails, ignore the date filter
                    $startDate = null;
                    $endDate = null;
                }
            }

            // Build the query with proper relationships
            $review = Review::query()->with(['booking.customer', 'user', 'listing', 'provider'])
                ->withCount('reply')
                ->whereHas('listing', function($q) {
                    // Only include reviews where the listing exists and is not deleted
                    $q->whereNull('deleted_at');
                });

            // Apply keyword search filter
            if (!empty($keyword)) {
                $review->where(function ($query) use ($keyword) {
                    $query->where('comment', 'LIKE', "%$keyword%")
                        ->orWhere('rating', "$keyword")
                        ->orWhereHas('listing', function ($q) use ($keyword) {
                            $q->where('name', 'LIKE', "%$keyword%");
                        })
                        ->orWhereHas('user', function ($q) use ($keyword) {
                            $q->where('first_name', 'LIKE', "%$keyword%")->orWhere('last_name', 'LIKE', "%$keyword%");
                        })
                        ->orWhereHas('booking', function ($q) use ($keyword) {
                            $q->where('booking_number', 'LIKE', "%$keyword%");
                        });
                });
            }

            // Apply date range filter
            if (!empty($startDate) && !empty($endDate)) {
                $review->whereBetween('created_at', [$startDate, $endDate]);
            }

            // Apply filter conditions (remove duplicates)
            if (!empty($request->customers)) {
                $review->whereIn('user_id', $request->customers);
            }
            if (!empty($request->service_providers)) {
                $review->whereIn('provider_id', $request->service_providers);
            }
            if (!empty($request->listings)) {
                $review->whereIn('listing_id', $request->listings);
            }

            // Apply role-based filtering
            $user = auth()->user();
            if ($user->hasRole(['user', 'sub_admin'])) {
                // No extra condition here - admins can see all reviews
            } elseif ($user->hasRole('service')) {
                $review->where('provider_id', $user->id);
            } elseif ($user->hasRole('customer')) {
                $review->where('user_id', $user->id);
            }

            // Get paginated results
            $review = $review->latest()->paginate($perPage);

            return view('review.review.layout.table', compact('review'))->render();

        } catch (\Exception $e) {
            // Return error response in case of any issues
            return response()->json([
                'error' => true,
                'message' => 'An error occurred while filtering reviews: ' . $e->getMessage()
            ], 500);
        }
    }
    function review_post(Request $request)
    {
        $request->validate([
            "booking_ids" => "required|exists:bookings,ids",
            "images" => "array|max:5",
            "images.*" => "image|mimes:jpeg,png,jpg|max:10240",
            "rating" => 'required|numeric',
            "communication" => "required",
            "cleanliness" => "required",
            "features" => "required",
            "value_of_money" => "required",
            "accuracy_of_description" => "required",
            "comment" => "nullable|string",
        ]);
        try {
            $request_data = $request->all();
            $request_data['images'] = [];
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $image) {
                    $request_data['images'][] = $this->storeImageToWebP('review-image', $image);
                }
            }
            $reviewService = $this->reviewService->create($request_data);
            if ($reviewService["status"] == true) {
                return back()->with(["message" => "Your review has been posted.", "type" => "success", "title" => "Thanks!"]);
            } else {
                return back()->with(["message" => $reviewService["message"], "type" => "error", "title" => "Error"]);
            }
        } catch (\Throwable $th) {
            return redirect()->back()->with("message", "Something Went wrong");
        }
    }
    function get_review_form(Request $request)
    {
        // try {
        $booking = Booking::with("listing")->where("ids", $request->booking_id)->where("is_reviewed", 0)->first();
        if (!$booking) {
            return api_response(false, "Booking not found");
        }
        $providerRating = round(Review::where("provider_id", $booking->provider_id)->avg("rating"), 1);
        $listing = $booking->listing;
        $page = (string) view("modals.review-form", compact('booking', "listing", "providerRating"));
        return api_response(true, "Review Form", $page);
        // } catch (\Throwable $e) {
        //     return api_response(false, "Something Went Wrong");
        // }
    }
    function edit_review_form($review_id)
    {
        // try {
        $review = Review::with(['booking', 'booking.provider', 'listing', 'listing.user'])->where('id', $review_id)->where('user_id', auth()->id())->first();
        $booking = $review->booking;
        $providerRating = round(Review::where("provider_id", $booking->provider_id)->avg("rating"), 1);
        if (!$review) {
            return api_response(false, "Review not found");
        }
        $page = (string) view("modals.review-form-edit", compact('review', 'booking', "providerRating"));
        return api_response(true, "Review Form", $page);
        // } catch (\Throwable $e) {
        //     return api_response(false, "Something Went Wrong");
        // }
    }
    function view_review_details(Request $request)
    {
        // try {
        // $booking = Booking::with("listing")->where("ids", $request->booking_id)->where("is_reviewed", 1)->first();
        $review = Review::with(['booking', 'booking.provider', 'listing', 'listing.user'])->where('id', $request->review_id)->first();
        $booking = $review->booking;
        if (!$review) {
            return api_response(false, "Booking not found");
        }

        // Check if this is reply mode
        $reply_mode = $request->has('reply_mode') && $request->reply_mode == 'true';

        $page = (string) view("modals.view-review", compact('review', 'booking', 'reply_mode'));
        return api_response(true, "Review Form", $page);
        // }
        // catch (\Throwable $e) {
        //     return api_response(false, "Something Went Wrong");
        // }
    }

    function edit_review_details($review_id)
    {
        // try {
        $review = Review::with(['booking', 'booking.provider', 'listing', 'listing.user'])->where('id', $review_id)->first();
        $booking = $review->booking;
        $providerRating = round(Review::where("provider_id", $booking->provider_id)->avg("rating"), 1);
        if (!$review) {
            return api_response(false, "Review not found");
        }
        $page = (string) view("modals.edit-review", compact('review', 'booking', 'providerRating'));
        return api_response(true, "Review Form", $page);
        // } catch (\Throwable $e) {
        //     return api_response(false, "Something Went Wrong");
        // }
    }
}
