@extends('website.layout.master')
@section('content')
    <section class="about_banner">
        <div class="container-fluid about-con">
            <div class="row">
                <div class="col-lg-12">
                    <div class="about_box">
                        <img height="400" style="object-fit: cover; object-position: center" src="{{ asset("website") . "/". $about->banner_image }}" alt="">
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="about">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="about-box2">
                        <h2  data-aos="fade">{{ $about->title }}</h2>
                        @guest
                            <a href="javascript:void(0)" class="button1"  data-aos="fade-up" data-bs-target="#signUp" data-bs-toggle="modal"  data-aos-delay="400">{{ translate('about.get_started') }}   <i class="bi bi-arrow-right"></i></a>
                        @endguest
                        <a href="{{ url('contact_us') }}" class="button1 black text-capitalize"  data-aos="fade-up"  data-aos-delay="400">{{ translate('about.contact_us') }}  </a>
                        @foreach ($about->contents as $content)
                            <div class="box1">
                                <h2 data-aos="fade">{{ $content->title }}</h2>
                                <p>{!! $content->description !!}</p>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
