<?php
namespace App\Http\Controllers;
use App\Http\Controllers\Controller;
use App\Models\Language;
use App\Http\Requests\LanguageRequest;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Contracts\Session\Session;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB as FacadesDB;
class LanguagesController extends Controller
{
    public function index()
    {
        $enTranslations = $this->openJSONFile('en');
        $esTranslations = $this->openJSONFile('es');
        // Method 1: Merge translations with language keys
        $mergedTranslations = [];
        foreach ($enTranslations as $key => $enValue) {
            $mergedTranslations[$key] = [
                'en' => $enValue,
                'es' => $esTranslations[$key] ?? []
            ];
        }
        // Add any Spanish keys that might not exist in English
        foreach ($esTranslations as $key => $esValue) {
            if (!isset($mergedTranslations[$key])) {
                $mergedTranslations[$key] = [
                    'en' => [],
                    'es' => $esValue
                ];
            }
        }
        return view('dashboard.languages.index', compact('mergedTranslations'));
    }
    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'key' => 'required',
            'value' => 'required',
        ]);
        $data = $this->openJSONFile('en');
        $data[$request->key] = $request->value;
        $this->saveJSONFile('en', $data);
        return redirect()->route('languages');
    }
    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function destroy($key)
    {
        $languages = DB::table('languages')->get();
        $keyParts = explode('.', $key);
        
        // Get all language files
        $langFiles = ['en', 'es']; // Add more languages as needed
        
        foreach ($langFiles as $langCode) {
            $data = $this->openJSONFile($langCode);
            
            if (count($keyParts) > 1) {
                // Nested key (e.g., "home.participants")
                $page = $keyParts[0];
                $nestedKey = $keyParts[1];
                
                if (isset($data[$page]) && isset($data[$page][$nestedKey])) {
                    unset($data[$page][$nestedKey]);
                    
                    // If page is now empty, remove it too
                    if (empty($data[$page])) {
                        unset($data[$page]);
                    }
                }
            } else {
                // Top-level key
                if (isset($data[$key])) {
                    unset($data[$key]);
                }
            }
            
            $this->saveJSONFile($langCode, $data);
        }
        return response()->json(['success' => $key]);
    }
    /**
     * Open Translation File
     * @return Response
     */
    private function openJSONFile($code)
    {
        $jsonString = [];
        if (File::exists(base_path('resources/lang/' . $code . '.json'))) {
            $jsonString = file_get_contents(base_path('resources/lang/' . $code . '.json'));
            $jsonString = json_decode($jsonString, true);
        }
        return $jsonString;
    }
    /**
     * Save JSON File
     * @return Response
     */
    private function saveJSONFile($code, $data)
    {
        ksort($data);
        $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        file_put_contents(base_path('resources/lang/' . $code . '.json'), stripslashes($jsonData));
    }
    /**
     * Update translation value
     * @return Response
     */
    public function transUpdate(Request $request)
    {
        $validator = \Validator::make($request->all(), [
            'language' => 'required|string',
            'value' => 'required|string',
            'page' => 'required|string',
            'key' => 'required|string',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'message' => 'The given data was invalid.',
                'errors' => $validator->errors()
            ], 422);
        }
        $lang = $request->input('language');
        $page = $request->input('page');
        $key = $request->input('key');
        $value = $request->input('value');
        $data = $this->openJSONFile($lang);
        if (!isset($data[$page]) || !is_array($data[$page])) {
            $data[$page] = [];
        }
        $data[$page][$key] = $value;
        $this->saveJSONFile($lang, $data);
        return response()->json([
            'success' => true,
            'value' => $value,
            'page' => $page,
            'key' => $key,
            'language' => $lang
        ]);
    }
    /**
     * Remove the specified resource from storage.
     * @return Response
     */
    public function transUpdateKey(Request $request)
    {
        $languages = DB::table('languages')->get();
        if ($languages->count() > 0) {
            foreach ($languages as $language) {
                $data = $this->openJSONFile($language->code);
                if (isset($data[$request->pk])) {
                    $data[$request->value] = $data[$request->pk];
                    unset($data[$request->pk]);
                    $this->saveJSONFile($language->code, $data);
                }
            }
        }
        return response()->json(['success' => 'Done!']);
    }
    private function flattenArray($array, $prefix = '')
    {
        $result = [];
        foreach ($array as $key => $value) {
            $new_key = $prefix === '' ? $key : $prefix . '.' . $key;
            if (is_array($value)) {
                $result += $this->flattenArray($value, $new_key);
            } else {
                $result[$new_key] = $value;
            }
        }
        return $result;
    }
}
