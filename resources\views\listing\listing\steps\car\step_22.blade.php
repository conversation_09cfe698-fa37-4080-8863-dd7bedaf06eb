@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "price-set");
@endphp
<fieldset class="set_price_step accommodations_rules_step">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    {{-- <div class="radios_wrapper">
                        @foreach (['hourly', 'daily'] as $basis_type)
                            <div class="custom_radio">
                                <label title="Set price to {{ $basis_type }}"
                                    for="chk_set_price_{{ $basis_type }}">{{ ucfirst($basis_type) }}</label>
                                <input class="price_type_radio" type="radio" id="chk_set_price_{{ $basis_type }}"
                                    name="basis_type" value="{{ ucfirst($basis_type) }}"
                                    @if (($listing->detail->basis_type ?? '') == ucfirst($basis_type) || $loop->first) checked @endif
                                    style="border: 0.5px solid rgb(155, 155, 155);">
                            </div>
                        @endforeach
                    </div> --}}

                    <div class="allow_pets_field_wrapper set_daily_hourly_wrapper mb-4">
                        {{-- <div class="allow_pets_title">
                            <h4>Do you allow Pets?</h4>
                        </div> --}}
                        <div class="allow_pets_input_wrapper">
                            @foreach (['hourly', 'daily'] as $basis_type)
                                <div class="allow_pets_input">
                                    <label title="Set price to {{ $basis_type }}" for="chk_set_price_{{ $basis_type }}">{{ ucfirst($basis_type) }}</label>
                                    <input type="radio" class="radio_btn" id="chk_set_price_{{ $basis_type }}" name="basis_type" value="{{ ucfirst($basis_type) }}"
                                    @if (($listing->detail->basis_type ?? '') == ucfirst($basis_type) || $loop->first) checked @endif>
                                </div>
                            @endforeach
                        </div>
                    </div>


                    <div class="price_hidden_fields_wrapper scrollable-section">
                        <div class="setting_price_input">
                            <div class="txt_field">
                                <div class="icon_input_wrapper">
                                    <i class="fa fa-usd" aria-hidden="true"></i>
                                    @if(($listing->detail->basis_type ?? '') == 'Daily')
                                        <input class="hidden_price" type="hidden" name="price" 
                                            value="{{ floor($listing->detail->per_day ?? 0) == 0 ? '' : floor($listing->detail->per_day ?? 0) }}">
                                    @else
                                        <input class="hidden_price" type="hidden" name="price" 
                                            value="{{ floor($listing->detail->per_hour ?? 0) == 0 ? '' : floor($listing->detail->per_hour ?? 0) }}">
                                    @endif

                                    <input type="text" name="" pattern="^\d+(\.\d{0,2})?$" id="price"
                                        value="{{ ($listing->detail->basis_type ?? '') == 'Daily' ? $listing->detail->per_day : $listing->detail->per_hour ?? 00 }}"
                                        placeholder="0">
                                </div>
                                <span>COP</span>
                            </div>
                        </div>
                        <div class="hidden_price_breakdown_wrapper">
                            <div class="price_breakdown_wrapper" style="display: none;">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ translate('stepper.guest_price_before_taxes') }} </th>
                                            <td>$<span class="guest_price"></span> COP</td>
                                        </tr>
                                        <tr>
                                            <th>{{ translate('stepper.host_service_fee') }}</th>
                                            <td>$<span class="service_fee"></span> COP</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <th>{{ translate('stepper.you_earn') }} </th>
                                            <td>$<span class="final_lisitng_price"></span> COP</td>
                                        </tr>
                                    </tbody>
                                    <tr class="spacer">
                                        <td colspan="2"></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="see_more_wrapper">
                                <a href="javascript:void(0)" class="btn see_more_btn">
                                    {{ translate('stepper.see_more') }} 
                                    <i class="fa fa-chevron-down" aria-hidden="true"></i>
                                </a>
                            </div>

                            <div class="learn_more_wrapper">
                                <a href="{{ $step_data->url ?? "" }}" class="learn_more_pricing_btn" target="_blank">{{ translate('stepper.learn_more') }}
                                    {{ translate('stepper.about_pricing') }} </a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>
@push('js')
    <script>
        $(document).ready(function() {
            // window.setPriceType = @json($listing->detail->basis_type ?? '');

            @if(isset($listing->detail->basis_type))
                window.setPriceType="{{strtolower($listing->detail->basis_type)}}";
                if(window.setPriceType == "hourly"){
                    $('.add_discounts_step .daily_hourly_discount').hide();
                    // $('.add_discounts_step .daily_hourly_discount input').prop('disabled', true);
                    $('.add_discounts_step .daily_hourly_discount input[type="number"]').attr('type', 'hidden');
                }else{
                    $('.add_discounts_step .daily_hourly_discount').show();
                    // $('.add_discounts_step .daily_hourly_discount input').prop('disabled', false);
                    $('.add_discounts_step .daily_hourly_discount input[type="hidden"]').attr('type', 'number');
                }
                console.log(window.setPriceType)
            @else
                window.setPriceType="hourly";
                $('.add_discounts_step .daily_hourly_discount').hide();
                // $('.add_discounts_step .daily_hourly_discount input').prop('disabled', true);
                $('.add_discounts_step .daily_hourly_discount input[type="number"]').attr('type', 'hidden');
            @endif
            // alert(window.setPriceType)
            $(document).on('change',
                '.listing_stepper .set_price_step .inner_section_main_col .set_daily_hourly_wrapper input[type="radio"]',
                function() {
                    var selectedValue = $(this).val().toLowerCase();
                    // var inputField = $(this).closest('.price_change').find('.seasonal_price_val');
                    if (selectedValue == "hourly") {
                        window.setPriceType = "hourly";
                        // $('.add_discounts_step .daily_hourly_discount').hide();
                        $('.add_discounts_step .daily_hourly_discount').hide();
                        // $('.add_discounts_step .daily_hourly_discount input').prop('disabled', true);
                        $('.add_discounts_step .daily_hourly_discount input[type="number"]').attr('type', 'hidden');
                    } else {
                        window.setPriceType = "daily";
                        // $('.add_discounts_step .daily_hourly_discount').show();
                        $('.add_discounts_step .daily_hourly_discount').show();
                        // $('.add_discounts_step .daily_hourly_discount input').prop('disabled', false);
                        $('.add_discounts_step .daily_hourly_discount input[type="hidden"]').attr('type', 'number');
                    }
                });


                $('.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper').hide();
            $(document).on('click',
                '.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .see_more_wrapper .see_more_btn',
                function() {
                    $(this).closest('.hidden_price_breakdown_wrapper').find('.price_breakdown_wrapper').show();
                    $(this).removeClass('see_more_btn');
                    $(this).addClass('see_less_btn');
                    $(this).html('See less <i class="fa fa-chevron-up" aria-hidden="true"></i>');
                });
            $(document).on('click',
                '.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .see_more_wrapper .see_less_btn',
                function() {
                    $(this).closest('.hidden_price_breakdown_wrapper').find('.price_breakdown_wrapper').hide();
                    $(this).addClass('see_more_btn');
                    $(this).removeClass('see_less_btn');
                    $(this).html('{{ translate('stepper.see_more') }}  <i class="fa fa-chevron-down" aria-hidden="true"></i>')
                });


            const $inputField = $('#price');
            const maxFontSize = 80;
            const minFontSize = 40;

            const initialWidth = $inputField.outerWidth();
            let previousValue = $inputField.val();

            $inputField.on('input', function() {
                setTimeout(() => {
                    const currentValue = $inputField.val();
                    // console.log(currentValue);
                    const tempSpan = $('<span>').css({
                        visibility: 'hidden',
                        fontFamily: 'Poppins',
                        padding: '0 10px',
                        id: 'tempspan',
                        fontWeight: '600',
                        whiteSpace: 'nowrap',
                        fontSize: `${maxFontSize}px`,
                    }).text(currentValue || $inputField.attr('placeholder'));
                    $('body').append(tempSpan);

                    // console.log('Temp Span Width: ' + tempSpan.outerWidth());

                    const inputWidth = $inputField.outerWidth();
                    let fontSize = maxFontSize;

                    while (tempSpan.outerWidth() > inputWidth && fontSize > minFontSize) {
                        fontSize--;
                        tempSpan.css('fontSize', `${fontSize}px`);
                    }

                    // console.log(fontSize);
                    // console.log(minFontSize);

                    if (fontSize <= minFontSize) {
                        let currentWidth = $inputField.outerWidth();
                        $inputField.css('width', tempSpan.outerWidth());
                        // console.log('increased');
                    }

                    if (currentValue.length < previousValue.length) {
                        let currentWidth = $inputField.outerWidth();
                        if (currentWidth > initialWidth) {
                            $inputField.css('width', tempSpan.outerWidth());
                        }
                    }

                    if (currentValue.trim() === '') {
                        $inputField.css('width', `${initialWidth}px`);
                        fontSize = maxFontSize;
                    }

                    $inputField.css('fontSize', `${fontSize}px`);
                    $('.listing_stepper .set_price_step .setting_price_input .icon_input_wrapper i').css(
                        'font-size', `${fontSize}px`);
                    $('.listing_stepper .set_price_step .inner_section_main_col .price_hidden_fields_wrapper .setting_price_input .txt_field > span')
                        .css({
                            'font-size': `${fontSize}px`,
                            'line-height': `${fontSize}px`,
                        });

                    tempSpan.remove();

                    previousValue = currentValue;
                }, 50);
                
            });


            setTimeout(() => {
                const currentValue = $inputField.val();
                // console.log(currentValue);
                const tempSpan = $('<span>').css({
                    visibility: 'hidden',
                    fontFamily: 'Poppins',
                    padding: '0 10px',
                    id: 'tempspan',
                    fontWeight: '600',
                    whiteSpace: 'nowrap',
                    fontSize: `${maxFontSize}px`,
                }).text(currentValue || $inputField.attr('placeholder'));
                $('body').append(tempSpan);

                // console.log('Temp Span Width: ' + tempSpan.outerWidth());

                const inputWidth = $inputField.outerWidth();
                let fontSize = maxFontSize;

                while (tempSpan.outerWidth() > inputWidth && fontSize > minFontSize) {
                    fontSize--;
                    tempSpan.css('fontSize', `${fontSize}px`);
                }

                // console.log(fontSize);
                // console.log(minFontSize);

                if (fontSize <= minFontSize) {
                    let currentWidth = $inputField.outerWidth();
                    $inputField.css('width', tempSpan.outerWidth());
                    // console.log('increased');
                }

                if (currentValue.length < previousValue.length) {
                    let currentWidth = $inputField.outerWidth();
                    if (currentWidth > initialWidth) {
                        $inputField.css('width', tempSpan.outerWidth());
                    }
                }

                if (currentValue.trim() === '') {
                    $inputField.css('width', `${initialWidth}px`);
                    fontSize = maxFontSize;
                }

                $inputField.css('fontSize', `${fontSize}px`);
                $('.listing_stepper .set_price_step .setting_price_input .icon_input_wrapper i').css('font-size',
                    `${fontSize}px`);
                $('.listing_stepper .set_price_step .inner_section_main_col .price_hidden_fields_wrapper .setting_price_input .txt_field > span')
                    .css({
                        'font-size': `${fontSize}px`,
                        'line-height': `${fontSize}px`,
                    });

                tempSpan.remove();

                previousValue = currentValue;

            }, 50);


            const inputField = document.getElementById("price");

            // function ensureDecimal() {
            //     if (!inputField.value.includes('.') && parseFloat(inputField.value) > 0) {
            //         inputField.value += '0.000';
            //     }
            // }

            // ensureDecimal();

            // function moveCursorToDecimal(event) {
            //     const cursorPos = inputField.selectionStart;
            //     const decimalIndex = inputField.value.indexOf('.');
            //     if (event.key === '.' || (event.key === 'ArrowRight' && cursorPos === decimalIndex)) {
            //         inputField.setSelectionRange(decimalIndex + 1, decimalIndex + 1);
            //         event.preventDefault();
            //     }
            // }
            // inputField.addEventListener('focus', ensureDecimal);
            // inputField.addEventListener('blur', ensureDecimal);
            // inputField.addEventListener('keydown', (event) => {
            //     if (!/[\d.]/.test(event.key) && !['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(
            //             event.key)) {
            //         event.preventDefault();
            //     }
            //     moveCursorToDecimal(event);
            // });

            // inputField.addEventListener('input', () => {
            //     let value = inputField.value;
            //     const parts = value.split('.');
            //     if (parts.length > 2) {
            //         value = parts[0] + '.' + parts[1];
            //     }
            //     if (value.includes('.')) {
            //         const [beforeDecimal, afterDecimal] = value.split('.');
            //         value = beforeDecimal + '.' + afterDecimal.slice(0, 3);
            //     }
            //     inputField.value = value;
            //     if (!value.includes('.')) {
            //         if (parseFloat(inputField.value) > 0) {
            //             inputField.value = value + '.000';
            //         } else {
            //             inputField.value = value + '0.000';
            //         }
            //     }
            // });



            // function setPriceBreakdown() {
            //     var price = $('.set_price_step #price').val();
            //     $('.set_price_step .price_breakdown_wrapper .guest_price').html(price);
            //     var serviceFee = price * {{ $category->tax }} / 100;
            //     var priceAfterDeduction = price - serviceFee;
            //     $('.set_price_step .price_breakdown_wrapper .service_fee').html(serviceFee.toFixed(3));
            //     $('.set_price_step .price_breakdown_wrapper .final_lisitng_price').html(priceAfterDeduction.toFixed(
            //         3));
            // }


            function setPriceBreakdown() {
                var price = parseFloat($('.set_price_step .hidden_price').val()) || 0;
                var tax = @js($category->tax);
                $('.set_price_step .price_breakdown_wrapper .guest_price').html(price.toLocaleString('en-US', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }));
                var serviceFee = (price * tax) / 100;
                var priceAfterDeduction = price - serviceFee;
                $('.set_price_step .price_breakdown_wrapper .service_fee').html(serviceFee.toLocaleString('en-US', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }));
                $('.set_price_step .price_breakdown_wrapper .final_lisitng_price').html(priceAfterDeduction.toLocaleString('en-US', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }));
            }


            // function setPriceBreakdown() {
            //     var price = parseFloat($('.set_price_step .hidden_price').val()) || 0;
            //     var tax = @js($category->tax);
            //     $('.set_price_step .price_breakdown_wrapper .guest_price').html(price.toFixed(
            //         3));
            //     var serviceFee = (price * tax) / 100;
            //     var priceAfterDeduction = price - serviceFee;
            //     $('.set_price_step .price_breakdown_wrapper .service_fee').html(serviceFee.toFixed(3));
            //     $('.set_price_step .price_breakdown_wrapper .final_lisitng_price').html(priceAfterDeduction.toFixed(
            //         3));
            // }


            $(document).on('keyup', '.set_price_step #price', function() {
                setPriceBreakdown();
            });

            setPriceBreakdown();

            $('#price').on('input', function() {
                let inputVal = $(this).val();
                $('.hidden_price').val($(this).val().replace(/,/g, ''));
                inputVal = inputVal.replace(/[^0-9]/g, '');
                const formattedVal = inputVal.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                $(this).val(formattedVal);
                $('#tempspan').html(formattedVal);
            });

            function settingSavedPrice(){
                var hiddenInputVal = $('.set_price_step .hidden_price').val();
                hiddenInputVal = hiddenInputVal.replace(/[^0-9]/g, '');
                // hiddenInputVal = hiddenInputVal.split('.')[0];
                const formattedVal = hiddenInputVal.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                $('#price').val(formattedVal);
            }

            settingSavedPrice();
        });

        // document.getElementById('price').addEventListener('input', function(e) {
        //     const value = e.target.value;
        //     const regex = /^\d*(\.\d{0,3})?$/;
        //     if (!regex.test(value)) {
        //         e.target.value = value.slice(0, -1);
        //     }
        // });

    </script>
@endpush
