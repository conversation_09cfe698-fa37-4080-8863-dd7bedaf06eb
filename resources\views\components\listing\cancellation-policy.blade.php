@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "cancellation");
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="inner_section_col">
            <div class="main_step_title">
                <h2>{{ $step_data->title ?? "" }}</h2>
            </div>
            @isset($step_data->sub_title)
                <div class="step_description">
                    <p>{{ $step_data->sub_title ?? "" }}</p>
                </div>
            @endisset
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="inner_section_categories_main_col scrollable-section">
            <div class="row">
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 single_category_col">
                    <div class="inner_section_single_category">
                        <input type="radio" name="cancellation_policy" id="cancellation_policy_1" value="Flexible"
                            @if (($listing->detail->cancellation_policy ?? '') == 'Flexible' ) checked @endif />
                        <label for="cancellation_policy_1">
                            <div class="img_title_wrapper">
                                <div class="category_icon_wrapper">
                                    <img src="{{ asset('website') }}/images/flexible_icon.png" alt="">
                                </div>
                                <div class="category_title">
                                    <h5>{{ translate('stepper.cancellation_flexible') }}</h5>
                                </div>
                            </div>
                            <div class="description">
                                <ul>
                                    <li>{{ translate('stepper.cancellation_flexible_1') }}</li>
                                    <li>{{ translate('stepper.cancellation_flexible_2') }}</li>
                                </ul>
                            </div>
                        </label>
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 single_category_col">
                    <div class="inner_section_single_category">
                        <input type="radio" name="cancellation_policy" id="cancellation_policy_2" value="Moderate"
                            {{ ($listing->detail->cancellation_policy ?? '') == 'Moderate' ? 'checked' : '' }}>
                        <label for="cancellation_policy_2">
                            <div class="img_title_wrapper">
                                <div class="category_icon_wrapper">
                                    <img src="{{ asset('website') }}/images/moderate_icon.png" alt="">
                                </div>
                                <div class="category_title">
                                    <h5>{{ translate('stepper.cancellation_moderate') }}</h5>
                                </div>
                            </div>
                            <div class="description">
                                <ul>
                                    <li>{{ translate('stepper.cancellation_moderate_1') }}</li>
                                    <li>{{ translate('stepper.cancellation_moderate_2') }}</li>
                                    <li>{{ translate('stepper.cancellation_moderate_3') }}</li>
                                </ul>
                            </div>
                        </label>
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 single_category_col">
                    <div class="inner_section_single_category">
                        <input type="radio" name="cancellation_policy" id="cancellation_policy_3" value="Strict"
                            {{ ($listing->detail->cancellation_policy ?? '') == 'Strict' ? 'checked' : '' }}>
                        <label for="cancellation_policy_3">
                            <div class="img_title_wrapper">
                                <div class="category_icon_wrapper">
                                    <img src="{{ asset('website') }}/images/strict_icon.png" alt="">
                                </div>
                                <div class="category_title">
                                    <h5>{{ translate('stepper.cancellation_strict') }}</h5>
                                </div>
                            </div>
                            <div class="description">
                                <ul>
                                    <li>{{ translate('stepper.cancellation_strict_1') }}</li>
                                    <li>{{ translate('stepper.cancellation_strict_2') }}</li>
                                    <li>{{ translate('stepper.cancellation_strict_3') }}</li>
                                </ul>
                            </div>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@push('js')
    <script>
        $(document).ready(function() {

            function listingCancellationValidation() {
                var checkedOptionsCount = 0;
                var test = 0;
                $('.cancellation_policies_step .inner_section_categories_main_col .single_category_col').each(function() {
                    if ($(this).find('input[type="radio"]').is(':checked')) {
                        checkedOptionsCount++;
                    }
                });
                if (checkedOptionsCount == 0) {
                    $('.cancellation_policies_step:has(.inner_section_categories_main_col) .next').prop('disabled', true);
                } else {
                    $('.cancellation_policies_step:has(.inner_section_categories_main_col) .next').prop('disabled', false);
                }
            }

            listingCancellationValidation();

            $(document).on('change',
                '.cancellation_policies_step .inner_section_categories_main_col .single_category_col input[type="radio"]',
                function() {
                    listingCancellationValidation();
                });
        });
    </script>
@endpush
