{"404_page": {"oops": "Oops!", "page_not_found": "Page Not Found", "go_back_home": "Go back home", "page_not_found_message": "The page you’re looking for doesn’t exist. It might have been removed, renamed, or is temporarily unavailable."}, "500_page": {"oops": "Oops!", "internal_server_error": "Internal Server Error", "something_went_wrong": "Something went wrong. We're experiencing technical difficulties. Please try again later.", "go_back_home": "Go back home"}, "about": {"contact_us": "Contact Us", "get_started": "Get Started"}, "advance_filters_modal": {"filters": "Filters", "price_range": "Price Range", "resident_type": "Resident Type", "select_type": "Select Type", "super_star_status": "Super Star Status", "amenities": "Amenities", "select_amenities": "Select Amenities", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "rules": "Rules", "pets_allowed": "<PERSON><PERSON>owed", "pets_not_allowed": "Pets Not Allowed", "apply": "Apply", "reset": "Reset", "hourly": "Hourly", "daily": "Daily", "vehicle_type": "Vehicle type", "seats": "Seats", "transmission": "Transmission", "select_transmission": "Select Transmission", "engine_type": "Engine type", "select_engine": "Select Engine", "ratings": "Ratings", "watercraft_type": "Watercraft type", "passengers": "Passengers", "watercraft_length": "Watercraft length", "any": "Any", "five_plus_fit": "5+ fit", "six_plus_fit": "6+ fit", "seven_plus_fit": "7+ fit", "activities": "Activities", "select_activity": "Select Activity", "per_child": "Per Child", "per_adult": "Per Adult", "duration": "Duration", "same_day": "Same Day", "multi_day": "Multi Day", "activity_type": "Activity Type", "experience_type": "Experience Type", "private": "Private", "grouped": "Grouped", "language_offered": "Language Offered", "select_language": "Select Language", "kid_friendly": "<PERSON>", "yes": "Yes", "no": "No", "accessible_features": "Accessible Features", "select_features": "Select Features", "time_of_day": "Time of Day", "morning": "Morning", "afternoon": "Afternoon", "evening": "Evening"}, "booking_pdf": {"booking_id": "Booking ID", "booking_status": "Booking Status", "check_in": "Check-in", "check_out": "Check-out", "hosted_by": "Hosted by", "passengers": "Passengers", "ft": "ft", "guests": "Guests", "everything_included": "Everything included in this place", "starts_at": "Starts at", "ends_at": "Ends at", "key_features": "Key Features", "whats_included": "What’s Included", "not_included": "Not Included", "pick_up": "Pick-up", "drop_off": "Drop-off", "seats": "Seats", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "key_features_description": "Key Features Description", "check_in_after": " Check-in After", "rules": "Rules", "check_out_after": " Check-out after", "pets": "Pets", "things_that_you_need_to_know": "Things that you need to know!", "booking_name": "Booking Name", "hour_slots": "Hour Slots", "booking_address": "Booking Address", "pricing_details": "Pricing Details", "listing_price": " Listing Price", "basis_type": "Basis Type", "total_hours": "Total Hour(s)", "total": "Total", "total_price": "Total Price", "sub_total": "Sub Total", "confirmed": "Confirmed", "payment_will_be_proceeded_in_usd": "Your Payment will be proceeded in USD", "cancelled": "Cancelled", "completed": "Completed", "payment_status": "Payment Status", "paid": "Paid", "fully_refunded": "Fully Refunded", "partially_refunded": "Partiallly Refunded", "no_refund": "No Refund", "payment_method": "Payment Metdod", "luxustars_secure_payment_assurance": "LuxuStars Secure Payment Assurance", "your_peace_of_mind_our_priority": "Your Peace of Mind, Our Priority", "at_luxustars_all_payments_are_securely_processed": "At LuxuStars, all payments are securely processed using the latest encryption technologies and protected by 3D Secure authentication for eligible cards. By keeping transactions within our platform, you're guaranteed:", "24_7_support": "24/7 Support", "assistance_whenever_you_need_it": "Assistance whenever you need it.", "payment_protection": "Payment Protection:", "your_funds_are_held_safely_until_the_booking_is_completed": "Your funds are held safely until the booking is completed.", "cancellation_coverage": "Cancellation Coverage:", "enjoy_policies_tailored_to_your_needs": "Enjoy policies tailored to your needs.", "experience_luxury_with_confidence": "Experience luxury with confidence, knowing we've got you covered every step of the way.", "copyright": "all copyright reserved."}, "chatbox": {"messages ": "Messages"}, "confirm_booking": {"confirm_your_booking": "Confirm Your Booking", "your_listing_details": "Your Listing Details", "price_details": "Price Details", "departure_time": "Departure Time", "return_time": "Return Time", "card_number": "Card Number", "expiry_format": "MM/YY", "expiry_date": "Expiry Date", "card_holder_name": "Card Holder Name", "cardholder_name": "Cardholder Name", "your_trip_details": "Your Trip Details", "departure_return_date": "Departure & Return Date", "start_end_date": "Start & End Date", "pickup_dropoff_date": "Pickup & Dropoff Date", "checkin_checkout_date": "Check-in & Check-out Date", "your_payment_will_be_processed_in_usd": "Your payment will be processed in USD", "total_adult": "Total Adult", "total_child": "Total Child", "start_date": "Start Date", "end_date": "End Date", "departure_date": "Departure Date", "return_date": "Return Date", "pickup_date": "Pickup Date", "dropoff_date": "Dropoff Date", "checkin_date": "Check-in Date", "checkout_date": "Check-out Date", "pico_y_placa_restrictions": "This vehicle has Pico y Placa restrictions on", "and": "and", "confirm_and_pay_agreement": "By selecting the Confirm and Pay button, I agree to the", "card_added_successfully": "Card Added Successfully", "default": "<PERSON><PERSON><PERSON>", "day": "Day", "days": "Days", "night": "Night", "nights": "Nights", "child": "Child", "adult": "Adult", "rules": "Rules", "pets": "Pets", "edit": "Edit", "guests": "Guests", "cancelation_policy": "Cancelation Policy", "payment_method": "Payment Method", "card": "Card", "saved_cards": "Saved Cards", "no_card_added": "No Card Added", "cvc": "CVC", "add_new_card": "Add New Card", "add_card": "Add Card", "booking_terms": "Booking Terms", "confirm_and_pay": "Confirm and Pay", "please_verify_your_identity": "Please verify your identity", "click_here": "Click here", "booking_detail": "Booking Detail", "modal_text": "Lorem ipsum dolor sit amet consectetur adipisicing elit. In beatae quas quae nostrum? Ab quia numquam illum, obca<PERSON><PERSON> sunt, unde maxime dicta modi earum possimus officia voluptas neque alias vel!"}, "contact": {"contact_us": "Contact Us", "phone_number": "Phone Number", "email_address": "Email Address", "location": "Location", "get_in_touch": "Get In Touch", "first_name": "First Name", "last_name": "Last Name(s)", "phone": "Phone Number", "email": "Email", "subject": "Subject", "message": "Message", "submit": "Submit", "get_started": "We're here to help! Fill out the form below and we'll get back to you as soon as possible."}, "content_management_system": {"content_management_system": "Content Management System", "categories": "Categories", "home": "Home", "about": "About", "contact": "Contact", "amenities": "Amenities", "terms_conditions": "Terms & Conditions", "privacy_policy": "Privacy Policy", "cancellation_refund_policy": "Cancellation & Refund Policy", "help_center": "Help Center", "add": "Add", "edit": "Edit", "delete": "Delete", "confirm_delete": "Confirm Delete", "yes_delete": "Yes, Delete", "cancel": "Cancel", "no_data_found": "No Data Found", "save": "Save", "update": "Update", "title": "Title", "description": "Description", "image": "Image", "payment_fee": "Payment Fee", "navigation_bar": "Navigation Bar", "logo": "Logo", "favicon": "Favicon", "footer": "Footer", "footer_logo": "Footer <PERSON>", "footer_description": "Footer Description", "footer_links": "Footer Links", "footer_link": "<PERSON>er <PERSON>", "footer_link_title": "Footer Link Title", "footer_link_url": "Footer Link URL", "about_site": "About Site", "home_page": "Home Page", "about_us": "About Us", "contact_us": "Contact Us", "search": "Search", "name": "Name", "action": "Action", "s_no": "S.No", "categories_english": "Categories (English)", "categories_spanish": "Categories (Spanish)", "add_amenity_category": "Add Amenity Category", "amenity_category": "Amenity Category", "social_links": "Social Links", "add_footer_link": "Add Footer Link", "phone": "Phone", "email": "Email", "address": "Address", "address_here": "Address Here", "suppliers_agreement": "Supplier Agreement", "banner_image": "Banner Image", "section": "Section", "back": "Back", "amenity_option": "Amenity Option", "amenity": "Amenity", "are_you_sure": "Are you sure you want to delete this item?", "no_amenity_in_this_category": "No Amenity in this Category", "amenity_detail": "Amenity Detail", "amenity_name": "Amenity Name", "in_eng": "In English", "in_spanish": "In Spanish", "loading": "Loading", "view": "View", "create_new": "Create New", "upload_amenity_image_dimensition": "Upload icon in PNG, JPEG or SVG and recommended dimension is 30 x 30", "name_field_isrequired": "Name field is required", "ok": "OK", "create_add_another": "Create & Add Another", "enter": "Enter", "yes_delete_it": "Yes, Delete it!", "types": "Types", "cms": "CMS", "type": "Type", "in_english": "In English", "edit_listing_type": "Edit Listing Type", "delete_listing_type": "Delete Listing Type", "upload_type_icon": "Upload Type Icon", "type_name": "Type Name", "status": "Status", "active": "Active", "inactive": "Inactive", "no_listing_types_in_this_category": "No Listing Types in this Category", "first_step": "First Step", "listing_general_detail": "Listing General Detail", "step_2_initialization": "Step 2 Initiation", "upload_image": "Upload Image", "title_in_english": "Title in English", "title_in_spanish": "Title in Spanish", "description_in_english": "Description in English", "description_in_spanish": "Description in Spanish", "enter_title_in_english": "Enter Title in English", "enter_title_in_spanish": "Enter Title in Spanish", "enter_description_in_english": "Enter Description in English", "enter_description_in_spanish": "Enter Description in Spanish", "sub_heading_in_english": "Sub-Heading in English", "sub_heading_in_spanish": "Sub-Heading in Spanish", "enter_sub_heading_in_english": "Enter Sub-Heading in English", "enter_sub_heading_in_spanish": "Enter Sub-Heading in Spanish", "finalize_listing": "Finalize Listing", "last_step": "Last Step", "price_step": "Price Step (Step # 3.1)", "hourly_step": "Hourly Availability Step (Step # 3.2)", "daily_step": "Daily Hours Step (Step # 3.3)", "preparation_step": "Preparation Step (Step # 3.6)", "review_step_last": "Review Step (Last)", "rules_step": "Rules Step (Step # 2.5)", "notes_step": "Notes Step (Step # 2.6)", "photos_step": "Photos Step (Step # 2.7)", "document_step": "Document Step (Step # 2.8)", "title_step": "Title Step (Step # 2.9)", "description_step": "Description Step (Step # 2.10)", "step_3": "Step # 3", "adult_price_step": "Adult Price Step (Step # 3.1)", "child_price_step": "Child Price Step (Step # 3.2)", "private_booking_price_step": "Private Booking Price Step (Step # 3.3)", "pico_placa_step": "<PERSON><PERSON> (Step # 3.3)", "cancellation_step": "Cancellation Step (Step # 3.4)", "book_step": "Book Step (Step # 3.5)", "cutoff_time_step": "Cut-Off Time Step (Step # 3.7)", "seasonal_step": "Seasonal Step (Step # 3.8)", "discount_step": "Discount Step (Step # 3.9)", "step_1": "Step # 1", "types_step": "Types Step (Step # 1.1)", "map_step": "Map Step (Step # 1.2)", "address_step": "Address Step (Step # 1.3)", "detail_step": "Detail Step (Step # 1.4)", "schedule_duration_step": "Schedule & Duration Step (Step # 1.5)", "language_step": "Language Step (Step # 1.6)", "step_2": "Step # 2", "accessibility_step": "Accessibility Step (Step # 2.1)", "itinerary_step": "Itinerary (Step # 2.2)", "key_feature_step": "Key Feature Step (Step # 2.3)", "inclusions_exclusions_step": "Inclusions and Exclusions Step (Step # 2.4)", "type_icon": "Type Icon", "edit_category": "Edit Category", "title_here": "Title Here", "description_here": "Description Here", "service_fee": "Service Fee", "edit_percentage": "Edit Percentage", "percentage": "Percentage", "facebook": "Facebook", "instagram": "Instagram", "tiktok": "Tiktok", "youtube": "Youtube", "save_and_update": "Save & Update", "ownership_step": "Ownership Step", "amenities_step": "Amenities Step (Step # 2.1)", "engine_step": "Engine Step (Step # 2.1)", "transmission_step": "Transmission (Step # 2.2)", "stay_step": "Minimum Stay Step (Step # 3.3)", "cut_off_time_step": "Cut-Off Time Step (Step # 3.5)"}, "dashboard": {"dashboard": "Dashboard", "total_earnings": "Total Earnings", "total_bookings": "Total Bookings", "total_listings": "Total Listings", "active_bookings": "Active Bookings", "pending_reports": "Pending Reports", "upcoming_bookings": "Upcoming Bookings", "lifetime_commisions": "Lifetime Commisions", "withdrawal_requests": "Withdrawal Requests", "life_time_earning": "Life Time Earning", "overall_bookings": "Overall Bookings", "reported_by": "Reported By", "host_reviews": "Host Reviews", "date": "Date", "status": "Status", "description": "Description", "action": "Action", "pending": "Pending", "approved": "Approved", "view": "View", "reply": "Reply", "delete": "Delete", "no_report_found": "No Report Found", "no_notification_found": "No Notification Found", "notifications": "Notifications", "no_new_notification_found": "No New Notification Found", "payments": "Payments", "message": "Message", "no_message": "No Message", "pay_now": "Pay Now", "profile_name": "Profile Name", "no_withdrawal_request": "No Withdrawal Request Found", "view_invoice": "View Invoice", "total_users": "Total Users", "total_reviews": "Total Reviews", "total_reports": "Total Reports", "total_messages": "Total Messages", "total_notifications": "Total Notifications", "total_settings": "Total Settings", "booking_id": "Booking ID", "listing": "Listing", "customer": "Customer", "category": "Category", "from": "From", "until": "Until", "total_paid_customer": "Total Paid Customer", "total_payout": "Total Payout", "booked": "Booked", "today": "Today", "tomorrow": "Tomorrow", "on_going": "On Going", "completed": "Completed", "upcoming": "Upcoming", "no_bookings_for_today": "No Bookings for Today", "no_bookings_for_tomorrow": "No Bookings for Tomorrow"}, "dashboard_bookings": {"download_all_csv": "Download All CSV", "download_status_csv": "Download {status} CSV", "download_today_csv": "Download Today CSV", "download_upcoming_csv": "Download Upcoming CSV", "download_ongoing_csv": "Download Ongoing CSV", "download_completed_csv": "Download Completed CSV", "download_cancelled_csv": "Download Cancelled CSV", "all_bookings": "All Bookings", "bookings": "Bookings", "booking_id": "Booking ID", "listing": "Listing", "customer": "Customer", "category": "Category", "date_from": "Date From", "until": "Until", "amount": "Amount", "payment_method": "Payment Method", "total_paid_by_customer": "Total Paid by Customer", "total_payout": "Total Payout", "booked": "Booked", "ratings": "Ratings", "created_at": "Created At", "status": "Status", "action": "Action", "deleted_listing": "Deleted Listing", "currency_code": "COP", "on_going": "On Going", "completed": "Completed", "upcoming": "Upcoming", "ongoing": "Ongoing", "cancelled": "Cancelled", "booking_detail": "Booking Detail", "customer_name": "Customer Name:", "listing_basis_type": "Listing Basis Type:", "date": "Date:", "check_in": "Check In:", "check_out": "Check Out:", "guest": "Guest:", "total_days": "Total Days:", "discount": "Discount:", "subtotal_amount": "Subtotal Amount:", "total_amount": "Total Amount:", "booking_cancellation": "Booking Cancellation", "how_much_refunded": "How much is Refunded?", "eg_80_percent": "e.g., 80%", "deduct_provider": "Deduct from the Service Provider's e-wallet?", "yes": "Yes", "no": "No", "submit": "Submit", "all": "All", "today": "Today", "search_placeholder": "Search by booking ID, customer, listing...", "select_date_range": "Select Date Range"}, "dashboard_calendar": {"calendar": "Calendar", "download_ics_file": "Download ICS File", "sync_with_airbnb": "Sync with Airbnb", "sync_with_airbnb_title": "Sync with Airbnb", "enter_airbnb_url": "Enter Airbnb Calendar URL", "paste_url_placeholder": "Paste your URL ending with .ics", "blocked": "Blocked", "no_blocked_dates": "No Blocked Dates", "no_blocked_dates_selected": "No blocked dates were selected to unblock.", "unblock_dates": "Unblock Dates", "confirm_unblock": "Do you want to unblock", "dates": "date(s)", "yes_unblock": "Yes, unblock", "edit_note": "Edit Note", "no_cancel": "No, cancel", "enter_note": "Enter a note", "update_note": "Update Note", "cancel": "Cancel", "please_enter_note": "Please enter a note", "note_updated": "Note Updated!", "note_updated_message": "The note has been updated.", "block_dates": "Block Dates", "confirm_block": "Do you want to block these dates?", "yes_block": "Yes, block", "dates_blocked_success": "The selected dates have been blocked.", "unblock_date": "Unblock Date", "confirm_unblock_single": "Do you want to unblock this date?", "please_note_calendar_synced_every_hour": "Please note that the calendar is synced with Airbnb every hour.", "submit": "Submit", "booked": "Booked", "blocking": "Blocking", "unblocking": "Unblocking"}, "dashboard_contact": {"contact_messages": "Mensajes de <PERSON>o", "all": "Todos", "unresolved": "No Resueltos", "resolved": "<PERSON><PERSON><PERSON><PERSON>", "search": "Buscar", "edit_temp": "<PERSON><PERSON>", "first_name": "Nombre", "last_name": "Apellido", "phone_number": "Número de Teléfono", "email": "Correo Electrónico", "subject": "<PERSON><PERSON><PERSON>", "status": "Estado", "action": "Acción", "view": "<PERSON>er", "reply": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Eliminar", "no_contact_message": "No se encontraron mensajes de contacto", "contact_detail": "Detalle del Contacto", "user_name": "Nombre de Usuario", "message": "Men<PERSON><PERSON>", "reply_to": "Responder a", "send": "Enviar", "are_you_sure": "¿Estás seguro?", "delete_confirmation": "¡No podrás revertir esto!", "yes_delete": "¡Sí, eliminar!", "deleted": "¡Eliminado!", "your_file_has_been_deleted": "Tu archivo ha sido eliminado", "error": "¡Error!", "reply_sent": "Respuesta Enviada", "reply_contact": "Responder al Contacto", "attachments": "Archivos Adjuntos", "enter_reply_here": "Ingresa tu respuesta aquí...", "submit": "Enviar", "enter_template_body": "Ingresa el cuerpo de tu plantilla aquí...", "template_body": "Cuerpo de la Plantilla", "key_words": "Palabra<PERSON>", "dont_give_space": "No pongas espacios entre { }", "template": "Plantilla"}, "dashboard_ewallet": {"your": "Your", "e_wallet": "E-Wallet", "total_earning": "Total Earning", "total_platform_fee": "Total Platform Fee", "total_cancellation": "Total Cancellation", "adjusments": "Adjusments", "incoming_payments": "Incoming Payments", "available_withdrawl": "Available Withdrawl", "withdrawal_requests": "Withdrawal Requests", "fees_paid": "<PERSON><PERSON>", "outstanding_payments": "Outstanding Payments", "incoming_withdrawal": "Incoming <PERSON>drawal", "in_transit": "In Transit", "earning_insights": "Earning Insights", "overall": "Overall", "today": "Today", "this_week": "This Week", "last_month": "Last Month", "this_month": "This Month", "this_year": "This Year", "last_year": "Last Year", "no_earning": "No Earning Yet", "manage_payouts": "Manage Payouts", "configure_payments": "Configure Payments", "auto_payout": "Auto Payout", "manual_payout": "Manual Payout", "withdrawal_request": "Withdrawal Request", "payments": "Payments", "browser_not_support_iframe": "Your browser does not support iframes", "profile_name": "Profile Name", "date": "Date", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "status": "Status", "no_withdrawal_request_yet": "No Withdrawal Request Yet", "submission": "Submission", "no_payments": "No Payments Yet", "withdraw_heading": "Withdraw funds manually without waiting for auto payouts.", "withdraw_amount": "Withdraw Amount", "minimum_withdraw_amount": "Minimum Withdraw Amount is 50000", "withdraw": "Withdraw", "submit": "Submit", "verify_heading": "Please Check Your Email", "verify_message": "We’ve sent the verification code to", "verify": "Verify", "resend": "Resend", "code": "Code", "active": "Active", "inactive": "Inactive", "autopayout_heading": "Automatically withdraw funds once your wallet reaches the amount you set.", "autopayout_message": "You can turn this off anytime.", "enter_amount": "Enter Amount", "enter_verification_code": "Enter Verification Code", "minimum_withdraw_amount_is_50000": "Minimum Withdraw Amount is 50000", "otp_success_title": "OTP Matched!", "otp_success_message": "Your OTP has been successfully verfied.", "payout_status_updated": "Payout status updated successfully", "toast_success": "Success", "warning": "Warning", "setup_payout_method_first": "Please setup your payout method first.", "ok": "OK", "unable_to_verify_payout_status": "Unable to verify payout status"}, "dashboard_help_center": {"help_center_articles": "Help Center Articles", "add_category": " Add Category", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "please_select_a_valid_view": "Please select a valid view!", "error": "Error!", "create_new_category": "Create New Category", "back": "Back", "articles": "Articles", "create": "Create", "category": "Category", "no_articles_added": "No Articles Added", "how_would_you_like_to_name_it": "How Would You Like to Name It?", "describe_what_this_category_is_about": "Describe What This Category is About", "upload_failed": "Upload Failed", "error_uploading_images": "Error uploading image:", "update": "Update", "id": "ID", "title": "Title", "description": "Description", "help_detail": "Help Detail", "example_description": "E.g., Everything related to payment issues.", "billing": "E.g., Billing", "helpcenter_added": "HelpCenter added!", "helpcenter_updated": "HelpCenter updated!", "image_deleted_successfully": "Image deleted successfully", "helpcenter_deleted": "HelpCenter deleted!"}, "dashboard_listing": {"confirm_delete": "Confirm Delete", "i_agree": "I Agree", "disagree": "Disagree", "save": "Save", "all_refundable_bookings": "All Refundable Bookings", "add_private_name": "Add a private name for this listing that only you can see. This helps you organize and identify your listing, more easily, especially if you manage multiple properties or services.", "uploaded_documents": "Uploaded Documents", "download_all": "Download All", "uploaded_document": "Uploaded Document", "view_documents": "View Documents", "new_listing": "New Listing", "no_active_bookings": "No Active Bookings", "copy_listing_url": "Copy Listing URL", "pause_listing": "Pause Listing", "unpause_listing": "Unpause Listing", "add": "Add", "my_listings": "My Listings", "all_listings": "All Listings", "add_listing": "Add Listing", "add_medical": "Add Medical", "listing_name": "Listing Name", "type": "Type", "active_bookings": "Active Bookings", "revenue_generated": "Revenue Generated", "status": "Status", "ratings": "Ratings", "calendar": "Calendar", "action": "Action", "provider_image": "Provider Image", "listing": "Listing", "internal_name": "Internal Name", "days_remaining": "Days Remaining", "you_dont_have_any_listings": "You don't have any listings", "reject": "Reject", "delete_selected": "Delete Selected", "all": "All", "published": "Published", "pending": "Pending", "accepted": "Accepted", "in_review": "In Review", "drafts": "Drafts", "drafts_delete_notice": "All drafts will be permanently deleted after 30 days.", "send": "Send", "contact_host": "Contact Host", "are_you_sure": "Are you sure?", "unsuspense_confirmation": "Do you want to unsuspense this listing!", "yes_unsuspende": "Yes, unsuspende it!", "listing_unsuspended": "Listing Unsuspended", "revert_warning": "You won't be able to revert this!", "yes_delete": "Yes, delete it!", "pause": "Pause", "unpause": "Unpause", "pause_confirmation": "Are you sure you want to pause this listing? It will no longer be visible to users.", "unpause_confirmation": "Are you sure you want to make this listing active again?", "yes_pause": "Yes, Pause it!", "yes_unpause": "Yes, Unpause it!", "copied": "<PERSON>pied", "link_copied": "Link copied to clipboard", "error": "Error", "failed_to_generate_url": "Failed to generate listing URL", "error_generating_url": "Error occurred while generating the listing URL", "no_listing_found": "No listing found", "no_listing_yet": "No listing yet", "no_listings_published": "No listings currently published", "no_listings_review": "No listings in review yet", "search": "Search...", "no_listings_draft": "No listings in draft yet", "paused": "Paused", "draft": "Draft", "days_left": "Days Left", "deleted": "Deleted", "back": "Back", "logo": "logo"}, "dashboard_report": {"reported_by": "Reported By", "subject": "Subject", "date": "Date", "status": "Status", "description": "Description", "actions": "Actions", "pending": "Pending", "Resolved": "Resolved", "view": "View", "delete": "Delete", "resolved": "Resolved"}, "dashboard_reviews": {"all_reviews": "All Reviews", "reported": "Reported", "reported_by": "Reported By", "host_reviews": "Host Reviews", "date": "Date", "status": "Status", "description": "Description", "action": "Action", "pending": "Pending", "approved": "Approved", "view": "View", "reply": "Reply", "delete": "Delete", "no_report_found": "No Report Found", "review_details": "Review Details", "review_by": "Review By", "listing": "Listing", "rating": "Rating", "images": "Images", "no_images": "No Images", "close": "Close", "edit_review": "Edit Review", "save_changes": "Save Changes", "cancel": "Cancel", "add_photos": "Add Photos", "drag_drop_photos": "Drag & Drop Photos", "or": "or", "browse": "Browse", "upload_complete": "Upload Complete", "all_images_uploaded": "All images have been uploaded successfully!", "uploading_images": "Uploading Images", "uploading_progress": "Uploading {current} out of {total}", "are_you_sure": "Are you sure?", "delete_confirmation": "You won't be able to revert this!", "yes_delete": "Yes, delete it!", "deleted": "Deleted!", "file_deleted": "Your file has been deleted.", "error": "Error!", "other_reason": "Other Reason", "booking_id": "Booking ID", "customer_name": "Customer Name", "listing_name": "Listing Name", "service_provider_name": "Service Provider Name", "actions": "Actions", "type": "Type", "reason": "Reason", "showing_entries": "Showing {from} to {to} of {total} entries", "filter": "Filter", "search_placeholder": "Search...", "select_date": "Select date", "reset": "Reset", "view_review": "View Review", "view_reason": "View Reason", "edit": "Edit", "resolve": "Resolve", "provider_response": "Provider Response", "customer_review": "Customer Review", "filter_modal_title": "Filter", "customers": "Customers", "service_provider": "Service Provider", "listings": "Listings", "select_customers": "Select Customers", "select_service_providers": "Select Service Providers", "select_listings": "Select Listings", "apply": "Apply", "clear": "Clear", "all": "All", "resolved": "Resolved", "delete_review": "Delete Review", "delete_report": "Delete Report", "report": "Report", "fecha": "Date", "reply_action": "Reply", "report_review": "Report Review", "inappropriate_language": "Inappropriate Language", "discrimination": "Discrimination", "harassment_bullying": "Harassment or Bullying", "violates_privacy": "Violates My Privacy", "spam_irrelevant": "Spam or Irrelevant", "false_misleading": "False or Misleading Information", "other": "Other", "provide_more_details": "Please provide more details", "submit": "Submit", "bedrooms": "Bedrooms", "guests": "Guests", "no_review_found": "No Review Found", "select_status": "Select Status", "beds": "Beds", "bathrooms": "Bathrooms", "total_booking_capacity": "Total Booking Capacity", "seats": "Seats"}, "dashboard_sidebar": {"dashboard": "Dashboard", "e_wallet": "E-Wallet", "user_management": "User Management", "messages": "Messages", "cms": "CMS", "user_chat": "User <PERSON>", "inbox": "Inbox", "calendar": "Calendar", "listings": "Listings", "bookings": "Bookings", "reviews": "Reviews", "reports": "Reports", "help_center": "Help Center", "amenity_options": "Amenity Options", "sub_admin": "Sub Admin", "categories": "Categories", "notifications": "Notifications", "settings": "Settings", "languages": "Languages"}, "footer": {"follow_us": "Follow Us", "terms_conditions": "Terms & Conditions", "all_rights_reserved": "All Rights Reserved", "company": "Company", "support": "Support", "about": "About", "press": "Press", "product_updates": "Product Updates", "luxuShield": "LuxuShield", "security": "Security", "terms": "Terms & Conditions", "privacy_policy": "Privacy Policy", "supplier_agreement": "Supplier Agreement", "help_center": "Help Center", "resolution_center": "Resolution Center", "cancellation_policy": "Cancellation Policy", "contact_us": "Contact Us", "submit_feature_request": "Submit a Feature Request", "bank_code": "Bank Code Lookup"}, "help_center": {"help_center": "Help Center", "search_for_answer": "What are you looking for?", "reset": "Reset", "get_started": "We're here to help! Browse our help topics or contact our support team for assistance.", "list_view": "List View", "box_view": "Box View", "total_articles": "Total Articles", "last_updated": "Last Updated", "no_help_topics_found": "No help center found", "faq_not_found": "FAQ not found"}, "help_center_detail": {"help_center": "Help Center", "search_for_answer": "Search for answer", "reset": "Reset", "last_updated": "Last Updated", "faq_not_found": "FAQ not found", "no_help_topics_found": "No Help Topics Found", "get_started": "We're here to help! Browse our help topics or contact our support team for assistance.", "contact_support": "Contact Support", "total_articles": "Total Articles", "view_all_articles": "View All Articles", "popular_topics": "Popular Topics", "frequently_asked_questions": "Frequently Asked Questions", "search_results": "Search Results", "search_results_for": "Search Results for", "no_results_found": "No results found for your search", "try_different_keywords": "Try different keywords or browse our help categories below"}, "home": {"where": "Where?", "select_date": "Select date", "checkin_checkout": "Check-in/Check-out", "adults": "Adults", "children": "Children", "pickup_time": "Pick-up time", "dropoff_time": "Drop-off time", "dropoff_time_placeholder": "Enter drop-off time", "guests": "Guests", "seats": "Seats", "current_location": "Current Location", "home_search_bar": "Hey! Anything specific you're looking for?", "manage_your_asset": "Manage your Asset", "list_your_asset": "List your Asset", "my_bookings": "My Bookings", "profile_settings": "Profile Settings", "logout": "Logout", "english": "English", "checkin_date": "Check-in date", "checkout_date": "Check-out date", "spanish": "Spanish", "price_range": "Price Range", "amenities": "Amenities", "amenity_1": "Amenity 1", "amenity_2": "Amenity 2", "amenity_3": "Amenity 3", "amenity_4": "Amenity 4", "rating": "Rating", "select": "Select", "rules": "Rules", "select_rules": "Select Rules", "pets_allowed": "<PERSON><PERSON>owed", "pets_not_allowed": "Pets Not Allowed", "bedrooms": "Bedrooms", "beds": "Beds", "apply": "Apply", "list_view": "List View", "map_view": "Map View", "select_amenities": "Select Amenities", "per_hourly": "per hourly", "per_day": "per day", "per_adult": "per adult", "select_an_options": "Select an options", "participants": "participants", "enter_destination": "Enter destination", "passengers": "Passengers", "airport_or_city": "Airport or city", "Where": "Where?", "Time": "Time", "how_many": "How many?", "pick_up_and_drop_off": "Pick-up & Drop-off", "please_select_different_dates": "Please select different dates.", "end_date_must_be_after_the_start_date": "End date must be after the start date.", "end_date_cannot_be_before_start_date": "End date cannot be before start date.", "select_time": "Select time", "reset": "Reset", "login_to": "<PERSON>gin <PERSON>", "forgot_password": "Forgot password?", "login": "<PERSON><PERSON>", "not_a_member": "Not a member?", "sign_up": "Sign Up", "email_address": "Email Address", "password": "Password", "sign_up_to": "Sign Up To", "id_match_warning": "Make sure your name matches your government ID", "first_name": "First Name", "last_name": "Last Name", "phone": "Phone", "confirm_password": "Confirm Password", "by_selecting": "By selecting", "i_agree_to": "I agree to", "terms_conditions": "Terms & Conditions", "and_acknowledge": "and acknowledge the", "privacy_policy": "Privacy Policy", "accept_terms_error": "Please accept the Terms & Conditions to continue", "already_a_member": "Already a Member?", "sign_in": "Sign In", "check_your_email": "Check Your Email", "sent_verification_code": "We've sent the verification code to", "enter_verification_code": "Enter Verification Code", "verify": "Verify", "resend_code": "Resend Code", "hoorah": "Hoorah!", "email_verified_success": "Your email has been successfully verified.", "close": "Close", "loading": "Loading", "next": "Next", "Daily": "Daily", "Hourly": "Hourly", "Tour": "Tour", "load_more": "Load More", "time": "Time", "date_time": "Date & Time", "pick_up": "Pick-up"}, "listing_details": {"report_listing": "Report Listing", "no_refund_after_that": "After that, you won't get a refund.", "before": "Before", "full_refund": "Full Refund", "no_refund": "No Refund", "no_refund_on_change_or_cancel": "If you change or cancel your booking you will not get a refund.", "cancel_before_time_full_refund": "Cancel your reservation before :date at 11:59 p.m. and you'll get a full refund. Times are based on the property's local time.", "full_refund_time_note": "at 11:59 p.m. and you'll get a full refund. Times are based on the property's local time.", "check_in": "Check-in", "checked": "Checked", "until": "Until", "date": "Date", "adults": "Adults", "children": "Children", "adult": "Adult", "child": "Child", "private_booking": "Private Booking", "total": "Total", "disabled": "Disabled", "subtotal_amount": "Subtotal Amount", "response_from": "Response from", "updated": "(Updated)", "translate": "Translate", "pico_y_placa_restriction_warning": "One or more of the selected dates fall under Pico y Placa restrictions. Please confirm the restrictions hours with the host before booking.", "guest": "Guest", "show_original": "Show Original", "date_to": "Date To", "new_listing": "New Listing", "report": "Report", "hosted_by": "Hosted by", "cancelation_policy": "Cancelation Policy", "new_host": "New Host", "your_browser_not_support_the_html_video": "Your browser does not support the HTML video", "view_all": "View All", "company_name": "Company Name", "desigination": "Desigination", "date_from": "Date From", "experience": "Experience", "url": "URL", "not_provided": "Not provided", "subject": "Subject", "description": "Description", "read_more": "Read More", "submit": "Submit", "show_more": "Show More", "show_less": "Show Less", "book_your": "Book Your", "now": "Now", "day_at": "Day at", "select_a_date": "Select a date", "select_a_valid_date": "Select a valid date", "error": "Error", "please_select_a_valid_view": "Please select a valid view!", "oops": "Oops!", "verify_identity": "Verify identity", "please_select_the_date": "Please select the date", "days_in": "Days in", "select_slots": "Select Slots", "select": "Select", "slots": "Slots", "slot": "Slot", "night_at": "Night at", "day_on": "Day on", "day_with": "Day with", "to": "to", "nights_minimum": "nights minimum.", "passengers": "Passengers", "ft": "ft", "starts_at": "Starts at", "ends_at": "Ends at", "everything_included_with_this_watercraft": "Everything included with this watercraft", "show_all": "Show all", "included_equipment": "Included Equipment", "key_features": "Key Features", "seats": "Seats", "whats_included": "What’s Included", "no_include_added": "No Include added", "not_included": "Not Included", "no_not_include_added": "No not include added", "guests": "Guests", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "check_in_after": "Check-in After", "check_out_before": "Check-out Before", "everything_included_in_this_place": "Everything included in this place", "overall_rating": "Overall Rating", "new": "New", "cleanliness": "Cleanliness", "communication": "Communication", "amenities_features": "Amenities/Features", "amenities": "Amenities", "value_for_money": "Value for Money", "accuracy_of_description": "Accuracy of Description", "view_all_reviews": "View All Reviews", "review": "Review", "reviews": "Reviews", "filter": "Filter", "most_recent": "Most Recent", "highest_rating": "Highest Rating", "lowest_rating": "Lowest Rating", "search_reviews": "Search Reviews", "report_review": "Report Review", "inappropriate_language": "Inappropriate Language", "discrimination_or_harassment": "Discrimination or Harassment", "private_information_shared": "Private Information Shared", "irrelevant_or_off_topic": "Irrelevant or Off-Topic", "retaliation_or_personal_attack": "Retaliation or Personal Attack", "inaccurate_or_misleading_information": "Inaccurate or Misleading Information", "other": "Other", "please_provide_more_details": "Please provide more details", "report_response": "Report Response", "discrimination": "Discrimination", "harassment_or_bullying": "Harassment or Bullying", "violates_my_privacy": "Violates My Privacy", "spam_or_irrelevant": "Spam or Irrelevant", "false_or_misleading_information": "False or Misleading Information", "booking_capacity": "Booking Capacity", "same_day_tour": "Same Day Tour", "duration": "Duration", "hours": "Hours", "days_tour": "Days Tour", "children_allowed": "Children Allowed", "children_not_allowed": "Children Not Allowed", "start_time": "Start Time", "end_time": "End Time", "accessibility_features": "Accessibility Features", "accessibility": "Accessibility", "no_accessibility": "No Accessibility", "included_languages": "Included Languages", "no_language_added": "No Language added", "itinerary": "Itinerary", "rules": "Rules", "you_need_to_know": "You need to know!", "contact_host": "Contact Host", "day": "Day", "hourly": "Hourly", "select_slot": "Select Slot", "pickup_dropoff_date": "Pickup & Dropoff Date", "rent_now": "Rent Now"}, "stepper": {"here_what_your_guests_will_see": "Here's what your guests will see. Take a moment to ensure everything is accurate and ready to go live.", "enter_check_in_time": "Enter Check-in Time", "enter_check_out_time": "Enter Check-out Time", "choose_how_much_time_you_need_between_bookings_to_prepare_clean_or_reset_your_vehicle": "Choose how much time you need between bookings to prepare, clean, or reset your Vehicle.", "a_high_performance_engine_ensures_a_lot_of_fun": "e.g., A high-performance engine ensures a lot of fun, superior speed, and an unforgettable driving experience.", "selected_amenities": "Selected Amenities", "length": "Length", "please_select_a_valid_view": "Please select a valid view!", "lets_review_your_experience": "Let's Review Your Experience", "child_price_is_compulsory_for_children_booking": "Child price is compulsory for children booking", "check_in_after": "Check-in After", "day_tour": "Days Tour", "pets_not_allowed": "Pets Not Allowed", "night_tour": "Nights Tour", "check_out_before": "Check-out Before", "check_in_time": "Check-in Time", "check_out_time": "Check-out Time", "children_not_allowed": "Children Not Allowed", "accessibility_features": "Accessibility Features", "eg_this_luxurious_beachfront_villa_offers_stunning_ocean_views": "E.g., This luxurious beachfront villa offers stunning ocean views, a private infinity pool, and spacious rooms designed for comfort and relaxation.", "eg_luxurious_beachfront_villa_with_private_pool": "E.g., Luxurious Beachfront Villa with Private Pool", "no_smoking_indoors": "E.g., No smoking indoors.", "there_a_grocery_store_just": "E.g., There’s a grocery store just a 5-minute walk from the property.", "guests_can_use_the_bbq_grill": "E.g., Guests can use the BBQ grill.", "enjoy_breathtaking_ocean_view": "E.g., Enjoy breathtaking ocean views from the private balcony, complete with outdoor seating.", "stunning_ocean_view_balcony": "E.g., Stunning Ocean View Balcony", "search_amenities": "Search for amenities", "amenities": "Amenities", "bedrooms": "Bedrooms", "guests": "Guests", "beds": "Beds", "bathrooms": "Bathrooms", "logo": "logo", "seats": "Seats", "not_sure ": "Not Sure?", "see_documents ": "See Documentation", "loading": "Loading...", "error": "Error", "something_went_wrong": "Something went wrong", "error_exclamation": "Error!", "invalid_view": "Please select a valid view!", "generating_preview": "Generating Preview", "invalid_price": "Invalid Price", "price_children": "Please enter a price for children.", "price_private": "Please enter a price for private booking.", "price_children_private": "Please enter a price for children and private booking.", "please_wait": "Please Wait", "wait_message": "Please wait as this may take a moment", "well_done": "Well Done!", "success": "Success!", "listing_under_review": "Listing under review.", "save_exit": "Save & Exit", "total_booking_capacity": "Total booking capacity", "offer_private_booking": "Would you like to offer a private booking option?", "children_allowed": "Are children allowed in this experience?", "define_child_age_range": "Define the age range for children", "yes": "Yes", "no": "No", "min_age": "Min Age", "max_age": "Max Age", "oops": "Oops!", "age_min_error": "The age cannot be less than 1.", "age_max_error": "The age cannot be greater than 18. Please enter a valid child age.", "age_range_error": "The end age should be greater than or equal to start age.", "same_day": "Same Day", "same_day_tour": "Same Day Tour", "multiple_days": "Multiple Days", "start_end_time": "Start & End Time", "how_many_days": "How Many Days?", "day_1_time": "Day 1 Start & End Time", "day_2_time": "Day 2 Start & End Time", "enter_start_time": "Enter Start Time", "enter_end_time": "Enter End Time", "to": "to", "day": "Day", "invalid_time": "Invalid Time Selection", "end_time_after_start": "End time must be after start time.", "selected_features": "Selected Features", "search_features": "Search for features", "search_activities": "Search for activities", "amenity_id": "amenityID", "checked": "Checked", "data_id": "data-id", "selected_data": "selectedData", "selected_data_value": "selectedDataValue", "before_filtering": "Before filtering:", "after_filtering": "After filtering:", "step_2": "Step 2", "step_3": "Step 3", "stacked_documents": "Stacked Documents", "search_categories": "Search Categories", "enter_your_address_here": "Enter your address here", "country_region": "Country/Region", "street_address": "Street Address", "apt_suite_unit_optional": "Apt, Suite, Unit (Optional)", "city_town": "City/Town", "state_territory": "State/Territory", "zip_code": "Zip Code", "enter_country": "Enter Country", "enter_street": "Enter Street", "enter_suit": "Enter Suit", "enter_city": "Enter City", "enter_state": "Enter State", "enter_zip_code": "Enter Zip Code", "search_languages": "Search Languages", "select_day": "Select Day", "add": "Add", "name_the_stop_or_activity": "Name the stop or activity (e.g., 'City Market Tour')", "briefly_describe_what_happens_at_this_stop": "Briefly describe what happens at this stop (e.g., 'Explore local vendors and handicrafts')", "itinerary_start": "Itinerary Start", "itinerary_end": "Itinerary End", "itinerary_stop": "Itinerary Stop", "enter_a_standout_feature": "Enter a standout feature (e.g., 'Breathtaking Scenic Views')", "whats_included": "What's Included", "whats_not_included": "What's Not Included", "enter_what_is_included": "Enter what is included (e.g., 'Snacks, water, guide services, transportation between locations')", "enter_what_is_not_included": "Enter what is not included (e.g., 'Meals, transportation to the meeting point, personal expenses')", "do_you_allow_pets": "Do you allow pets?", "whats_allowed": "What's Allowed", "whats_not_allowed": "What's Not Allowed", "add_any_additional_notes_or_helpful_information_for_your_guests": "Add any additional notes or helpful information for your guests (e.g., 'Wear comfortable clothing and bring sunscreen for outdoor activities')", "add_stop": "Add Stop", "add_activity": "Add Activity", "add_start": "Add Start", "add_end": "Add End", "add_photo": "Add Photo", "add_video": "Add Video", "add_photos": "Add Photos", "add_videos": "Add Videos", "no_valid_images_to_upload": "No valid images to upload", "uploading_images": "Uploading Images", "upload_complete": "Upload Complete", "all_images_have_been_uploaded_successfully": "All images have been uploaded successfully", "error_uploading_images": "Error uploading images", "failed_to_update_sort_order": "Failed to update sort order", "delete_confirmation": "Are you sure you want to delete this image?", "make_cover_photo": "Make Cover Photo", "delete": "Delete", "drag_and_drop": "Drag & Drop", "upload_photos_or_videos": "Upload Photos or Videos", "upload_photos": "Upload Photos", "upload_videos": "Upload Videos", "or_browse_for_photos": "Or browse for photos", "or_browse_for_photos_and_videos": "Or browse for photos and videos", "add_documents": "Add Documents", "valid_images": "Valid images to upload", "note": "Note", "upload": "Upload", "cancel": "Cancel", "browse": "Browse", "optional": "Optional", "see_a_list_of_required_documents": "See a list of required documents", "uploading_documents": "Uploading Documents", "key_features": "Key Features", "your_file_has_been_deleted": "Your file has been deleted", "you_can_only_upload_a_maximum_of_images_at_a_time": "You can only upload a maximum of 10 images at a time.", "invalid_file_type": "Invalid file type", "file_size_not_allowed": "File size not allowed (must be 300KB-10MB)", "uploading": "Uploading", "no_files_selected": "No files selected", "wear_comfortable_clothing_and_bring_sunscreen_for_outdoor_activities": "Wear comfortable clothing and bring sunscreen for outdoor activities", "describe_this_feature_in_detail": "Describe this feature in detail (e.g., 'Enjoy panoramic views of the coastline from a stunning vantage point.')", "experience_image": "Experience Image", "tour_image": "Tour Image", "guests_can_bring_their_own_cameras_for_personal_photos": "E.g., Guests can bring their own cameras for personal photos.", "children_under_5_are_not_allowed_due_to_safety_concerns": "E.g., Children under 5 are not allowed due to safety concerns.", "guests_are_required_to_check_in_at_the_front_desk": "E.g., Guests are required to check-in at the front desk.", "no_pets_are_allowed_in_this_experience": "E.g., No pets are allowed in this experience.", "guests_are_required_to_provide_a_valid_id_upon_check_in": "E.g., Guests are required to provide a valid ID upon check-in.", "guests_are_required_to_check_in_by_10am": "E.g., Guests are required to check-in by 10am.", "are_you_sure": "Are you sure you want to delete this image?", "yes_delete": "Yes, delete it!", "deleted": "Deleted", "itinerary": "Itinerary", "eg_vip_helicopter_tour_over_the_caribbean_coast": "E.g., VIP Helicopter Tour Over the Caribbean Coast", "highlight_what_makes_your_experience_unique_and_unforgettable": "Highlight what makes your experience unique and unforgettable.", "eg_embark_on_a_thrilling_off_road_adventure_through_rugged_jungle_terrain": "E.g., Embark on a thrilling off-road adventure through rugged jungle terrain. Drive your own ATV as you navigate scenic trails, hidden waterfalls, and wildlife-rich landscapes. End the journey with a refreshing dip in a secluded cenote.", "describe_service": "Which of these describe your service", "set_price_guest": "Now, set your price for guests", "luxury_stays": "<PERSON><PERSON><PERSON> stays", "luxury_car": "Luxury car", "luxury_boat": "Luxury boat", "experiences": "Experiences", "step_1": "Step 1", "experience_intro": "Tell us about the experience you're offering!", "experience_detail": "We'll inquire about the type of experience you organise. Afterward, please provide details regarding its location.", "type_experience": "type of experience", "selection_field": "Selection Field", "pakistan": "Pakistan", "usa": "USA", "uae": "UAE", "india": "India", "confirm_address": "Confirm Your Address", "address_note": "Your address is shared with guests only once they have completed a booking.", "country_selection": "Country Selection", "apartment_label": "Apt, suit, unit (if applicable)", "enter_suite": "Enter Suit", "enter_zip": "Enter Zip Code", "start_point": "Where's the start point?", "enter_location": "Enter a location", "drag_map": "Drag the map to reposition the Pin", "share_basics": "Share some basics about your experience", "booking_capacity": "booking capacity", "start_time": "Start Time", "end_time": "End Time", "define_child_age": "Define Child Age", "define_adult_age": "Define adult Age", "make_stand_out": "Make your experience stand out", "enhance_experience": "Enhance your space by highlighting its unique amenities and presenting it with a collection of five or more appealing photos. Then, craft a title and description that captivate.", "boat_details": "Share with guests the details of your boat", "specs_features": "specs & features", "multiple_selection_field": "multiple selection field", "connectivity": "Connectivity", "entertainment": "Entertainment", "kitchen": "Kitchen", "laundry": "<PERSON><PERSON><PERSON>", "parking": "Parking", "environment": "Environment", "workspace": "Work Space", "title": "Title", "description": "Description", "accessibility": "Accessibility", "type_field": "Type Field", "tour_rules": "Tell guests about your tour rules", "pets_allowed": "Do you allow Pets?", "house_rules": "write your own house rules", "high_quality_photos": "Add some high quality photos", "drop_photos": "Drop your photos here", "choose_images": "Choose at least 5 images", "upload_device": "Upload from your device", "title_prompt": "Now, let's come up with a title for your experience.", "title_note": "Opt for brief titles. Enjoy the creativity; changes can be made at any time.", "title_place": "Title for your place", "describe_experience": "Describe your experience", "unique_space": "Share what makes your space unique", "get_creative": "Let's get creative", "advance_listing": "Advance your listing", "min_stay_length": "Minimum Stay Length", "min_nights_question": "What's the minimum nights you will rent your place for?", "one": "One", "two": "Two", "three": "Three", "missing_amenities": "Missing Amenities? Create Your Own", "missing_amenities_description": "Write any stand-out amenities of your accommodation that were not included in our catalog.", "custom_amenities": "Custom Amenities", "example_title": "E.g., Stunning Ocean View Balcony", "example_description": "E.g., Enjoy breathtaking ocean views from the private balcony, complete with outdoor seating.", "no_description": "No Description", "editor_initialized": "Editor was initialized", "editor_initialization_error": "There was a problem initializing the editor.", "input_exists": "Input exists.", "next": "Next", "back": "Back", "listing_availability": "Listing availability", "advance_booking": "How far in advance can guests book?", "advance_booking_label": "advance booking", "cancelation_policy": "cancelation policy", "finish_publish": "Let's finish up & publish your experience", "finalize_listing": "Finally, you'll choose booking settings, set up pricing, and publish your listing", "set_price_child": "Now, set your price for child", "change_anytime": "You can change it anytime", "price_example": "$137.99", "price_after_commission": "Guest price after commissions will be 150.00", "learn_pricing": "Learn more about pricing", "set_price_adult": "Now, set your price for adult", "add_discounts": "Add discounts", "listing_tip": "Help your place stand out to get booked faster and earn your first reviews.", "new_listing_discount": "new listing discount", "upto_3_bookings": "For upto 3 bookings", "weekly_discount": "Weekly discount", "weekly_note": "For stays of 7 nights or more", "monthly_discount": "Monthly Discount", "monthly_note": "For stays of 28 nights or more", "seasonal_pricing": "seasonal pricing", "seasonal_note": "*Allows you to increase the the price of your listing for a specific period of time", "season_start": "season start", "season_end": "season end", "price_change": "price change", "review_experience": "Let's review your experience", "review_note": "This is what your guests will see, make sure everything is correct.", "best_experience": "Best experience", "new": "New", "price_per_night": "$127.00", "night": "night", "whats_next": "What's Next?", "confirm_and_publish": "Confirm few details and publish", "verification_note": "We'll let you know if you need to verify your identity or register with the local government.", "rule_1": "Rule 1", "allowed": "allowed", "not_allowed": "not allowed", "update": "Update", "finish": "Finish", "one_month": "One Month", "three_months": "Three Months", "six_months": "Six Months", "save_and_continue": "Save & Continue", "same_day_booking_close": "When Should Same-Day Bookings Close?", "enter_cutoff_time": "Enter cut-off time", "increase": "Increase", "decrease": "Decrease", "double_zero": "00", "price": "Price", "new_price_guest": "New Price for Guest:", "new_price_adult": "New Price for Adult:", "new_price_child": "New Price for Child:", "new_price_private": "New Price for Private Booking:", "new_price": "New Price:", "cop": "COP", "season_starts": "Season Starts:", "season_ends": "Season Ends:", "invalid_date_range": "Invalid Date Range", "disabled_date_warning": "You cannot select a range that includes already disabled dates.", "cancellation_flexible": "Flexible", "cancellation_flexible_1": "If the customer cancels at least 1 day before check-in, you will not receive any payment.", "cancellation_flexible_2": "If the customer cancels within 24 hours of check-in, you will receive 100% of the booking amount (minus the service fee).", "cancellation_moderate": "Moderate", "cancellation_moderate_1": "If the customer cancels at least 5 days before check-in, you will not receive any payment.", "cancellation_moderate_2": "If the customer cancels between 5 and 2 days before check-in, you will receive 50% of the booking amount (minus the service fee).", "cancellation_moderate_3": "If the customer cancels within 2 days of check-in, you will receive 100% of the booking amount (minus the service fee).", "cancellation_strict": "Strict", "fill_fields_first": "Please fill in all fields before proceeding.", "for_up_to_3_bookings": "For up to 3 bookings", "offer_disabled": "3 bookings completed - offer disabled.", "weekly_discount_description_days": "For rentals of 7 days or more", "weekly_discount_description_nights": "For stays of 7 nights or more", "monthly_discount_description_days": "For rentals of 28 days or more", "monthly_discount_description_nights": "For stays of 28 nights or more", "cancellation_strict_1": "If the customer cancels at least 30 days before check-in, you will not receive any payment.", "cancellation_strict_2": "If the customer cancels between 30 and 14 days before check-in, you will receive 50% of the booking amount (minus the service fee).", "cancellation_strict_3": "If the customer cancels within 14 days of check-in, you will receive 100% of the booking amount (minus the service fee).", "important_notes_title": "Add Important Notes for Guests", "important_notes_description": "Add Any Additional Notes or Helpful Information for Your Guests.", "example_note": "E.g., The vehicle has a GPS tracker for safety purposes.", "guest_price_before_taxes": "Guest price before taxes", "host_service_fee": "Host service fee", "you_earn": "You earn", "see_more": "See more", "about_pricing": "about pricing", "learn_more": "Learn more", "one_night": "One Night", "two_nights": "Two Nights", "three_nights": "Three Nights", "four_nights": "Four Nights", "five_nights": "Five Nights", "need_help": "Need help?", "accepted_documents_info": "Click here to see the list of accepted documents.", "diesel": "Diesel", "electric": "Electric", "gasoline": "Gasoline (Petrol)", "hydrogen": "Hydrogen", "hybrid": "Hybrid", "no_engine": "No Engine", "other": "Other", "manual": "Manual", "automatic": "Automatic", "cvt": "CVT", "dct": "DCT", "semi_automatic": "Semi-Automatic", "no_transmission": "No Transmission", "enter_standout_feature": "Enter a standout feature (e.g., High-Performance Engine)", "describe_standout_feature": "Describe this feature in detail (e.g., A high-performance engine ensures a lot of fun, superior speed, and an unforgettable driving experience.)", "eg_features_list": "E.g., GPS, child seats, unlimited mileage, roadside assistance.", "your_files_have_been_uploaded": "Your files have been uploaded", "no_preparation_time_needed": "No preparation time needed", "one_day": "One Day", "two_days": "Two Days", "three_days": "Three Days", "four_days": "Four Days", "five_days": "Five Days", "six_days": "Six Days", "seven_days": "Seven Days", "publish": "Publish", "reviews": "Reviews", "review_listing_notice": "Here's what your guests will see. Take a moment to ensure everything is accurate and ready to go live."}, "user_account_setting": {"account_settings": "Account <PERSON><PERSON>", "get_started": "Manage your personal information, preferences, and security settings to enhance your experience on our platform.", "profile_picture": "Profile Picture", "profile_picture_tooltip": "Upload icon in PNG, JPEG or SVG and recommended dimension is 30 x 30", "update": "Update", "personal_information": "Personal Information", "first_name": "First Name", "last_name": "Last Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "Town/City", "state": "State", "country": "Country", "zip_code": "Zipcode/Postal", "save_changes": "Save", "change_password": "Change Password", "change_password_button": "Change Password", "enter_old_password": "Enter Old Password", "enter_new_password": "Enter New Password", "confirm_new_password": "Confirm New Password", "password_updated": "Password updated!", "current_password": "Current Password", "new_password": "New Password", "confirm_password": "Confirm Password", "session_history": "Session History", "start_time": "Start Time", "end_time": "End Time", "duration": "Duration", "ip_address": "IP Address", "location": "Location", "device": "<PERSON><PERSON>", "browser": "Browser", "platform": "Platform", "status": "Status", "active": "Active", "inactive": "Inactive", "current_session": "Current Session", "logout": "Logout", "action": "Action", "payment_methods": "Payment Methods", "add_manage_payment_methods": "Add and manage your payment methods", "default": "<PERSON><PERSON><PERSON>", "set_as_default": "Set as default", "remove": "Remove", "add_payment_method": "Add Payment Method", "card_number": "Card Number", "no_saved_cards": "You don't have any saved cards", "mm_yyyy": "MM/YYYY", "card_holder_name": "Card Holder Name", "cardholder_name": "Cardholder Name", "loading": "Loading...", "card_added_successfully": "Card Added Successfully", "add_card": "Add Card", "profile_updated_successfully": "Profile updated successfully!", "ok": "OK", "an_error_occurred": "An error occurred. Please try again.", "are_you_sure": "Are you sure?", "logout_session_confirmation": "This will log out the selected session!", "yes_logout": "Yes, logout!", "cancel": "Cancel", "session_logged_out_successfully": "Session logged out successfully!", "failed_to_logout_session": "Failed to logout session. Please try again.", "verification": "Verification", "kyc_verification": "KYC Verification", "verify_now": "Verify now >", "identity_verification": "Identity Verification", "upload_document": "Upload Document", "document_type": "Document Type", "document_number": "Document Number", "expiry_date": "Expiry Date", "upload_image": "Upload Image", "submit": "Submit", "notification_settings": "Notification Settings", "email_notifications": "Email Notifications", "sms_notifications": "SMS Notifications", "push_notifications": "Push Notifications", "privacy_settings": "Privacy Settings", "profile_visibility": "Profile Visibility", "public": "Public", "private": "Private", "delete_account": "Delete Account", "success": "Success!", "error": "Error", "delete_account_warning": "Warning: This action cannot be undone. All your data will be permanently deleted."}, "user_booking_details": {"report_listing": "Report Listing", "booking_id": "Booking ID", "new_host": "New Host", "hosted_by": "Hosted by", "contact_host": "Contact Host", "booking_details": "Booking Details", "check_in": "Check-in", "check_out": "Check-out", "payment_status": "Payment Status", "booking_status": "Booking Status", "paid": "Paid", "cancelled": "Cancelled", "confirmed": "Confirmed", "completed": "Completed", "download_invoice": "Download Invoice", "cancel_booking": "Cancel Booking", "subject": "Subject", "description": "Description", "submit": "Submit", "read_more": "Read More", "read_less": "Read Less", "cancellation_policy": "Cancellation Policy", "refund_amount": "Refund Amount", "refund_percentage": "Refund Percentage", "cancel_my_booking": "Cancel my Booking", "keep_my_booking": "Keep my Booking", "reviews": "Reviews", "rules": "Rules", "pets": "Pets", "amenities": "Amenities", "everything_included_in_this_place": "Everything included in this place", "need_assistance": "Need Assistance? Get Help Now!", "contact_support": "Contact Support", "go_to_help_center": "Go to Help Center", "pricing_details": "Pricing Details", "price_x_adult": "Price x Adult", "price_x_child": "Price x Child", "total": "Total", "download_booking_confirmation": "Download Booking Confirmation", "private_booking_price": "Private Booking Price", "listing_price": "Listing Price", "booking_type": "Booking Type", "tour_type": "Tour Type", "total_adult": "Total Adult", "total_child": "Total Child", "total_price": "Total Price", "sub_total": "Sub Total", "total_paid_in_usd": "Total Paid in USD", "payment_method": "Payment Method", "slots": "Slots", "things_that_you_need_to_know": "Things that you need to know", "not_provided": "Not Provided", "multiple_days": "Multiple Days", "start_day": "Start Day", "end_day": "End Day", "tour_duration_type": "Tour Duration Type", "departure_return_date": "Departure & Return Date", "start_end_date": "Start & End Date", "pickup_dropoff_date": "Pickup & Dropoff Date", "checkin_checkout_date": "Check-in & Check-out Date", "pickup_date": "Pickup Date", "dropoff_date": "Dropoff Date", "departure_date": "Departure Date", "return_date": "Return Date", "checkin_date": "Check-in Date", "checkout_date": "Check-out Date", "pickup_time": "Pickup Time", "dropoff_time": "Dropoff Time", "departure_time": "Departure Time", "return_time": "Return Time", "booking_name": "Booking Name", "guests": "Guests", "booking_address": "Booking Address", "zip_code": "Zip Code", "suite": "Suite", "night": "Night", "day": "Day", "experience": "Experience", "total_hours": "Total Hours", "adult": "Adult", "adults": "Adults", "child": "Child", "children": "Children", "childrens": "Childrens", "change_booking_details": "Change Booking Details", "luxustars_secure_payment_assurance": "LuxuStars Secure Payment Assurance", "your_peace_of_mind_our_priority": "Your Peace of Mind, Our Priority", "at_luxustars_all_payments_are_securely_processed": "At LuxuStars, all payments are securely processed using the latest encryption technologies and protected by 3D Secure authentication for eligible cards. By keeping transactions within our platform, you're guaranteed:", "24_7_support": "24/7 Support", "payment_protection": "Payment Protection", "cancellation_coverage": "Cancellation Coverage", "assistance_whenever_you_need_it": "Assistance whenever you need it", "your_funds_are_held_safely_until_the_booking_is_completed": "Your funds are held safely until the booking is completed", "enjoy_policies_tailored_to_your_needs": "Enjoy policies tailored to your needs", "experience_luxury_with_confidence": "Experience luxury with confidence, knowing we've got you covered every step of the way", "are_you_sure": "Are you sure?", "no_refund": "No Refunds", "fully_refunded": "Fully Refunded", "partially_refunded": "Partially Refunded", "show_more": "Show More", "show_less": "Show Less"}, "user_bookings": {"booking_id": "Booking ID", "no_booking_found": "No Booking Found", "my_bookings": "My Bookings", "past_booking": "Past Booking", "on_going_booking": "On Going Bookings", "host_name": "Host Name", "from": "From", "to": "To", "property_name": "Property/Asset Name", "price": "Price", "review": "Review", "booking_detail": "Booking Detail", "customer_name": "Customer Name", "listing": "Listing", "listing_basis_type": "Listing Basis Type", "date": "Date", "check_in": "Check In", "check_out": "Check Out", "total_paid_by_customer": "Total Paid by Customer", "total_payout": "Total Payout", "booked": "Booked", "status": "Status", "created_at": "Created At", "search_here": "Search here"}, "user_management": {"user_management": "User Management", "users": "Users", "name": "Name", "email": "Email", "phone": "Phone", "status": "Status", "action": "Action", "view": "View", "edit": "Edit", "delete": "Delete", "suspend": "Suspend", "unsuspend": "Unsuspend", "no_users_found": "No Users Found", "no_service_providers_found": "No Service Providers Found", "no_customers_found": "No Customers Found", "no_admins_found": "No Admins Found", "search": "Search", "search_by_name_email": "Search by Name or Email", "customers": "Customers", "service_providers": "Service Providers", "admins": "Admins", "add_admin": "Add Admin", "profile_name": "Profile Name", "phone_number": "Phone Number", "total_bookings": "Total Bookings", "total_expenditure": "Total Expenditure", "last_active": "Last Active", "ratings": "Ratings", "approved": "Approved", "suspended": "Suspended", "active_payment": "Active Payment", "hold_payment": "Hold Payment", "revenue_generated": "Revenue Generated", "total_listings": "Total Listings", "role": "Role", "first_name": "First Name", "last_name": "Last Name", "password": "Password", "confirm_password": "Confirm Password", "create": "Create", "update": "Update", "all_refundable_bookings": "All Refundable Bookings", "suspend_reason": "Suspend Reason", "confirm_action": "Confirm Action", "suspend_user_confirmation": "Do you want to suspend this user?", "yes_suspend": "Yes, Suspend", "unsuspend_user_confirmation": "Do you want to unsuspend this user?", "yes_unsuspend": "Yes, unsuspende it!", "confirm_delete_admin": "Are you sure you want to delete this admin?"}, "wishlist": {"wishlist": "Wishlist", "search": "Search", "new_listing": "New Listing", "booking_capacity": "Booking Capacity", "same_day_tour": "Same Day Tour", "days_tour": "Days Tour", "children_allowed": "Children allowed", "passengers": "passengers", "ft": "ft", "seats": "Seats", "wishlist_is_empty": "Wishlist is Empty", "deleted": "Deleted!", "your_file_has_been_deleted": "Your file has been deleted.", "card_holder_name": "Card Holder Name"}}