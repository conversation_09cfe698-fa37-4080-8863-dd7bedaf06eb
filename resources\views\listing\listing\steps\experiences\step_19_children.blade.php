@push('css')
    <style>
        .hidden-fieldset {
            visibility: hidden;
            height: 0;
            overflow: hidden;
        }
    </style>
@endpush

@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', 'child-price');
@endphp
<fieldset class="set_price_step set_price_children_step">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    @isset($step_data->sub_title)
                        <div class="content">
                            <p>{{ $step_data->sub_title ?? "" }}</p>
                        </div>
                    @endisset
                    <div class="content">
                        <p class="disclaimer">{{ translate('stepper.child_price_is_compulsory_for_children_booking') }}</p>
                    </div>
                    <div class="price_hidden_fields_wrapper scrollable-section">
                        <div class="setting_price_input">
                            <div class="txt_field">
                                <div class="icon_input_wrapper">
                                    <i class="fa fa-usd" aria-hidden="true"></i>
                                    <input class="hidden_price hidden_price_children" type="hidden" name="child_price"
                                        value="{{ ($listing->detail->child_price ?? '') == '0.00' ? '' : $listing->detail->child_price ?? '' }}">
                                    <input type="text" name="" pattern="^\d+(\.\d{0,2})?$" id="price_children"
                                        value="{{ ($listing->detail->child_price ?? '') == '0.00' ? '' : $listing->detail->child_price ?? '' }}"
                                        placeholder="0">
                                </div>
                                <span>COP</span>
                            </div>
                        </div>

                        <div class="hidden_price_breakdown_wrapper">
                            <div class="price_breakdown_wrapper" style="display: none;">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>{{ translate('stepper.guest_price_before_taxes') }} </th>
                                            <td>$<span class="guest_price"></span> COP</td>
                                        </tr>
                                        <tr>
                                            <th>{{ translate('stepper.host_service_fee') }}</th>
                                            <td>$<span class="service_fee"></span> COP</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <th>{{ translate('stepper.you_earn') }} </th>
                                            <td>$<span class="final_lisitng_price"></span> COP</td>
                                        </tr>
                                    </tbody>
                                    <tr class="spacer">
                                        <td colspan="2"></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="see_more_wrapper">
                                <a href="javascript:void(0)" class="btn see_more_btn">
                                    {{ translate('stepper.see_more') }} 
                                    <i class="fa fa-chevron-down" aria-hidden="true"></i>
                                </a>
                            </div>
                            <div class="learn_more_wrapper">
                                <a href="{{ $step_data->url }}" class="learn_more_pricing_btn" target="_blank">{{ translate('stepper.learn_more') }}
                                    {{ translate('stepper.about_pricing') }} </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script>
        $(document).ready(function() {
            $('.listing_stepper .set_price_children_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper')
                .hide();
            $(document).on('click',
                '.listing_stepper .set_price_children_step .hidden_price_breakdown_wrapper .see_more_wrapper .see_more_btn',
                function() {
                    $(this).closest('.hidden_price_breakdown_wrapper').find('.price_breakdown_wrapper').show();
                    $(this).removeClass('see_more_btn');
                    $(this).addClass('see_less_btn');
                    $(this).html('See less <i class="fa fa-chevron-up" aria-hidden="true"></i>');
                });
            $(document).on('click',
                '.listing_stepper .set_price_children_step .hidden_price_breakdown_wrapper .see_more_wrapper .see_less_btn',
                function() {
                    $(this).closest('.hidden_price_breakdown_wrapper').find('.price_breakdown_wrapper').hide();
                    $(this).addClass('see_more_btn');
                    $(this).removeClass('see_less_btn');
                    $(this).html('{{ translate('stepper.see_more') }}  <i class="fa fa-chevron-down" aria-hidden="true"></i>')
                });


            const $inputField = $('#price_children');
            const maxFontSize = 80;
            const minFontSize = 40;

            const initialWidth = $inputField.outerWidth();
            let previousValue = $inputField.val();

            $inputField.on('input', function() {
                setTimeout(() => {
                    const currentValue = $inputField.val();
                    // console.log(currentValue);
                    const tempSpan = $('<span>').css({
                        visibility: 'hidden',
                        fontFamily: 'Poppins',
                        padding: '0 10px',
                        id: 'tempspan',
                        fontWeight: '600',
                        whiteSpace: 'nowrap',
                        fontSize: `${maxFontSize}px`,
                    }).text(currentValue || $inputField.attr('placeholder'));
                    $('body').append(tempSpan);

                    // console.log('Temp Span Width: ' + tempSpan.outerWidth());

                    const inputWidth = $inputField.outerWidth();
                    let fontSize = maxFontSize;

                    while (tempSpan.outerWidth() > inputWidth && fontSize > minFontSize) {
                        fontSize--;
                        tempSpan.css('fontSize', `${fontSize}px`);
                    }

                    // console.log(fontSize);
                    // console.log(minFontSize);

                    if (fontSize <= minFontSize) {
                        let currentWidth = $inputField.outerWidth();
                        $inputField.css('width', tempSpan.outerWidth());
                        // console.log('increased');
                    }

                    if (currentValue.length < previousValue.length) {
                        let currentWidth = $inputField.outerWidth();
                        if (currentWidth > initialWidth) {
                            $inputField.css('width', tempSpan.outerWidth());
                        }
                    }

                    if (currentValue.trim() === '') {
                        $inputField.css('width', `${initialWidth}px`);
                        fontSize = maxFontSize;
                    }

                    $inputField.css('fontSize', `${fontSize}px`);
                    $('.listing_stepper .set_price_children_step .setting_price_input .icon_input_wrapper i')
                        .css(
                            'font-size', `${fontSize}px`);
                    $('.listing_stepper .set_price_children_step .inner_section_main_col .price_hidden_fields_wrapper .setting_price_input .txt_field > span')
                        .css({
                            'font-size': `${fontSize}px`,
                            'line-height': `${fontSize}px`,
                        });

                    tempSpan.remove();

                    previousValue = currentValue;
                }, 50);

            });


            setTimeout(() => {
                const currentValue = $inputField.val();
                // console.log(currentValue);
                const tempSpan = $('<span>').css({
                    visibility: 'hidden',
                    fontFamily: 'Poppins',
                    padding: '0 10px',
                    id: 'tempspan',
                    fontWeight: '600',
                    whiteSpace: 'nowrap',
                    fontSize: `${maxFontSize}px`,
                }).text(currentValue || $inputField.attr('placeholder'));
                $('body').append(tempSpan);

                // console.log('Temp Span Width: ' + tempSpan.outerWidth());

                const inputWidth = $inputField.outerWidth();
                let fontSize = maxFontSize;

                while (tempSpan.outerWidth() > inputWidth && fontSize > minFontSize) {
                    fontSize--;
                    tempSpan.css('fontSize', `${fontSize}px`);
                }

                // console.log(fontSize);
                // console.log(minFontSize);

                if (fontSize <= minFontSize) {
                    let currentWidth = $inputField.outerWidth();
                    $inputField.css('width', tempSpan.outerWidth());
                    // console.log('increased');
                }

                if (currentValue.length < previousValue.length) {
                    let currentWidth = $inputField.outerWidth();
                    if (currentWidth > initialWidth) {
                        $inputField.css('width', tempSpan.outerWidth());
                    }
                }

                if (currentValue.trim() === '') {
                    $inputField.css('width', `${initialWidth}px`);
                    fontSize = maxFontSize;
                }

                $inputField.css('fontSize', `${fontSize}px`);
                $('.listing_stepper .set_price_children_step .setting_price_input .icon_input_wrapper i')
                    .css('font-size',
                        `${fontSize}px`);
                $('.listing_stepper .set_price_children_step .inner_section_main_col .price_hidden_fields_wrapper .setting_price_input .txt_field > span')
                    .css({
                        'font-size': `${fontSize}px`,
                        'line-height': `${fontSize}px`,
                    });

                tempSpan.remove();

                previousValue = currentValue;

            }, 50);





            const inputField = document.getElementById("price_children");

            // function ensureDecimal() {
            //     if (!inputField.value.includes('.') && parseFloat(inputField.value) > 0) {
            //         inputField.value += '0.000';
            //     }
            // }

            // ensureDecimal();

            // function moveCursorToDecimal(event) {
            //     const cursorPos = inputField.selectionStart;
            //     const decimalIndex = inputField.value.indexOf('.');
            //     if (event.key === '.' || (event.key === 'ArrowRight' && cursorPos === decimalIndex)) {
            //         inputField.setSelectionRange(decimalIndex + 1, decimalIndex + 1);
            //         event.preventDefault();
            //     }
            // }
            // inputField.addEventListener('focus', ensureDecimal);
            // inputField.addEventListener('blur', ensureDecimal);
            // inputField.addEventListener('keydown', (event) => {
            //     if (!/[\d.]/.test(event.key) && !['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(
            //             event.key)) {
            //         event.preventDefault();
            //     }
            //     moveCursorToDecimal(event);
            // });

            // inputField.addEventListener('input', () => {
            //     let value = inputField.value;
            //     const parts = value.split('.');
            //     if (parts.length > 2) {
            //         value = parts[0] + '.' + parts[1];
            //     }
            //     if (value.includes('.')) {
            //         const [beforeDecimal, afterDecimal] = value.split('.');
            //         value = beforeDecimal + '.' + afterDecimal.slice(0, 3);
            //     }
            //     inputField.value = value;
            //     if (!value.includes('.')) {
            //         if (parseFloat(inputField.value) > 0) {
            //             inputField.value = value + '.000';
            //         } else {
            //             inputField.value = value + '0.000';
            //         }
            //     }
            // });



            // function setPriceBreakdown() {
            //     var price = $('.set_price_children_step #price_children').val();
            //     $('.set_price_children_step .price_breakdown_wrapper .guest_price').html(price);
            //     var serviceFee = price * {{ $category->tax }} / 100;
            //     var priceAfterDeduction = price - serviceFee;
            //     $('.set_price_children_step .price_breakdown_wrapper .service_fee').html(serviceFee.toFixed(3));
            //     $('.set_price_children_step .price_breakdown_wrapper .final_lisitng_price').html(priceAfterDeduction.toFixed(
            //         3));
            // }


            function setPriceBreakdown() {
                var price = parseFloat($('.set_price_children_step .hidden_price_children').val()) || 0;
                var tax = @js($category->tax);
                $('.set_price_children_step .price_breakdown_wrapper .guest_price').html(price.toLocaleString(
                    'en-US', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }));
                var serviceFee = (price * tax) / 100;
                var priceAfterDeduction = price - serviceFee;
                $('.set_price_children_step .price_breakdown_wrapper .service_fee').html(serviceFee.toLocaleString(
                    'en-US', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }));
                $('.set_price_children_step .price_breakdown_wrapper .final_lisitng_price').html(priceAfterDeduction
                    .toLocaleString('en-US', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    }));
            }


            // function setPriceBreakdown() {
            //     var price = parseFloat($('.set_price_children_step .hidden_price_children').val()) || 0;
            //     var tax = @js($category->tax);
            //     $('.set_price_children_step .price_breakdown_wrapper .guest_price').html(price.toFixed(
            //         3));
            //     var serviceFee = (price * tax) / 100;
            //     var priceAfterDeduction = price - serviceFee;
            //     $('.set_price_children_step .price_breakdown_wrapper .service_fee').html(serviceFee.toFixed(3));
            //     $('.set_price_children_step .price_breakdown_wrapper .final_lisitng_price').html(priceAfterDeduction.toFixed(
            //         3));
            // }


            $(document).on('keyup', '.set_price_children_step #price_children', function() {
                setPriceBreakdown();
            });

            setPriceBreakdown();

            $('#price_children').on('input', function() {
                let inputVal = $(this).val();
                $('.hidden_price_children').val($(this).val().replace(/,/g, ''));
                inputVal = inputVal.replace(/[^0-9]/g, '');
                const formattedVal = inputVal.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                $(this).val(formattedVal);
                $('#tempspan').html(formattedVal);
            });


            function settingSavedPrice() {
                var hiddenInputVal = $('.set_price_children_step .hidden_price_children').val();
                hiddenInputVal = hiddenInputVal.replace(/[^0-9]/g, '');
                // hiddenInputVal = hiddenInputVal.split('.')[0];
                const formattedVal = hiddenInputVal.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                $('#price_children').val(formattedVal);
            }

            settingSavedPrice();


            // function checkChildOption(){
            //     var childSelectedValue = $('input[name="child_allow"]:checked').val();
            //     let fieldset = $('.set_price_children_step');

            //     if(childSelectedValue === 'no'){
            //         fieldset.removeAttr('id'); // Remove ID so stepper doesn't recognize it
            //         fieldset.removeClass('active'); // Prevents it from becoming active
            //         fieldset.hide(); // Hide it completely
            //     } else {
            //         fieldset.attr('id', 'step-20'); // Restore step ID (Update ID accordingly)
            //         fieldset.show(); // Show it again
            //     }
            // }

            // $(document).on('change', 'input[name="child_allow"]', function(){
            //     checkChildOption();
            // });

            // checkChildOption();

        });

        // document.getElementById('price').addEventListener('input', function(e) {
        //     const value = e.target.value;
        //     const regex = /^\d*(\.\d{0,3})?$/;
        //     if (!regex.test(value)) {
        //         e.target.value = value.slice(0, -1);
        //     }
        // });
    </script>
@endpush
