@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "transmission-type");
@endphp
<fieldset class="select_categories_step min_stay_requirement_step">
    <div class="inner_section_fieldset h_100">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title }}</h2>
                        @isset($step_data->sub_title)
                            <p>{{ $step_data->sub_title }}</p>
                        @endisset
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_categories_main_col inner_section_transmission_main_col scrollable-section">
                    <div class="row">
                        @php
                            $transmissions = [
                                'manual_transmission.png' => translate('stepper.manual'),
                                'automatic_transmission.png' => translate('stepper.automatic'),
                                'cvt_transmission.png' => translate('stepper.cvt'),
                                'dct_transmission.png' => translate('stepper.dct'),
                                'semi_auto_transmission.png' => translate('stepper.semi_automatic'),
                                'not_allowed_icon.png' => translate('stepper.no_transmission'),
                                'no_transmission.png' => translate('stepper.other'),
                            ];
                        @endphp
                        {{-- Loop to be insert here --}}
                        @foreach ($transmissions as $image => $transmission)
                            <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 single_transmission_col">
                                <div class="inner_section_single_category">
                                    <input type="radio" name="transmission" value="{{ $transmission }}"
                                        @if (isset($listing) && $listing->detail->transmission == $transmission) checked
                                        {{-- @elseif ($loop->index == 0) checked --}}
                                         @endif
                                        id="transmission_{{ $loop->index }}">
                                    <label for="transmission_{{ $loop->index }}">
                                        <div class="category_icon_wrapper">
                                            <img src="{{ asset('website') }}/images/{{ $image }}" alt="">
                                        </div>
                                        <div class="category_title">
                                            <h5>{{ $transmission }}</h5>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" disabled />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script>
        $(document).ready(function() {

            function listingTransmissionValidation() {
                var checkedTransCount = 0;
                var test = 0;
                $('.select_categories_step .inner_section_transmission_main_col .single_transmission_col').each(function() {
                    if ($(this).find('input[type="radio"]').is(':checked')) {
                        checkedTransCount++;
                    }
                });
                if (checkedTransCount == 0) {
                    $('.select_categories_step:has(.inner_section_transmission_main_col) .next').prop('disabled', true);
                    console.log('Not Checked Transmissiom');

                } else {
                    $('.select_categories_step:has(.inner_section_transmission_main_col) .next').prop('disabled', false);
                }
            }

            listingTransmissionValidation();

            $(document).on('change',
                '.select_categories_step .inner_section_transmission_main_col .single_transmission_col input[type="radio"]',
                function() {
                    listingTransmissionValidation();
                });
        });
    </script>
@endpush