@push('css')
    {{-- <link rel="stylesheet" href="https://cdn.ckeditor.com/ckeditor5/44.1.0/ckeditor5.css"> --}}
    {{-- <link rel="stylesheet" href="{{ asset('website/assets_cdn/summernote/summernote.min.css') }}"> --}}
    {{-- @php
        $froalaLinks = [
            'image.min.css',
            'table.min.css',
            'video.min.css',
            'code_view.min.css',
            'colors.min.css',
            'emoticons.min.css',
            'image_manager.min.css',
            'line_breaker.min.css',
            'char_counter.min.css',
            'fullscreen.min.css',
            'file.min.css',
            'draggable.min.css',
            // 'quick_insert'
        ];
    @endphp --}}
    {{-- <link rel="stylesheet" href="{{ asset('website/assets_cdn/froala_editor/css/froala_editor.min.css') }}"> --}}

    {{-- @foreach ($froalaLinks as $froalaLink)
        <link rel="stylesheet" href="{{ asset('website/assets_cdn/froala_editor/css/plugins') . '/' . $froalaLink }}">
    @endforeach --}}

    <link rel="stylesheet" href="{{ asset('website/assets_cdn/jodit_editor/jodit.min.css') }}">
    <link rel="stylesheet" href="{{ asset('website/assets_cdn/jodit_editor/plugins/speech-recognize/speech-recognize.min.css') }}">

    <style>
        .ck.ck-balloon-panel.ck-balloon-panel_position_border-side_right.ck-powered-by-balloon {
            display: none !important;
        }

        .cke_chrome {display: none !important;}

        .cmsForms .ck-editor__editable {
            border-radius: 12px;
            border: .5px solid #9b9b9b;
            box-shadow: unset;
        }

        .cmsForms .ck-editor__editable_inline {
            min-height: calc(50px * 5);
        }

        .cmsForms .ck .ck-placeholder:before,
        .key_features_step .key_features_fields_wrapper .ck.ck-placeholder:before {
            color: #A9AEC0; font-weight: 400
        }


        .custom_link_popup {display: flex; flex-direction: column; gap: 5px}
        .custom_link_popup * {font-family: "Poppins"}
        .custom_link_popup input[type="text"] {height: 45px; border: 1px solid #b8b8b8; border-radius: 10px; padding-left: 10px; padding-right: 10px; color: black; font-family: "Poppins"}
        .custom_link_popup button, .jodit-ui-button {width: 100%; padding: 10px 0; background-color: #ffce32; color: black; border-radius: 10px; font-weight: 500}
        .custom_link_popup br {display: none;}
        .jodit-ui-button {display: flex; justify-content: center; align-items: center; gap: 5px;}
        body .jodit-ui-button .jodit-ui-button__text {flex-grow: unset;}
        body .jodit-ui-button:hover, .custom_link_popup button:hover {background-color: #000 !important; color: white}
        body .jodit-ui-button:hover .jodit-ui-button__icon svg {fill: white;}

        .custom_anchor_popup {max-height: 240px; overflow-y: auto; min-width: 150px;}
        .custom_anchor_popup div:not(:last-child) {border-bottom: 1px solid lightgray; padding-bottom: 9px;}
        .custom_anchor_popup div:not(:first-child) {padding-top: 5px}

        .jodit-tabs__button {display: flex; justify-content: center; align-items: center;} 
        .jodit-tabs__button[aria-pressed="true"] {background-color: #ffce32 !important; color: black;}
        .jodit-tabs__button[aria-pressed="false"] {background-color: #000; color: white !important;}
        .jodit-tabs__button .jodit-ui-button__text {flex-grow: unset !important; flex-basis: fit-content; background: unset !important;}
        .jodit-tabs__button[aria-pressed="true"] .jodit-ui-button__text {color: black}
        .jodit-tabs__button[aria-pressed="true"]:hover .jodit-ui-button__text {color: black}
        .jodit .jodit-popup .jodit-tabs__button[aria-pressed="true"]:hover {background: #ffce32 !important;}
        .jodit-tabs__button[aria-pressed="false"] .jodit-ui-button__text {color: white;}
        .jodit-container .jodit-toolbar__box .jodit-ui-group_group_insert .jodit-toolbar-button_link {display: none;}
        .custom_anchor_popup:not(:has(div)) {position: relative; display: flex; justify-content: center; align-items: center}
        .custom_anchor_popup:not(:has(div)):before {content: "No anchors found";}
        body .jodit-dialog__panel {width: 500px;}
        .jodit-ui-group__file {display: none;}

    </style>
@endpush
{{-- Faq add and remove --}}
<div id="faq_pane">
    <section class="det_form cmsForms">
        <div class="row">
            <div class="col-md-12">
                <div class="form_field_padding {{ $errors->has('title') ? 'has-error' : '' }}">
                    <label for="title" class="control-label">{{ translate('dashboard_help_center.how_would_you_like_to_name_it') }}</label>
                    <input class="form-control m-input" name="title" type="text" id="title"
                        value="{{ $helpcenter->title ?? '' }}" placeholder="{{ translate('dashboard_help_center.billing') }}">
                    {!! $errors->first('title', '<p class="help-block">:message</p>') !!}
                </div>
            </div>
            <div class="col-md-12">
                <div class="form_field_padding {{ $errors->has('description') ? 'has-error' : '' }}">
                    <label for="description" class="control-label">{{ translate('dashboard_help_center.describe_what_this_category_is_about') }} </label>
                    {{-- <textarea class="form-control m-input" name="description" rows="8" id="description"
                        placeholder="E.g., Everything related to payment issues.">{{ $helpcenter->description ?? '' }}</textarea>
                    {!! $errors->first('description', '<p class="help-block">:message</p>') !!} --}}


                    <textarea class=" no_validate" id="editor" name="description" placeholder="{{ translate('dashboard_help_center.example_description') }}">{{ $helpcenter->description ?? '' }}
                    </textarea>
                    {!! $errors->first('description', '<p class="help-block">:message</p>') !!}

                </div>
            </div>
            <div class="col-md-12">
                <button class="btn btn_yellow">{{ $submitButtonText ?? translate('dashboard_help_center.create') }}</button>
            </div>
            {{-- <div class="col-md-12">
                <form id="faqForm" action="{{ route('add_faq') }}" method="POST">
                    @csrf
                    <div id="inputFormRow">
                        @forelse ($helpcenter->faqs ?? [] as $faq)
                            <div class="inputRow">
                                <div class="content-between">
                                    <h1>FAQ #{{ $loop->iteration }}</h1>
                                    @if (!$loop->first)
                                        <button type="button" class="btn btn-danger removeRow">Remove</button>
                                    @endif
                                </div>
                                <div class="form_field_padding">
                                    <label for="">Title</label>
                                    <input type="text" class="form-control m-input"
                                        name="items[{{ $loop->index }}][name]" value="{{ $faq->title }}"
                                        placeholder="Enter item name" autocomplete="off" />
                                </div>
                                <div class="form_field_padding">
                                    <label for="">Description</label>
                                    <textarea class="form-control m-input editor" name="items[{{ $loop->index }}][description]"
                                        placeholder="Enter description" autocomplete="off">{!! htmlspecialchars_decode($faq->description) !!}</textarea>
                                </div>
                            </div>
                        @empty
                            <div class="inputRow">
                                <div class="content-between">
                                    <h1>FAQ #1</h1>
                                </div>
                                <div class="form_field_padding">
                                    <label for="">Title</label>
                                    <input type="text" class="form-control m-input" name="items[0][name]"
                                        placeholder="Enter item name" autocomplete="off" />
                                </div>
                                <div class="form_field_padding">
                                    <label for="">Description</label>
                                    <textarea class="form-control m-input editor" name="items[0][description]" placeholder="Enter description"
                                        autocomplete="off"></textarea>
                                </div>
                            </div>
                        @endforelse
                    </div>


                    <div class="row">
                        <div class="col-md-12">
                            <div class="form_field_padding">
                                <div class="list_form_btn">
                                    <input class="btn cancel_btn add" id="addRow" type="button"
                                        value="Add More Questions">
                                    <input class="btn create_btn" type="submit"
                                        value="{{ $submitButtonText ?? 'Create' }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div> --}}
        </div>
    </section>
</div>
{{-- Faq add and remove end --}}

@push('js')
    {{-- <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script> --}}

    {{-- <script type="text/javascript" src="{{ asset('website/assets_cdn/froala_editor/js/froala_editor.min.js') }}"></script> --}}

    <script type="text/javascript" src="{{ asset('website/assets_cdn/jodit_editor/jodit.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('website/assets_cdn/jodit_editor/plugins/speech-recognize/speech-recognize.min.js') }}"></script>

    {{-- <script type="text/javascript" src="{{ asset('website/assets_cdn/jodit_editor/customLinkType.js') }}"></script> --}}
    {{-- <script type="text/javascript" src="{{ asset('website/assets_cdn/jodit_editor/anchorManager.js') }}"></script> --}}

    {{-- @php
        $froalaCDNs = [
            'image.min.js',
            'table.min.js',
            'video.min.js',
            'code_view.min.js',
            'link.min.js',
            'lists.min.js',
            'paragraph_format.min.js',
            'emoticons.min.js',
            'paragraph_style.min.js',
            // 'quick_insert.min.js',
            'quote.min.js',
            'save.min.js',
            'url.min.js',
            'draggable.min.js',
            
        ];
    @endphp --}}

    {{-- @foreach ($froalaCDNs as $froalaCDN)
        <script type="text/javascript" src="{{ asset('website/assets_cdn/froala_editor/js/plugins') . '/' . $froalaCDN }}"></script>
    @endforeach --}}

    <script>

        const anchorLinks = {};
    
        const editor = new Jodit('#editor', {
            controls: {
                anchorManager: {
                    iconURL: `{{ asset('website/images/bookmark.svg') }}`
                }
            },
            events: {
                afterInit: function (editor) {
                    const style = editor.createInside.element('style');
                    style.textContent = `
                        .jodit-anchor {
                            background-color: rgba(173, 216, 230, 0.3);
                            border-bottom: 1px dashed #4a90e2;
                            padding: 0 2px;
                        }
                        .jodit-anchor:hover::after {
                            content: ' #' attr(id);
                            color: #666;
                            font-size: 0.8em;
                        }
                    `;
                    editor.editorDocument.head.appendChild(style);
                }
            },
            uploader: {
                insertImageAsBase64URI: false,
                url: `{{ url('help-center-image') }}`,
                format: 'json',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                prepareData: function (data) {
                    return data;
                },
                isSuccess: function (resp) {
                    return resp.success;
                },
                getMsg: function (resp) {
                    return resp.error || 'Unknown error';
                },
                process: function (resp) {
                    return {
                        files: [resp.url],
                        path: resp.url,
                        baseurl: resp.baseurl,
                        error: resp.error || null,
                        message: resp.message || null
                    };
                },
                defaultHandlerSuccess: function (data) {
                    if (data.files && data.files.length) {
                        this.selection.insertImage(data.files[0]);
                    }
                },
                error: function (e) {
                    // alert('Error uploading image: ' + e.message);
                    Swal.fire({
                        icon: 'error',
                        title: @json(translate('dashboard_help_center.upload_failed')),
                        text: @json(translate('dashboard_help_center.error_uploading_images')) + e.message,
                        confirmButtonText: 'OK'
                    });
                }
            },
            toolbar: [
                'bold', 'italic', 'underline', 'strikethrough', '|',
                'font', 'fontsize', 'brush', 'paragraph', '|',
                'align', 'outdent', 'indent', '|',
                'insertOrderedList', 'insertUnorderedList', '|',
                'link', 'unlink', 'image', 'video', 'table', '|',
                'undo', 'redo', '|',
                'cut', 'copy', 'paste', 'selectall', '|',
                'subscript', 'superscript', '|',
                'hr', 'fontcolor', 'backcolor', '|',
                'left', 'center', 'right', 'justify', '|',
                'fullscreen', 'source', 'print', '|',
                'anchor', 'paragraphFormat', 'blockquote', 'code', 'emoticon', 'indent', 'outdent', '|',
                '|', 'ul', 'ol',
                'customLink', 'anchorManager'
            ],
            extraButtons: [
                {
                    name: 'customLink',
                    icon: 'link',
                    tooltip: 'Insert Link',
                    group: 'insert',
                    exec: (editor) => {
                        // Capture selection when popup is opened
                        const domSelection = window.getSelection();
                        const domRange = domSelection.rangeCount > 0 ? domSelection.getRangeAt(0) : null;
                        const selectedText = domSelection.toString();
                        console.log('Initial Selection:', selectedText, 'Range Start:', domRange?.startContainer);

                        const popup = document.createElement('div');
                        popup.classList.add('custom_link_popup');
                        popup.style.padding = '10px';

                        const typeSelect = document.createElement('select');
                        typeSelect.innerHTML = `
                            <option value="external">External Link</option>
                            <option value="internal">Internal Link</option>
                        `;

                        const urlInput = document.createElement('input');
                        urlInput.type = 'text';
                        urlInput.placeholder = 'Enter URL or anchor';
                        urlInput.style.marginTop = '5px';

                        const btn = document.createElement('button');
                        btn.innerText = 'Insert Link';
                        btn.style.marginTop = '10px';

                        btn.addEventListener('mousedown', (event) => {
                            event.preventDefault();
                            event.stopPropagation();

                            // Focus editor
                            const contentEditable = editor.editor.querySelector('[contenteditable="true"]');
                            if (contentEditable) {
                                contentEditable.focus();
                            }

                            let href = urlInput.value.trim();
                            if (!href) {
                                alert('Please enter a valid URL or anchor');
                                return;
                            }
                            if (typeSelect.value === 'internal') {
                                href = '#' + href.replace(/^#*/, '');
                            }

                            const textToUse = selectedText || href; // Use highlighted text, fallback to href
                            console.log('Inserting Link:', href, 'Text:', textToUse, 'Range:', domRange);

                            setTimeout(() => {
                                // Restore selection
                                if (domRange) {
                                    const newSelection = window.getSelection();
                                    newSelection.removeAllRanges();
                                    newSelection.addRange(domRange);
                                }

                                // Remove existing link if it exists at the same position
                                const existing = editor.s.sel?.focusNode?.parentElement?.closest('a[href]');
                                if (existing) {
                                    const parent = existing.parentNode;
                                    const textContent = existing.textContent;
                                    const textNode = document.createTextNode(textContent);
                                    parent.replaceChild(textNode, existing);
                                }

                                // Create link
                                const a = document.createElement('a');
                                a.href = href;
                                a.textContent = textToUse;
                                a.classList.add(typeSelect.value === 'external' ? 'jodit-link' : 'jodit-anchor');

                                // Insert link
                                if (domRange && selectedText) {
                                    try {
                                        domRange.deleteContents();
                                        domRange.insertNode(a);
                                        console.log('Link inserted at range:', a.parentNode);
                                    } catch (e) {
                                        console.error('DOM insertNode failed:', e);
                                        contentEditable.appendChild(a); // Fallback
                                    }
                                } else {
                                    // Fallback: insert at cursor
                                    const newRange = document.createRange();
                                    const selection = window.getSelection();
                                    if (selection.rangeCount) {
                                        newRange.setStart(selection.focusNode, selection.focusOffset);
                                        newRange.insertNode(a);
                                    } else {
                                        contentEditable.appendChild(a);
                                    }
                                }

                                // Place cursor after link
                                const newRange = document.createRange();
                                newRange.setStartAfter(a);
                                const selection = window.getSelection();
                                selection.removeAllRanges();
                                selection.addRange(newRange);

                                // Sync editor
                                editor.synchronizeValues();

                                // Scroll to link
                                setTimeout(() => {
                                    const newLink = editor.editor.querySelector(`a[href="${href}"]`);
                                    if (newLink) {
                                        newLink.scrollIntoView({ behavior: 'smooth' });
                                    } else {
                                        console.error('Link not found:', href);
                                    }
                                }, 100);

                                popup.remove();
                            }, 100);
                        });

                        popup.appendChild(typeSelect);
                        popup.appendChild(document.createElement('br'));
                        popup.appendChild(urlInput);
                        popup.appendChild(document.createElement('br'));
                        popup.appendChild(btn);

                        // Position popup
                        const button = editor.toolbar.buttons.find(button => button.name === 'customLink');
                        const buttonRect = button.container.getBoundingClientRect();
                        const popupPosition = {
                            top: buttonRect.bottom + window.scrollY + 5,
                            left: buttonRect.left + window.scrollX
                        };

                        popup.style.position = 'absolute';
                        popup.style.top = `${popupPosition.top}px`;
                        popup.style.left = `${popupPosition.left}px`;
                        popup.style.background = '#fff';
                        popup.style.border = '1px solid #ccc';
                        popup.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
                        document.body.appendChild(popup);

                        // Adjust popup if it overflows
                        const spaceRight = window.innerWidth - popupPosition.left - popup.offsetWidth;
                        if (spaceRight < 20) {
                            popupPosition.left = Math.max(buttonRect.left - popup.offsetWidth, 0);
                            popup.style.left = `${popupPosition.left}px`;
                        }

                        // Close popup on outside click
                        document.addEventListener('click', function closePopup(event) {
                            if (!popup.contains(event.target) && !button.container.contains(event.target)) {
                                popup.remove();
                                document.removeEventListener('click', closePopup);
                            }
                        });
                    }
                },
                {
                    name: 'anchorManager',
                    icon: 'bookmark',
                    tooltip: 'Manage Anchors',
                    group: 'insert',
                    exec: (editor) => {
                        // Capture selection when popup is opened
                        const domSelection = window.getSelection();
                        const domRange = domSelection.rangeCount > 0 ? domSelection.getRangeAt(0) : null;
                        const selectedText = domSelection.toString();
                        console.log('Initial Selection:', selectedText, 'Range Start:', domRange?.startContainer);

                        const popup = document.createElement('div');
                        popup.classList.add('custom_anchor_popup');
                        popup.style.padding = '10px';

                        const anchors = getAnchorsWithUsageStatus(editor);

                        Object.keys(anchors).forEach(link => {
                            const item = document.createElement('div');
                            item.innerText = link;
                            item.style.color = anchors[link] ? 'gray' : 'black';
                            item.style.cursor = 'pointer';
                            item.style.margin = '5px 0';

                            item.addEventListener('mousedown', (event) => {
                                event.preventDefault(); // Prevent focus shift
                                event.stopPropagation();

                                // Focus editor
                                const contentEditable = editor.editor.querySelector('[contenteditable="true"]');
                                if (contentEditable) {
                                    contentEditable.focus();
                                }

                                const id = link.slice(1);
                                const textToUse = selectedText || id; // Use initial selection, fallback to id

                                // Log for debugging
                                console.log('Inserting Anchor:', id, 'Text:', textToUse, 'Range:', domRange);

                                setTimeout(() => {
                                    // Restore selection
                                    if (domRange) {
                                        const newSelection = window.getSelection();
                                        newSelection.removeAllRanges();
                                        newSelection.addRange(domRange);
                                    }

                                    // Remove existing anchor
                                    const existing = editor.editor.querySelector(`[id="${id}"]`);
                                    if (existing) {
                                        const parent = existing.parentNode;
                                        const textContent = existing.textContent;
                                        const textNode = document.createTextNode(textContent);
                                        parent.replaceChild(textNode, existing);
                                    }

                                    // Create anchor
                                    const anchorEl = document.createElement('a');
                                    anchorEl.id = id;
                                    anchorEl.textContent = textToUse;
                                    anchorEl.classList.add('jodit-anchor');

                                    // Insert anchor
                                    if (domRange && selectedText) {
                                        try {
                                            domRange.deleteContents();
                                            domRange.insertNode(anchorEl);
                                            console.log('Anchor inserted at range:', anchorEl.parentNode);
                                        } catch (e) {
                                            console.error('DOM insertNode failed:', e);
                                            contentEditable.appendChild(anchorEl); // Fallback
                                        }
                                    } else {
                                        // Fallback: append at cursor
                                        const newRange = document.createRange();
                                        const selection = window.getSelection();
                                        if (selection.rangeCount) {
                                            newRange.setStart(selection.focusNode, selection.focusOffset);
                                            newRange.insertNode(anchorEl);
                                        } else {
                                            contentEditable.appendChild(anchorEl);
                                        }
                                    }

                                    // Place cursor after anchor
                                    const newRange = document.createRange();
                                    newRange.setStartAfter(anchorEl);
                                    const selection = window.getSelection();
                                    selection.removeAllRanges();
                                    selection.addRange(newRange);

                                    // Sync editor
                                    editor.synchronizeValues();

                                    // Scroll to anchor
                                    setTimeout(() => {
                                        const newAnchor = editor.editor.querySelector(`[id="${id}"]`);
                                        if (newAnchor) {
                                            newAnchor.scrollIntoView({ behavior: 'smooth' });
                                        } else {
                                            console.error('Anchor not found:', id);
                                        }
                                    }, 100);

                                    popup.remove();
                                }, 100);
                            });

                            popup.appendChild(item);
                        });

                        // Position popup
                        const button = editor.toolbar.buttons.find(button => button.name === 'anchorManager');
                        const buttonRect = button.container.getBoundingClientRect();
                        const popupPosition = {
                            top: buttonRect.bottom + window.scrollY + 5,
                            left: buttonRect.left + window.scrollX
                        };

                        popup.style.position = 'absolute';
                        popup.style.top = `${popupPosition.top}px`;
                        popup.style.left = `${popupPosition.left}px`;
                        popup.style.background = '#fff';
                        popup.style.border = '1px solid #ccc';
                        popup.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
                        document.body.appendChild(popup);

                        // Adjust popup if it overflows
                        const spaceRight = window.innerWidth - popupPosition.left - popup.offsetWidth;
                        if (spaceRight < 20) {
                            popupPosition.left = Math.max(buttonRect.left - popup.offsetWidth, 0);
                            popup.style.left = `${popupPosition.left}px`;
                        }

                        // Close popup on outside click
                        document.addEventListener('click', function closePopup(event) {
                            if (!popup.contains(event.target) && !button.container.contains(event.target)) {
                                popup.remove();
                                document.removeEventListener('click', closePopup);
                            }
                        });
                    }
                },
                {
                    name: 'fileUpload',
                    icon: 'file',
                    tooltip: 'Upload File',
                    group: 'insert',
                    exec: (editor) => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = '*/*';
                        input.style.display = 'none';
                        document.body.appendChild(input);

                        input.addEventListener('change', (event) => {
                            const file = event.target.files[0];
                            if (!file) return;

                            const formData = new FormData();
                            formData.append('file', file);

                            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
                            if (!csrfToken) {
                                console.error('CSRF token not found');
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Failed',
                                    text: 'CSRF token not found',
                                    confirmButtonText: 'OK'
                                });
                                document.body.removeChild(input);
                                return;
                            }

                            fetch(`{{ url('help-center-image') }}`, {
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': csrfToken,
                                    'Accept': 'application/json'
                                },
                                body: formData
                            })
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error(`HTTP error! Status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(data => {
                                if (data.success) {
                                    const a = document.createElement('a');
                                    a.href = data.url;
                                    a.textContent = data.filename || file.name || 'Download File';
                                    a.classList.add('jodit-file-link');
                                    a.setAttribute('download', '');
                                    editor.selection.insertNode(a);

                                    const newRange = document.createRange();
                                    newRange.setStartAfter(a);
                                    const selection = window.getSelection();
                                    selection.removeAllRanges();
                                    selection.addRange(newRange);

                                    editor.synchronizeValues();
                                } else {
                                    throw new Error(data.error || 'File upload failed');
                                }
                            })
                            .catch(err => {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Upload Failed',
                                    text: 'Error uploading file: ' + err.message,
                                    confirmButtonText: 'OK'
                                });
                            })
                            .finally(() => {
                                document.body.removeChild(input);
                            });
                        });

                        input.click();
                    }
                },
            ]
        });

        // Initialize previousImages
        if (!editor.data) editor.data = {};
        editor.data.previousImages = [];

        // // Track image insertions with afterInsertNode (as a fallback)
        // editor.events.on('afterInsertNode', function (node) {
        //     try {
        //         if (node.nodeName === 'IMG') {
        //             console.log('Image inserted:', node.src);
        //             const currentImages = Array.from(editor.element.querySelectorAll('img')).map(img => img.src);
        //             editor.data.previousImages = currentImages;
        //             console.log('Updated previousImages after insertion:', editor.data.previousImages);
        //         }
        //     } catch (error) {
        //         console.error('Error in afterInsertNode event:', error);
        //     }
        // });

        editor.events.on('afterRemoveNode', function (node) {
            try {
                console.log('afterRemoveNode triggered, node:', node.nodeName);
                if (node.nodeName === 'IMG') {
                    const deletedImageUrl = node.src;
                    console.log('Image removed:', deletedImageUrl);

                    // Get the CSRF token
                    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
                    if (!csrfToken) {
                        console.error('CSRF token not found');
                        return;
                    }

                    // Send the fetch request to delete the image
                    console.log('Attempting to delete image:', deletedImageUrl);
                    const deleteUrl = `{{ url('help-center-image/delete') }}`;
                    console.log('Delete URL:', deleteUrl);

                    fetch(deleteUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({ image_url: deletedImageUrl })
                    })
                    .then(response => {
                        console.log('Fetch response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Delete Response:', data);
                        if (!data.success) {
                            console.error('Server error:', data.error);
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting image:', error);
                    });
                } else if (node.nodeName === 'A' && node.classList.contains('jodit-file-link')) {
                    const deletedFileUrl = node.href;
                    console.log('File link removed:', deletedFileUrl);

                    fetch(deleteUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrfToken,
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({ file_url: deletedFileUrl })
                    })
                    .then(response => {
                        console.log('File delete response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('File delete response:', data);
                        if (!data.success) {
                            console.error('Server error:', data.error);
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting file:', error);
                    });
                }
            } catch (error) {
                console.error('Error in afterRemoveNode event:', error);
            }
        });

        // Bind change event (for debugging)
        // editor.events.on('change', function () {
        //     try {
        //         console.log('change event triggered');
        //         const currentImages = Array.from(editor.element.querySelectorAll('img')).map(img => img.src);
        //         const previousImages = editor.data.previousImages || [];
        //         const deletedImages = previousImages.filter(url => !currentImages.includes(url));

        //         console.log('Current Images:', currentImages);
        //         console.log('Previous Images:', previousImages);
        //         console.log('Deleted Images:', deletedImages);

        //         // Update previousImages
        //         editor.data.previousImages = currentImages;
        //     } catch (error) {
        //         console.error('Error in change event:', error);
        //     }
        // });

        function getInternalAnchorLinks(editor) {
            const anchors = {};
            editor.editor.querySelectorAll('a[href^="#"]').forEach(a => {
                const href = a.getAttribute('href');
                if (href && href.startsWith('#')) {
                    anchors[href] = false;
                }
            });
            return anchors;
        }

        function getAnchorsWithUsageStatus(editor) {
            const anchors = getInternalAnchorLinks(editor);

            // Mark anchors that have associated <a id="..."> or any element with matching id
            Object.keys(anchors).forEach(href => {
                const id = href.slice(1);
                const existing = editor.editor.querySelector(`[id="${id}"]`);
                anchors[href] = !!existing;
            });

            return anchors;
        }

    </script>


    {{-- <script>
        let editorInstanceDescription;

        ClassicEditor
            .create(document.querySelector('#editor_description'), {
                enterMode: 'paragraph',
                shiftEnterMode: 'softBreak',
                // toolbar: false,
                toolbar: [
                    'bold',
                    'italic',
                    'link',
                    'bulletedList',
                    'numberedList',
                    'blockQuote',
                    'underline',
                    'strikethrough',
                    'code',
                    'imageUpload',
                    'mediaEmbed',
                    'undo',
                    'redo',
                    'fontSize',
                    'fontColor',
                    'highlight',
                    'alignment'
                ],
            })
            .then(editor => {
                console.log('Editor was initialized', editor);
                editorInstanceDescription = editor;
            })
            .catch(error => {
                console.error('There was a problem initializing the editor.', error);
            });
    </script> --}}


    {{-- <script>
        const {
            ClassicEditor,
            Essentials,
            Paragraph,
            Bold,
            Italic,
            Font
        } = CKEDITOR;
        // Create a free account and get <YOUR_LICENSE_KEY>
        // https://portal.ckeditor.com/checkout?plan=free
        ClassicEditor
            .create(document.querySelector('#editor'), {
                // licenseKey: '<YOUR_LICENSE_KEY>',
                plugins: [Essentials, Paragraph, Bold, Italic, Font],
                toolbar: [
                    'undo', 'redo', '|', 'bold', 'italic', '|',
                    'fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor'
                ]
            })
            .then(editor => {
                window.editor = editor;
            })
            .catch(error => {
                console.error(error);
            });
    </script> --}}


    {{-- <script>
        var rowIdx = $('.inputRow').length

        // Add more FAQs
        $('#addRow').on('click', function() {
            $('#inputFormRow').append(`
                <div class="inputRow">
                    <div class="content-between">
                        <h1>FAQ #${rowIdx + 1}</h1>
                        <button type="button" class="btn btn-danger removeRow">Remove</button>
                    </div>
                    <div class="form_field_padding">
                        <label>Title</label>
                        <input class="form-control" type="text" name="items[${rowIdx}][name]" placeholder="Enter item name"/>
                    </div>
                    <div class="form_field_padding">
                        <label>Description</label>
                        <textarea class="form-control editor" name="items[${rowIdx}][description]" placeholder="Enter description"></textarea>
                    </div>
                </div>`);
            initializeCKEditor()
            rowIdx++;
        });
        initializeCKEditor()

        function initializeCKEditor() {
            $('.editor').each(function() {
                const textareaId = $(this).attr('name'); // Use the 'name' attribute for dynamic initialization

                // Only initialize CKEditor if it is not already initialized
                // if (!CKEDITOR.instances[textareaId]) {
                var url = "{{ route('faq_image_upload', ['_token' => csrf_token()]) }}";
                CKEDITOR.replace(this, {
                    allowedContent: true,
                    extraAllowedContent: 'p h1 h2 h3 h4 h5 h6 strong em; a[!href]; ul ol li; img[!src,alt,width,height]',
                    disallowedContent: 'script; *[on*]',
                    removePlugins: 'paste,sourcearea,scayt,templates,about,forms,table,tabletools,tableselection,iframe,div,language',
                    removeButtons: 'ExportPdf,NewPage,Save',
                    filebrowserUploadUrl: url, // Your image upload URL
                    filebrowserUploadMethod: 'form', // Upload method
                    filebrowserUploadParams: {
                        type: 'image' // Ensures only image types are allowed in file dialog
                    }

                });
                // }
            });
        }
        // Remove an FAQ row and reindex the remaining rows
        $(document).on('click', '.removeRow', function() {
            var textareaName = $(this).closest('.inputRow').find('textarea').attr('name');

            // Check if CKEditor instance exists for the textarea and destroy it
            if (CKEDITOR.instances[textareaName]) {
                CKEDITOR.instances[textareaName].destroy(true); // Destroy the CKEditor instance
            }

            // Remove the row from the DOM
            // $(this).closest('.inputRow').remove();

            $(this).closest('.inputRow').remove();
            reindexRows();
        });

        // Function to reindex FAQs
        function reindexRows() {
            $('.inputRow').each(function(index) {
                // Update the FAQ header number
                $(this).find('h1').text('FAQ #' + (index + 1));

                // Update the name attributes for inputs and textareas
                $(this).find('input, textarea').each(function() {
                    var name = $(this).attr('name');
                    name = name.replace(/items\[\d+\]/, 'items[' + index + ']');
                    $(this).attr('name', name);
                });
            });

            // Update rowIdx to reflect the current count of rows for next addition
            initializeCKEditor()
            rowIdx = $('.inputRow').length;
        }
    </script> --}}
@endpush
