@extends('layouts.master')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="white-box">
                    <h3 class="box-title pull-left">{{ translate('content_management_system.edit_listing_type') }} : {{$listingType->name}}</h3>
                    {{-- @can('view-'.str_slug('Category')) --}}
                    <a  class="btn btn_yellow pull-right" href="{{route('listing-type.index',['category_id' => $category->ids])}}"> {{ translate('content_management_system.back') }}</a>
                    {{-- @endcan --}}

                    <div class="clearfix"></div>
                    <hr>
                    @if ($errors->any())
                        <ul class="alert alert-danger">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    @endif

                    <form method="POST" action="{{ route('listing-type.update', ['category_id' => $category->ids, 'listing_type' => $listingType->id]) }}" accept-charset="UTF-8"
                          class="form-horizontal" enctype="multipart/form-data">
                          {{ method_field('PATCH') }}
                          {{ csrf_field() }}
                        @include ('listingType.form', ['submitButtonText'=> translate('content_management_system.update')])
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
