<section class="Residence-modal">
    <div class="modal fade" id="tour_filter_category" tabindex="-1" aria-labelledby="filterCategoryLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="filterCategoryLabel">{{ translate('advance_filters_modal.filters') }}</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="bg_white">
                        <div class="price-list">
                            <div class="inner-title">
                                <h6 class="title">{{ translate('advance_filters_modal.price_range') }}</h6>
                            </div>
                            <ul class="icheck-list">
                                <li>
                                    <label for="flat-radio-1">{{ translate('advance_filters_modal.per_child') }}</label>
                                    <input type="radio" class="check" id="flat-radio-1" name="price_range_type"
                                        value="child" data-radio="iradio_flat-yellow" checked>
                                </li>
                                <li>
                                    <label for="flat-radio-2">{{ translate('advance_filters_modal.per_adult') }}</label>
                                    <input type="radio" class="check" id="flat-radio-2" name="price_range_type"
                                        value="adult" data-radio="iradio_flat-yellow">
                                </li>
                            </ul>
                        </div>
                        <input type="text" class="js-range-slider" name="price_range" value=""
                            data-skin="round" data-type="double" data-min="1" data-max="1000000" data-grid="false" />
                    </div>
                    {{-- <div class="bg_white">
                        <h6 class="title">length of activity</h6>
                        <div class="custom-radio">
                            <input type="radio" class="length_activity_radio_btn" id="tour_activity_length_any"
                                name="tour_activity_length" value="any" checked>
                            <label for="tour_activity_length_any">any</label>
                            <input type="radio" class="length_activity_radio_btn" id="one-hour"
                                name="tour_activity_length" value="1">
                            <label for="one-hour">1+ hour</label>
                            <input type="radio" class="length_activity_radio_btn" id="two-hour"
                                name="tour_activity_length" value="2">
                            <label for="two-hour">2+ hour</label>
                            <input type="radio" class="length_activity_radio_btn" id="three-hour"
                                name="tour_activity_length" value="3">
                            <label for="three-hour">3+ hour</label>
                            <input type="radio" class="length_activity_radio_btn" id="four-hour"
                                name="tour_activity_length" value="4">
                            <label for="four-hour">4+ hour</label>
                            <input type="radio" class="length_activity_radio_btn" id="five-hour"
                                name="tour_activity_length" value="5">
                            <label for="five-hour">5+ hour</label>
                            <input type="radio" class="length_activity_radio_btn" id="six-hour"
                                name="tour_activity_length" value="6">
                            <label for="six-hour">6+ hour</label>
                            <input type="radio" class="length_activity_radio_btn" id="seven-hour"
                                name="tour_activity_length" value="7">
                            <label for="seven-hour">7+ hour</label>
                            <input type="radio" class="length_activity_radio_btn" id="eight-hour"
                                name="tour_activity_length" value="8">
                            <label for="eight-hour">8+ hour</label>
                        </div>
                    </div> --}}
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.duration') }}</h6>
                        <div class="custom-radio duration">
                            <input type="checkbox" class="rating_tour_radio_btn" id="tour_same" name="tour_day_type[]"
                                value="same_day">
                            <label for="tour_same" class="ms-0">{{ translate('advance_filters_modal.same_day') }}</label>
                            <input type="checkbox" class="rating_tour_radio_btn" id="tour_multi" name="tour_day_type[]"
                                value="multiple_days">
                            <label for="tour_multi">{{ translate('advance_filters_modal.multi_day') }}</label>
                        </div>
                    </div>
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.activity_type') }}</h6>
                        <select class="js-example-basic-multiple" name="listing_types[]" multiple="multiple"
                            data-placeholder="{{ translate('advance_filters_modal.select_activity') }}">
                            {{-- Replace this with tour activity in future BE --}}
                            @foreach ($categories->where('id', 1)->first()->listing_types as $type)
                                <option value="{{ $type->id }}">{{ $type->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.experience_type') }}</h6>
                        <div class="custom-radio">
                            <input type="radio" class="rating_tour_radio_btn" id="private" name="private_booking"
                                value="yes">
                            <label for="private" class="ms-0">{{ translate('advance_filters_modal.private') }}</label>
                            <input type="radio" class="rating_tour_radio_btn" id="grouped" name="private_booking"
                                value="no">
                            <label for="grouped">{{ translate('advance_filters_modal.grouped') }}</label>
                        </div>
                    </div>
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.language_offered') }}</h6>
                        <select class="js-example-basic-multiple" name="languages[]" multiple="multiple"
                            data-placeholder="{{ translate('advance_filters_modal.select_language') }}">
                            @foreach ($categories->where('id', 1)->first()->tour_languages as $tour_language)
                                <option value="{{ $tour_language->id }}">{{ $tour_language->country_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.kid_friendly') }}</h6>
                        <div class="custom-radio">
                            <input type="radio" class="rating_tour_radio_btn" id="yes" name="child_allow"
                                value="yes">
                            <label for="yes" class="ms-0">{{ translate('advance_filters_modal.yes') }}</label>
                            <input type="radio" class="rating_tour_radio_btn" id="no" name="child_allow"
                                value="no">
                            <label for="no">{{ translate('advance_filters_modal.no') }}</label>
                        </div>
                    </div>
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.accessible_features') }}</h6>
                        <select class="js-example-basic-multiple" name="amenities[]" multiple="multiple"
                            data-placeholder="{{ translate('advance_filters_modal.select_features') }}">
                            {{-- <option value="" disabled>Type Search Field</option> --}}
                            @foreach ($categories->where('id', 1)->first()->amenities as $amenity)
                                <option value="{{ $amenity->id }}">{{ $amenity->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.ratings') }}</h6>
                        <div class="custom-radio">
                            <input type="radio" class="rating_tour_radio_btn" id="tour-one-plus" name="ratings"
                                value="1">
                            <label for="tour-one-plus" class="ms-0">1</label>
                            <input type="radio" class="rating_tour_radio_btn" id="tour-two-plus" name="ratings"
                                value="2">
                            <label for="tour-two-plus">2</label>
                            <input type="radio" class="rating_tour_radio_btn" id="tour-three-plus" name="ratings"
                                value="3">
                            <label for="tour-three-plus">3</label>
                            <input type="radio" class="rating_tour_radio_btn" id="tour-four-plus" name="ratings"
                                value="4">
                            <label for="tour-four-plus">4</label>
                            <input type="radio" class="rating_tour_radio_btn" id="tour-five-plus" name="ratings"
                            value="5">
                        <label for="tour-five-plus">5</label>
                        </div>
                    </div>
                    <div class="bg_white day_time">
                        <h6 class="title">{{ translate('advance_filters_modal.time_of_day') }}</h6>
                        <div class="custom-radio">
                            <input type="radio" class="rating_tour_radio_btn" id="tour_time[0]" name="tour_time"
                                value="Morning">
                            <label for="tour_time[0]" class="ms-0">{{ translate('advance_filters_modal.morning') }}</label>
                            <input type="radio" class="rating_tour_radio_btn" id="tour_time[1]" name="tour_time"
                                value="Afternoon" checked>
                            <label for="tour_time[1]">{{ translate('advance_filters_modal.afternoon') }}</label>
                            <input type="radio" class="rating_tour_radio_btn" id="tour_time[2]" name="tour_time"
                                value="Evening">
                            <label for="tour_time[2]">{{ translate('advance_filters_modal.evening') }}</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn yellow-btn adv_apply">{{ translate('advance_filters_modal.apply') }}</button>
                    <button type="button" class="btn white-btn adv_reset">{{ translate('advance_filters_modal.reset') }}</button>
                </div>
            </div>
        </div>
    </div>
</section>
