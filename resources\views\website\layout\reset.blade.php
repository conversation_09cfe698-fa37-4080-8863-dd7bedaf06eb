<div class="modal fade login verify" data-bs-backdrop="static" id="forgot" tabindex="-1"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content p-4">
            <form id="forget_password_form">
                @csrf
                <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">Forgot Password </h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div class="form-outline mb-4">
                            <input type="email" class="form-control" id="forget_pass_email" name="email"
                                placeholder="Email Address" />
                            <div id="forget_pass_message" class="text-start text-danger"></div>
                        </div>
                    </div>
                    <button type="button" name="next" class="action-button btn button login btn-block mb-4"
                        id="fg_btn1">Send</button>
                </fieldset>
                <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">Please Check Your Email</h4>
                    </div>
                    <div class="modal-body text-center">
                        <p class="fs-14">We’ve send you the verification code on <span
                                class="fw-bold user-email-span"><EMAIL></span></p>
                                <div class="d-flex">
                                    <input type="text" class="form-control" id="forget_password_otp"
                                        placeholder="Enter Verification Code here">
                                    <button type="button" class="px-2 verify_email_btn"
                                        id="verify_otp_btn_password"> Verify </button>
                                </div>
                    </div>
                    <input type="button" name="next" style="display: none" id="otp-submit-btn"
                        class="next action-button btn button login btn-block mb-4" value="Next" />
                    <button type="button" class="transparent button mx-auto w-100 b-none" id="resend-otp-btn">Resend
                        code</button>
                </fieldset>
                <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">Reset Password </h4>
                    </div>
                    <div class="modal-body text-center pt-0">
                        <p class="fs-14">Please type something you’ll remember</p>

                        <div class="error_message_wrapper" style="opacity: 0; height: 0; overflow: hidden; display: none;">
                            <span></span>
                        </div>

                        <div class="form-outline mb-4">
                            <input type="password" class="form-control" id="new_password_inp" name="setPassword" placeholder="Password" />
                            <i class="fas fa-eye eye_icon" id="pass_btn1"></i>
                        </div>
                        <div class="form-outline mb-4">
                            <input type="password" class="form-control" id="confirm_password_inp" name="setConfirmPassword"
                                placeholder="Confirm Password" />
                            <i class="fas fa-eye eye_icon" id="pass_btn2"></i>
                        </div>
                    </div>
                    <input type="button" id="reset_password_btn" name="next" class=" action-button btn button login btn-block mb-4"
                        value="Send" />
                </fieldset>
                <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">Success!</h4>
                    </div>
                    <div class="modal-body text-center">
                        <p class="fs-14">Your password has been changed.</p>
                        <button type="button" class="btn btn-warning" data-bs-dismiss="modal">Close</button>
                    </div>
                </fieldset>
            </form>
        </div>
    </div>
</div>

@push('js')
    <script>
        $(document).ready(function() {
            let email;
            $("#fg_btn1").on("click", function() {
                email = $("#forget_pass_email").val();
                let forget_pass_btn = $(this).html(
                    `<div class="spinner-border" role="status"> <span class="visually-hidden">Loading...</span></div>`
                ).prop('disabled', true);
                $.ajax({
                    url: '{{ route('forget_password') }}',
                    type: "POST",
                    data: {
                        email
                    },
                    success: function(response) {
                        if (response.status == true) {
                            $("#forget_pass_message").html("");
                            $("#fg_btn1").addClass("next");
                            $(".user-email-span").html(email);
                            setTimeout(() => {
                                forget_pass_btn.html("<b>Next</b>").removeAttr("id");
                                current_fs = forget_pass_btn.parent().hide();
                                next_fs = forget_pass_btn.parent().next();
                                //show the next fieldset
                                next_fs.show();
                                //hide the current fieldset with style
                                current_fs.animate({
                                    opacity: 0
                                }, {
                                    step: function(now) {
                                        // for making fielset appear animation
                                        opacity = 1 - now;
                                        current_fs.css({
                                            'display': 'none',
                                            'position': 'relative'
                                        });
                                        next_fs.css({
                                            'opacity': opacity
                                        });
                                    },
                                    duration: 500
                                });
                            }, 50);
                        } else {
                            $("#forget_pass_message").html(response.message);
                        }
                    },
                    complete: function() {
                        forget_pass_btn.html("Send").prop('disabled', false);
                    }
                })
            })
        })
        $(document).on("click", "#verify_otp_btn_password", function() {
            forget_password_submit();
            })
        function forget_password_submit() {
            email = $("#forget_pass_email").val();
            console.log("Submitting...");
            let otp = $("#forget_password_otp").val();
            $.ajax({
                url: "{{ route('check_otp') }}",
                type: "POST",
                data: {
                    email,
                    otp,
                    type: "password"
                },
                success: function(response) {
                    if (response.status == true) {
                        $("#otp-submit-btn").show();
                        $("#otp-submit-btn").click();
                        $.toast({
                            heading: 'Success',
                            text: response.message,
                            showHideTransition: 'plain',
                            icon: 'success'
                        })
                    } else {
                        $.toast({
                            heading: 'Error',
                            text: response.message,
                            showHideTransition: 'plain',
                            icon: 'error'
                        })
                    }
                }
            });
            // otp form end

        }
        // resend otp
        $(document).on("click", "#resend-otp-btn", function() {
            email = $("#forget_pass_email").val();
            let resend_otp_btn = $(this).html(
                `<div class="spinner-border" role="status"> <span class="visually-hidden">Loading...</span></div>`
            ).prop('disabled', true);
            $.ajax({
                url: "{{ route('resend_otp') }}",
                type: "POST",
                data: {
                    email,
                    type: "password",
                },
                success: function(response) {
                    console.log(response);
                    if (response.status == true) {
                        resend_otp_btn.html("Resend code")
                        $.toast({
                            heading: 'Success',
                            text: response.message,
                            showHideTransition: 'plain',
                            icon: 'success'
                        })
                    }
                },
                complete: function() {
                    resend_otp_btn.prop('disabled', false);
                }
            });
        });

        // reset password
        $("#reset_password_btn").on("click", function(){
            let reset_password_btn = $(this).html(
                `<div class="spinner-border" role="status"> <span class="visually-hidden">Loading...</span></div>`
            ).prop('disabled', true);
            let new_password = $("#new_password_inp").val();
            let confirm_password = $("#confirm_password_inp").val();
            let otp = $("#forget_password_otp").val();
            email = $("#forget_pass_email").val();

            $.ajax({
                url: "{{ route("reset_password") }}",
                type: "POST",
                data: {
                    otp,
                    new_password,
                    confirm_password,
                    email
                },
                success: function(response){
                    console.log("testings");
                    if(response.status == true){
                        $.toast({
                            heading: 'Success',
                            text: response.message,
                            showHideTransition: 'plain',
                            icon: 'success'
                        });
                        
                        const $wrapper = $(".error_message_wrapper");
                        $wrapper.animate({ height: 0, opacity: 0 }, 300, function() {
                            $wrapper.hide();
                        });

                        window.location.reload();
                    }else{
                        // $.toast({
                        //     heading: 'Error',
                        //     text: response.message,
                        //     showHideTransition: 'plain',
                        //     icon: 'error'
                        // })

                        // Function to animate the error message

                        function showError(message) {
                            const $wrapper = $(".error_message_wrapper");
                            const $span = $wrapper.find("span");

                            let finalMessage = '';

                            if (typeof message === 'string') {
                                finalMessage = message;
                            } else if (typeof message === 'object') {
                                // Assume message is an object with arrays of errors
                                $.each(message, function(key, value) {
                                    if (Array.isArray(value)) {
                                        finalMessage += `<li>${value.join('<br>')}</li>`;
                                    } else {
                                        finalMessage += `<li>${value}</li>`;
                                    }
                                    $('input[name="' + key + '"]').addClass('is-invalid');
                                });
                                finalMessage = `<ul>${finalMessage}</ul>`;
                            }

                            $span.html(finalMessage);
                            $wrapper.css({ height: "auto", opacity: 1 });
                            const targetHeight = $wrapper.outerHeight();
                            $wrapper.css({ height: 0, opacity: 0 }).show();
                            $wrapper.animate({ height: targetHeight, opacity: 1 }, 400, function() {
                                $wrapper.css("height", "auto");
                            });
                        }


                        // Function ends here

                        showError(response.message);

                    }
                },
                complete: function() {
                    reset_password_btn.prop('disabled', false);

                }
            })
        })
    </script>
    <script>
        $('body').on('click', "#pass_btn1", function() {
            var passwordInput = $("#new_password_inp");

            console.log('heyy');
            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#pass_btn1').addClass('fa-eye-slash')
                $('#pass_btn1').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#pass_btn1').removeClass('fa-eye-slash')
                $('#pass_btn1').addClass('fa-eye')
            }
        });
        $('body').on('click', "#pass_btn2", function() {
            var passwordInput = $("#confirm_password_inp");

            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#pass_btn2').addClass('fa-eye-slash')
                $('#pass_btn2').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#pass_btn2').removeClass('fa-eye-slash')
                $('#pass_btn2').addClass('fa-eye')
            }
        });
    </script>
@endpush
