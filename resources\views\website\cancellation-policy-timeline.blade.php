<ul class="cancelation_policy_timeline_wrapper">
    @if(isset($policyType))
        @if($policyType == 'Flexible')
            @if($refundPercentage == 100)
                <li class="timeline_step flexible_full_refund">
                    <span class="refund_date">{{ translate('listing_details.today')   }}</span>
                </li>
                <li class="timeline_step">
                    <span class="refund_date">{{$dates['flexible_cutoff']}}</span>
                </li>
                <li class="timeline_step">
                    <span class="refund_date">{{ translate('listing_details.check_in')   }}</span>
                </li>
            @else
                <li class="timeline_step">
                    <span class="refund_date">{{ translate('listing_details.today')   }}</span>
                </li>
                <li class="timeline_step">
                    <span class="refund_date">{{ translate('listing_details.check_in')   }}</span>
                </li>
            @endif
        @endif

        @if($policyType == 'Moderate' || $policyType == 'Strict')
            @if($refundPercentage == 100)
                <li class="timeline_step">
                    <span class="refund_date">{{ translate('listing_details.today')   }}</span>
                </li>
                <li class="timeline_step">
                    <span class="refund_date">{{$dates['moderate_cutoff_full'] ?? $dates['strict_cutoff_full'] ?? ""}}</span>
                </li>
                <li class="timeline_step">
                    <span class="refund_date">{{$dates['moderate_cutoff_half'] ?? $dates['strict_cutoff_half'] ?? ""}}</span>
                </li>
                <li class="timeline_step">
                    <span class="refund_date">{{ translate('listing_details.check_in')   }}</span>
                </li>
            @elseif($refundPercentage == 50)
                <li class="timeline_step">
                    <span class="refund_date">{{ translate('listing_details.today')   }}</span>
                </li>
                <li class="timeline_step">
                    <span class="refund_date">{{$dates['moderate_cutoff_half'] ?? $dates['strict_cutoff_half'] ?? ""}}</span>
                </li>
                <li class="timeline_step">
                    <span class="refund_date">{{ translate('listing_details.check_in')   }}</span>
                </li>
            @else
                <li class="timeline_step">
                    <span class="refund_date">{{ translate('listing_details.today')   }}</span>
                </li>
                <li class="timeline_step">
                    <span class="refund_date">{{ translate('listing_details.check_in')   }}</span>
                </li>
            @endif
        @endif
    @endif
</ul>
<div class="timeline_detailed_wrapper">
    @if(isset($policyType))
        @if($policyType == 'Flexible')
            @if($refundPercentage == 100)
                <div class="timeline_detail_single">
                    <div class="timeline_date">
                        <div class="date_tag">
                            <span>{{ translate('listing_details.before') }}</span>
                            <h5>{{$dates['flexible_cutoff']}}</h5>
                        </div>
                    </div>
                    <div class="description">
                        <h5>{{ translate('listing_details.full_refund')   }}</h5>
                        <p>{{ translate('listing_details.cancel_before_time_full_refund') }} {{$dates['flexible_cutoff']}} {{ translate('listing_details.full_refund_time_note')   }}</p>
                    </div>
                </div>
                <div class="timeline_detail_single">
                    <div class="timeline_date">
                        <div class="date_tag">
                            <span>{{ translate('listing_details.after')}}</span>
                            <h5>{{$dates['flexible_cutoff']}}</h5>
                        </div>
                    </div>
                    <div class="description">
                        <h5>{{ translate('listing_details.no_refund') }}</h5>
                        <p>{{ translate('listing_details.no_refund_after_that')}}</p>
                    </div>
                </div>
            @else
                <div class="timeline_detail_single">
                    <div class="timeline_date">
                        <div class="date_tag">
                            <span>{{ translate('listing_details.until')   }}</span>
                            {{-- <h5>{{$dates['flexible_cutoff']}}</h5> --}}
                            <h5>{{$startDate->format('j M')}}</h5>
                        </div>
                    </div>
                    <div class="description">
                        <h5>{{ translate('listing_details.no_refund') }}</h5>
                        <p>{{ translate('listing_details.no_refund_on_change_or_cancel')   }}</p>
                    </div>
                </div>
            @endif
        @endif

        @if($policyType == 'Moderate' || $policyType == 'Strict')
            @if($refundPercentage == 100)
                <div class="timeline_detail_single">
                    <div class="timeline_date">
                        <div class="date_tag">
                            <span>{{ translate('listing_details.before')   }}</span>
                            <h5>{{$dates['moderate_cutoff_full'] ?? $dates['strict_cutoff_full'] ?? ""}}</h5>
                        </div>
                    </div>
                    <div class="description">
                        <h5>{{ translate('listing_details.full_refund') }}</h5>
                        <p>{{ translate('listing_details.cancel_before') }} {{$dates['moderate_cutoff_full'] ?? $dates['strict_cutoff_full'] ?? ""}} {{ translate('listing_details.full_refund_time_notice')}}</p>
                    </div>
                </div>
                <div class="timeline_detail_single">
                    <div class="timeline_date">
                        <div class="date_tag">
                            <span>{{ translate('listing_details.before')  }}</span>
                            <h5>{{$dates['moderate_cutoff_half'] ?? $dates['strict_cutoff_half'] ?? ""}}</h5>
                        </div>
                    </div>
                    <div class="description">
                        <h5>{{ translate('listing_details.partial_refund') }}</h5>
                        <p>{{ translate('listing_details.cancel_before_partial_refund') }}{{$dates['moderate_cutoff_half'] ?? $dates['strict_cutoff_half'] ?? ""}} {{ translate('listing_details.partial_refund_time_notice')  
 }}</p>
                    </div>
                </div>
                <div class="timeline_detail_single">
                    <div class="timeline_date">
                        <div class="date_tag">
                            <span>{{ translate('listing_details.after')  }}</span>
                            <h5>{{$dates['moderate_cutoff_half'] ?? $dates['strict_cutoff_half'] ?? ""}}</h5>
                        </div>
                    </div>
                    <div class="description">
                        <h5>{{ translate('listing_details.no_refund')   }}</h5>
                        <p>{{ translate('listing_details.no_refund_after_that')   }}</p>
                    </div>
                </div>
            @elseif($refundPercentage == 50)
                <div class="timeline_detail_single">
                    <div class="timeline_date">
                        <div class="date_tag">
                            <span>{{ translate('listing_details.before')   }}</span>
                            <h5>{{$dates['moderate_cutoff_half'] ?? $dates['strict_cutoff_half'] ?? ""}}</h5>
                        </div>
                    </div>
                    <div class="description">
                        <h5>{{ translate('listing_details.partial_refund')   }}</h5>
                        <p>{{ translate('listing_details.cancel_before_partial_refund')   }} {{$dates['moderate_cutoff_half'] ?? $dates['strict_cutoff_half'] ?? ""}} {{ translate('listing_details.partial_refund_time_notice') }}</p>
                    </div>
                </div>
                <div class="timeline_detail_single">
                    <div class="timeline_date">
                        <div class="date_tag">
                            <span>{{ translate('listing_details.after')   }}</span>
                            <h5>{{$dates['moderate_cutoff_half'] ?? $dates['strict_cutoff_half'] ?? ""}}</h5>
                        </div>
                    </div>
                    <div class="description">
                        <h5>{{ translate('listing_details.no_refund')  }}</h5>
                        <p>{{ translate('listing_details.no_refund_after_that')   }}</p>
                    </div>
                </div>
            @else
                <div class="timeline_detail_single">
                    <div class="timeline_date">
                        <div class="date_tag">
                            <span>{{ translate('listing_details.until')   }}</span>
                            {{-- <h5>{{$dates['moderate_cutoff_half'] ?? $dates['strict_cutoff_half'] ?? ""}}</h5> --}}
                            <h5>{{$startDate->format('j M')}}</h5>
                        </div>
                    </div>
                    <div class="description">
                        <h5>{{ translate('listing_details.no_refund')   }}</h5>
                        <p>{{ translate('listing_details.no_refund_on_change_or_cancel')   }}</p>
                    </div>
                </div>
            @endif
        @endif
    @endif
</div>