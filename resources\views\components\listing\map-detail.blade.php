@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "address-detail");
@endphp
<div class="col-md-12">
    <div class="inner_section_main_col">
        <div class="main_step_title">
            <h2>{{ $step_data->title ?? "" }}</h2>
        </div>
        @isset($step_data->sub_title)
            <div class="step_description">
                <p>{{ $step_data->sub_title ?? "" }}</p>
            </div>
        @endisset
        <div class="fields_wrapper scrollable-section">
            <div class="txt_field">
                <label for="">{{ translate('stepper.country_region') }} *</label>
                <input type="text" placeholder="{{ translate('stepper.enter_country') }}" class="form-control step_input" name="country"
                    id="country" value="{{ $listing->address->country ?? '' }}" required />
            </div>
            <div class="txt_field">
                <label for="">{{ translate('stepper.street_address') }} *</label>
                <input type="text" placeholder="{{ translate('stepper.enter_street') }}" class="form-control step_input" name="street"
                    id="street" value="{{ $listing->address->street ?? '' }}" required />
            </div>
            <div class="txt_field">
                <label for="">{{ translate('stepper.apt_suite_unit_optional') }}</label>
                <input type="text" placeholder="{{ translate('stepper.enter_suit') }}" class="form-control step_input no_validate"
                    name="suit" id="suit" value="{{ $listing->address->suit ?? '' }}" />
            </div>
            <div class="txt_field">
                <label for="">{{ translate('stepper.city_town') }} *</label>
                <input type="text" placeholder="{{ translate('stepper.enter_city') }}" class="form-control step_input" name="city"
                    id="city" value="{{ $listing->address->city ?? '' }}" required />
            </div>
            <div class="txt_field">
                <label for="">{{ translate('stepper.state_territory') }} *</label>
                <input type="text" placeholder="{{ translate('stepper.enter_state') }}" class="form-control step_input" name="state"
                    id="state" value="{{ $listing->address->state ?? '' }}" required />
            </div>
            <div class="txt_field">
                <label for="">{{ translate('stepper.zip_code') }}*</label>
                <input type="number" placeholder="{{ translate('stepper.enter_zip_code') }}" class="form-control step_input" name="zip_code"
                    value="{{ $listing->address->zip_code ?? '' }}" min="0" id="zip"
                    pattern="/^-?\d+\.?\d*$/" onKeyPress="if(this.value.length==6) return false;" required />
            </div>
        </div>
    </div>
</div>


@push('js')
    <script>
        $('#address').on('keyup', function() {
            RNT();
        });
        $('.next').on('click', function() {
            RNT();
        });

        function RNT() {
            var selected_country = $('#country').val().trim();
            if (selected_country == 'Colombia') {
                $('.rnt').show(100);
                $('.rnt').find('input').removeClass('no_validate').prop('disabled', false);
            } else {
                $('.rnt').hide(100);
                $('.rnt').find('input').addClass('no_validate').prop('disabled', true);
            }
        }


        $('.listing_stepper .basic_accommodation_step .space_detail_quantity_wrapper input[type="number"]').on("input blur",
            function() {
                if ($(this).val() === "") {
                    $(this).val($(this).attr("min") || 1);
                }
            });
    </script>
@endpush
