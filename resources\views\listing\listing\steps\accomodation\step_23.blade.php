@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "cut-off-time");
@endphp

<fieldset class="checkIn_checkOut_step" id="">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    <div class="checkin_checkout_fields_wrapper">
                        <div class="row">
                            <div class="col-md-7 col_left">
                                <div class="inner_section_left_col">
                                    <div class="checkin_title">
                                        <label for="">{{ translate('stepper.check_in_after') }}</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-5 col_right">
                                <div class="inner_section_right_col">
                                    <div class="time_field_wrapper">
                                        <input type="time" name="check_in_time" class="form-control start_time"
                                            value="{{ $listing->detail->check_in_time ?? '' }}" placeholder="{{ translate('stepper.enter_check_in_time') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-7 col_left">
                                <div class="inner_section_left_col">
                                    <div class="checkin_title">
                                        <label for="">{{ translate('stepper.check_out_before') }}</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-5 col_right">
                                <div class="inner_section_right_col">
                                    <div class="time_field_wrapper">
                                        <input type="time" name="check_out_time" class="form-control end_time"
                                            value="{{ $listing->detail->check_out_time ?? '' }}" placeholder="{{ translate('stepper.enter_check_out_time') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-7 col_left">
                                <div class="inner_section_left_col">
                                    <div class="checkin_title">
                                        <label for="">{{ translate('stepper.same_day_booking_close') }}</label>
                                        {{-- <em class="d-block">Select the time after which same-day reservations will no
                                            longer be accepted.</em> --}}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-5 col_right">
                                <div class="inner_section_right_col">
                                    <div class="time_field_wrapper">
                                        <input type="time" name="booking_close_time" class="form-control end_time"
                                            value="{{ $listing->detail->booking_close_time ?? '' }}" placeholder="{{ translate('stepper.enter_cutoff_time') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1 " value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>
