@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <div class="container-fluid report_sec">
        @can('add-' . str_slug('Report'))
            <a class="btn btn_yellow mb-3" href="{{ url('/report/report/create') }}"> {{ __('add') }}
                {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Report') }}</a>
        @endcan
        <!-- .row -->
        <div class="row">
            <div class="col-md-12">
                <div class="head d-flex justify-content-between">
                    <h2 class="box-title pull-left">
                        {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Reports') }}
                    </h2>
                    <div class="nav_search main">
                        <!-- Actual search box -->
                        <div class="form-group has-feedback has-search">
                            <form class="example" action="/action_page.php" style="width: 100%;">
                                <button type="button"><i class="fa fa-search"></i></button>
                                <input type="text" placeholder="Search.." id="searchBar" name="search2" />
                                {{-- <i class="fa-solid fa-filter" style="color: #000000;"></i> --}}
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    {{-- <div class="clearfix"></div>
                    <hr> --}}
                    <div class="table-responsive">
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    {{-- <th>#</th> --}}
                                    <th>{{ translate('dashboard_report.reported_by') }}</th>
                                    <th>{{ translate('dashboard_report.subject') }}</th>
                                    <th>{{ translate('dashboard_report.date') }}</th>
                                    <th>{{ translate('dashboard_report.status') }}</th>
                                    <th>{{ translate('dashboard_report.description') }}</th>
                                    <th>{{ translate('dashboard_report.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($report as $item)
                                    <tr>
                                        {{-- <td>{{ $loop->iteration ?? $item->id }}</td> --}}
                                        <td>{{ $item->user->name ?? '' }}</td>
                                        <td>{{ $item->subject }}</td>
                                        <td>{{ $item->created_at->format('Y/m/d') }}</td>
                                        <td>
                                            @if ($item->status == 0)
                                                <span class="text-primary">{{ translate('dashboard_report.pending') }}</span>
                                            @else
                                                <span class="text-success">{{ translate('dashboard_report.approved') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <p class="limit">
                                                {{ $item->description }}
                                            </p>
                                        </td>

                                        <td class="form_btn ">
                                            <div class="dropdown">
                                                <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                </button>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    @can('view-' . str_slug('Report'))
                                                        <a href="" data-toggle="modal" data-target="#report"
                                                            onclick="openModal({{ $item->id }})"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Report') }}">
                                                            <button class="btn  dropdown-item">
                                                                {{ translate('dashboard_report.view') }}
                                                            </button>
                                                        </a>
                                                        {{-- <a href="{{ url('/report/report/' . $item->id) }}"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Report') }}">
                                                            <button class="btn  dropdown-item">
                                                                View
                                                            </button>
                                                        </a> --}}
                                                    @endcan
                                                    {{-- edit --}}
                                                    {{-- @can('edit-' . str_slug('Report'))
                                                        <a href="{{ url('/report/report/' . $item->id . '/edit') }}"
                                                            title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Report') }}">
                                                            <button class="btn  dropdown-item">
                                                                {{ __('reply') }}
                                                            </button>
                                                        </a>
                                                    @endcan --}}
                                                    {{-- delete --}}
                                                    @can('delete-' . str_slug('Report'))
                                                        <form method="POST" id="delete-form-{{ $item->id }}"
                                                            action="{{ url('/report/report' . '/' . $item->id) }}"
                                                            accept-charset="UTF-8" style="display:inline">
                                                            {{ method_field('DELETE') }}
                                                            {{ csrf_field() }}
                                                            <button type="button" class="btn dropdown-item delete-btn" data-id="{{ $item->id }}"
                                                                title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Report') }}"
                                                                >
                                                                {{ translate('dashboard_report.delete') }}
                                                            </button>
                                                        </form>
                                                    @endcan
                                                    @if ($item->status == 0)
                                                        <a href="{{ route('report.resolved', $item->id) }}"
                                                            class="dropdown-item delete-contact">Resolved</a>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <div class="pagination-wrapper"> {!! $report->appends(['search' => Request::get('search')])->render() !!} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Modal --}}
    <section class="report reject">
        <div class="modal fade" id="report" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
            <div class="modal-dialog" id="myModal" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="title">{{ __('report_detail') }}</h1>
                        <div class="form_field_padding">
                            <div class="mb-3 mod_cust_text">
                                <p id="userName">{{ __('user_name') }}:</p>
                                <p id="subject">{{ __('subject') }}:</p>
                                <p id="date">{{ __('date') }}:</p>
                                <p id="status">{{ __('status') }}:</p>
                                <p id="description">{{ __('description') }}:</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <script>
        function openModal(id) {
            $.ajax({
                url: '{{ url('getData') }}' + '/' + id,
                type: 'GET',
                success: function(response) {
                    $('#userName').html('<span>User Name:</span> ' + response.user.name);
                    $('#subject').html('<span>Subject:</span> ' + response.subject);
                    $('#date').html('<span>Date:</span> ' + moment(response.created_at).format('YY/MM/DD'));
                    $('#status').html('<span>Status:</span> ' + (response.status == 1 ? 'Resolved' :
                        'Pending'));

                    $('#description').html('<span>Description:</span> ' + response.description);
                },
                error: function(xhr) {
                    console.log(xhr.responseText);
                }
            });
        }
    </script>
@endsection
@push('js')
    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>

    <script>
        $(function() {
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }]
            });

        });
    </script>
@endpush
