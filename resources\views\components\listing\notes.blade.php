@push('css')
    <style>
        .key_features_step .key_features_fields_wrapper .ck-editor__editable_inline {
            min-height: calc(1.5em * 5);
        }

        .key_features_step .key_features_fields_wrapper .ck .ck-placeholder:before,
        .key_features_step .key_features_fields_wrapper .ck.ck-placeholder:before {
            color: #A9AEC0;
            font-weight: 400
        }
    </style>
@endpush

@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "notes");
@endphp

<div class="col-md-12">
    <div class="inner_section_main_col">
        <div class="main_step_title">
            <h2>{{$step_data->title ?? ""}}</h2>
            @isset($step_data->sub_title)
                <p class="sub_title">{{$step_data->sub_title ?? ""}}</p>
            @endisset
        </div>
        <div class="key_features_fields_wrapper scrollable-section">
            <div class="features_add_btn_wrapper">
                <label for="add_note" class="add_note">{{ucfirst($label ?? "")}}</label>
                <a class="add_feature_btn cust_add" href="javascript:void(0)">{{ translate('stepper.add') }}</a>
            </div>
            <div class="txt_field">
                <input
                    class="form-control key_features_title repeater1  {{ count($listing->itineraries ?? []) == 0 ? '' : 'no_validate' }} no_validate"
                    type="text" id="add_note"
                    {{-- placeholder="{{ ($listing->category_id ?? '') === 2 
                    ? 'E.g., All safety equipment, including life jackets, must be returned in good condition.' 
                    : 'E.g., There’s a grocery store just a 5-minute walk from the property.' }}"> --}}
                    placeholder="{{ $placeholder }}">
            </div>
            <div class="saved_features_list_wrapper">
                @forelse ($listing->notes ?? [] as $note)
                    <div class="single_saved_feature">
                        <div class="content_cross_wrapper">
                            <div class="title_description_wrapper w-100">
                                <div class="note_count">
                                    <span>{{ translate('stepper.note') }} # {{ $loop->iteration }}</span>
                                </div>
                                <div class="feature_title">
                                    <h6>{{ $note->name }}</h6>
                                    <input type="hidden" id="notes_{{ $loop->index }}" name="notes[]"
                                        class="add_input no_validate" value="{{ $note->name }}" />
                                </div>
                            </div>
                            <div class="cross_icon_wrapper">
                                <a class="remove_key_feature_btn" href="javascript:void(0)">
                                    <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                @endforelse
            </div>
        </div>
    </div>
</div>
@push('js')
    {{-- <script src="https://cdn.ckeditor.com/ckeditor5/37.1.0/classic/ckeditor.js"></script> --}}
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    {{-- <script src="https://unpkg.com/@ckeditor/ckeditor5-inspector@4.1.0/build/inspector.js"></script> --}}

    <script>
        $(document).ready(function() {


            function validateNotesField() {
                let noteInput = $('.important_notes_step .key_features_fields_wrapper .key_features_title');
                if(noteInput.val().length >= 1){
                    $('.important_notes_step .next').prop("disabled", true);
                }else{
                    $('.important_notes_step .next').prop("disabled", false);
                }
            }

            $(document).on('input blur', '.important_notes_step .key_features_fields_wrapper .key_features_title', function() {
                validateNotesField();
            });


            var counter = $('.important_notes_step .saved_features_list_wrapper .single_saved_feature').length;

            // function notesValidation() {
            //     var notesNumber = $(
            //         '.important_notes_step .saved_features_list_wrapper .single_saved_feature').length;
            //     if (notesNumber >= 1) {
            //         $('.listing_stepper .important_notes_step .next.action-button').prop('disabled',
            //             false);
            //     } else {
            //         $('.listing_stepper .important_notes_step .next.action-button').prop('disabled',
            //             true);
            //     }
            // }

            // $(document).on('focusout','.important_notes_step .key_features_fields_wrapper .key_features_title',function(){
            //     notesValidation();
            // });

            // notesValidation();

            $(document).on('click', '.important_notes_step .features_add_btn_wrapper .add_feature_btn', function() {
                var title = $(this).closest('fieldset').find('.key_features_title').val().trim();
                // var description = $(this).closest('fieldset').find('.key_features_description').val().trim();
                // var description = editorInstance.getData();
                // var plainText = $('<div>').html(description).text();

                if (title !== "") {
                    var featureHTML = `
                    <div class="single_saved_feature">
                        <div class="content_cross_wrapper">
                            <div class="title_description_wrapper w-100">
                                <div class="note_count">
                                    <span>{{ translate('stepper.note') }} # ${counter + 1}</span>
                                </div>
                                <div class="feature_title">
                                    <h6>${title}</h6>
                                    <input type="hidden" id="notes_${counter}" name="notes[]" class="add_input no_validate" value="${title}" />
                                </div>
                            </div>
                            <div class="cross_icon_wrapper">
                                <a class="remove_key_feature_btn" href="javascript:void(0)">
                                    <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                </a>
                            </div>
                        </div>
                    </div>`;
                    $('.important_notes_step .saved_features_list_wrapper').prepend(featureHTML);
                    $(this).closest('fieldset').find('.key_features_title').val('');
                    counter++;
                    // notesValidation();
                    validateNotesField();
                } else {
                    Swal.fire({
                        title: @json(translate('stepper.error')),
                        text: @json(translate('stepper.fill_fields_first')),
                        icon: "error"
                    });
                }
            });
            $(document).on('click', '.important_notes_step .remove_key_feature_btn', function() {
                $(this).closest('.single_saved_feature').remove();
                reindexName();
                // notesValidation();
                validateNotesField();

                if ($('input[name="itineraries[0][title]"]').length > 0) {
                    console.log('Input exists.');
                } else {
                    $('.itinerary_parent .txt_field input').removeClass('no_validate');
                    $('.itinerary_parent .txt_field textarea').removeClass('no_validate');
                }
            });

            function reindexName() {
                // $('important_notes_step .saved_features_list_wrapper .single_saved_feature').each(function(index) {
                //     $(this).find('input').each(function() {
                //         var name = $(this).attr('name');
                //         var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                //         $(this).attr('name', newName);

                //         var id = $(this).attr('id');
                //         var baseId = id.substring(0, id.lastIndexOf('_') + 1);
                //         var newId = baseId + index;
                //         $(this).attr('id', newId);
                //     });
                // });
                counter = $('.important_notes_step .saved_features_list_wrapper .single_saved_feature').length;
            }
        });
    </script>
@endpush
