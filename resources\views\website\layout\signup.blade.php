<div class="modal fade login signup" id="signUp" data-bs-backdrop="static" tabindex="-1"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content p-4">
            <form  method="POST" id="signUp-form">
                @csrf


                <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">{{ translate('home.sign_up_to') }} <span class="color luxu">LuxuStars</span></h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <p class="fs-14 bold red">{{ translate('home.id_match_warning') }}</p>

                        <div id="sign-up-error" style="display: none" class="alert alert-danger text-start "
                            role="alert">
                            <ul>
                            </ul>
                        </div>
                        <div class="form-outline mb-4 d-inline-block">
                            <input type="name" class="form-control" name="first_name" placeholder="{{ translate('home.first_name') }}"
                                required />
                        </div>
                        <div class="form-outline mb-4 d-inline-block">
                            <input type="name" class="form-control" name="last_name" placeholder="{{ translate('home.last_name') }}"
                                required />
                        </div>
                        <div class="form-outline mb-4">
                            <input type="email" class="form-control" id="user_email" name="email"
                                placeholder="{{ translate('home.email_address') }}"  required />
                        </div>
                        <div class="form-outline mb-4">
                            <input type="tel" class="form-control" id="telephone" maxlength="12" name="phone"
                                placeholder="{{ translate('home.phone') }}" pattern="[0-9]{3} [0-9]{3} [0-9]{4}"
                                required />
                            <input type="hidden" class="country_code" name="country_code" value="+1">
                        </div>
                        <div class="form-outline mb-4">
                            <input type="password" id="createPassword" class="form-control" name="password"
                                placeholder="{{ translate('home.password') }}" required />
                            <i class="fas fa-eye eye_icon" id="pass_btn1"></i>
                        </div>
                        <div class="form-outline mb-4">
                            <input type="password" id="confirmPassword" class="form-control"
                                name="password_confirmation" placeholder="{{ translate('home.confirm_password') }}" required />
                            <i class="fas fa-eye eye_icon" id="pass_btn2"></i>
                        </div>
                        <div class="form-check term-condition">
                            <input class="form-check-input" type="checkbox" value="" id="terms_condition"
                                required>
                            <label class="form-check-label" for="terms_condition">
                                {{ translate('home.by_selecting') }} <span class="bold">{{ translate('home.sign_up') }}</span>, {{ translate('home.i_agree_to') }} LuxuStars <a
                                    href="{{ route("terms") }}">{{ translate('home.terms_conditions') }}</a>, {{ translate('home.and_acknowledge') }}
                                <a href="{{ route("privacy_policy") }}">{{ translate('home.privacy_policy') }}</a>.
                            </label>
                        </div>
                        <!-- Error message placeholder, initially hidden -->
                        <div id="terms-error" style="display: none; color: red; margin-bottom:10px;">
                            {{ translate('home.accept_terms_error') }}
                        </div>
                    </div>

                    <button type="button" class="btn button login btn-block mb-4" id="signup-btn">{{ translate('home.sign_up') }}</button>

                    <!-- Register buttons -->
                    <div class="text-center">
                        <p class="fs-14">{{ translate('home.already_a_member') }} <button type="button" class="not_btn blue"
                                data-bs-toggle="modal" data-bs-dismiss="modal" data-bs-target="#login">
                                {{ translate('home.sign_in') }}
                            </button>
                        </p>
                    </div>
                </fieldset>



                <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">{{ translate('home.check_your_email') }}</h4>
                    </div>
                    <div class="modal-body text-center">
                        <p class="fs-14">{{ translate('home.sent_verification_code') }} <span
                                class="fw-bold user-email-span"></span></p>
                        <div class="d-flex">
                            <input type="text" class="form-control" id="otp_inpt"
                                placeholder="{{ translate('home.enter_verification_code') }}">
                        </div>
                    </div>
                    <input type="button" name="next" id="verify_otp_btn"
                        class="action-button btn button login btn-block mb-4 disable-on-click" value="{{ translate('home.verify') }}" />
                    <button type="button" class="transparent button mx-auto w-100 b-none"
                        id="resend-email-otp-btn">{{ translate('home.resend_code') }}</button>
                </fieldset>

                {{-- <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">{{ translate('home.hoorah') }}</h4>
                    </div>
                    <div class="modal-body text-center">
                        <p class="fs-14">{{ translate('home.email_verified_success') }}</p>
                        <button type="button" class="btn btn-warning" onclick="location.reload()"
                            data-bs-dismiss="modal">{{ translate('home.close') }}</button>
                    </div>
                </fieldset> --}}
            </form>
        </div>
    </div>
</div>
</div>
@push('js')
    <script src="{{ asset('website') }}/js/jquery.toast.js"></script>
    <script>
        $("#signup-btn").on("click", function(e) {
            e.preventDefault();
            // Hide the error message when you try again
            $('#terms-error').hide();

            // Check if the terms and conditions checkbox is checked
            if (!$('#terms_condition').is(':checked')) {
                // If not checked, display the error message
                $('#terms-error').show();
                return; // Stop the function here
            }
            let sign_btn = $(this).html(
                `<div class="spinner-border" role="status"> <span class="visually-hidden">{{ translate('home.loading') }}</span></div>`
            ).prop('disabled', true);
            let signup_data = $("#signUp-form").serialize();
            let user_email = $("#user_email").val()
            $.ajax({
                url: "{{ route('sign_up') }}",
                data: signup_data,
                type: "POST",
                success: function(response) {
                    if (response.status == true) {
                        $("#sign-up-error").slideUp();
                        $("#signup-btn").addClass("next");
                        $(".user-email-span").html(user_email);
                        setTimeout(() => {
                            sign_btn.html("<b>{{ translate('home.next') }}</b>").removeAttr("id");
                            current_fs = sign_btn.parent().hide();
                            next_fs = sign_btn.parent().next();
                            //show the next fieldset
                            next_fs.show();
                            //hide the current fieldset with style
                            current_fs.animate({
                                opacity: 0
                            }, {
                                step: function(now) {
                                    // for making fielset appear animation
                                    opacity = 1 - now;
                                    current_fs.css({
                                        'display': 'none',
                                        'position': 'relative'
                                    });
                                    next_fs.css({
                                        'opacity': opacity
                                    });
                                },
                                duration: 500
                            });
                        }, 50);
                    } else {
                    
                        $('input').removeClass('is-invalid');
                        $("#sign-up-error").slideDown();
                        $("#sign-up-error ul").empty();

                        $.each(response.message, function(key, messages) {
                            // Loop through each message for this field
                            $.each(messages, function(_, message) {
                                $("#sign-up-error ul").append(`<li>${message}</li>`);
                            });
                            // Mark the input as invalid
                            $('input[name="' + key + '"]').addClass('is-invalid');
                        });

                        setTimeout(() => {
                            $("#sign-up-error").slideUp();
                        }, 10000);
                    }
                },
                complete: function() {
                    sign_btn.html("Sign Up").prop('disabled', false);
                }
            })


            function email_verify_submit(email) {
                let otp = $("#otp_inpt").val();
                $.ajax({
                    url: "{{ route('signUpEmailVerification') }}",
                    type: "POST",
                    data: {
                        email,
                        otp,
                        type: "email"
                    },
                    success: function(response) {
                        if (response.status == true) {
                            $("#otp-submit-btn-email").show();
                            // $.toast({
                            //     heading: 'Success',
                            //     text: response.message,
                            //     showHideTransition: 'plain',
                            //     icon: 'success'
                            // })
                            Swal.fire({
                                title: "Hoorah!",
                                text: "Your email has been successfully verfied.",
                                icon: "success"
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    window.location.reload();
                                }
                            });
                        } else {
                            $.toast({
                                heading: 'Error',
                                text: response.message,
                                showHideTransition: 'plain',
                                icon: 'error'
                            })
                        }
                    },
                    complete: function(response) {
                        $(".code_otp input").prop("disabled", false);
                    }
                });
            }

            $(document).on("click", "#verify_otp_btn", function() {
                let email = $("#user_email").val();
                if(email != ""){
                    email_verify_submit(email);
                }
            })
            // resend otp
            $(document).on("click", "#resend-email-otp-btn", function() {
                email = $("#user_email").val();
                let resend_otp_btn = $(this).html(
                    `<div class="spinner-border" role="status"> <span class="visually-hidden">Loading...</span></div>`
                ).prop('disabled', true);
                if (email) {
                    $.ajax({
                        url: "{{ route('resend_otp') }}",
                        type: "POST",
                        data: {
                            email,
                            type: "email",
                        },
                        success: function(response) {
                            resend_otp_btn.html("Resend code")
                            if (response.status == true) {
                                $.toast({
                                    heading: 'Success',
                                    text: response.message,
                                    showHideTransition: 'plain',
                                    icon: 'success'
                                })
                            } else {
                                $.toast({
                                    heading: 'Error',
                                    text: response.message,
                                    showHideTransition: 'plain',
                                    icon: 'error'
                                })
                            }
                        },
                        complete: function() {
                            setTimeout(() => {
                                resend_otp_btn.prop('disabled', false);
                            }, 1000*60);
                        }
                    });
                }
            });
            // resend otp end
        });
    </script>
    <script>
        $(document).ready(function() {
            $('#telephone').each(function() {
                const $input = $(this);
                
                $input.on('input', function() {
                    this.value = this.value.replace(/[^0-9\s\-]/g, '');
                });
                
                $input.on('keypress', function(event) {
                    if (event.which === 8 || event.which === 9 || event.which === 27 || event.which === 13 || 
                        event.which === 46 || event.which === 45 || event.which === 40 || event.which === 41 || 
                        event.which === 43 || event.which === 32) {
                        return true;
                    }
                    if (event.which >= 48 && event.which <= 57) {
                        return true;
                    }
                    event.preventDefault();
                });
                $input.removeAttr('min');
            });
        });
    </script>
    <script>
        $("#pass_btn1").click(function() {
            var passwordInput = $("#createPassword");

            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#pass_btn1').addClass('fa-eye-slash')
                $('#pass_btn1').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#pass_btn1').removeClass('fa-eye-slash')
                $('#pass_btn1').addClass('fa-eye')
            }
        });
        $("#pass_btn2").click(function() {
            var passwordInput = $("#confirmPassword");

            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#pass_btn2').addClass('fa-eye-slash')
                $('#pass_btn2').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#pass_btn2').removeClass('fa-eye-slash')
                $('#pass_btn2').addClass('fa-eye')
            }
        });
    </script>
@endpush
