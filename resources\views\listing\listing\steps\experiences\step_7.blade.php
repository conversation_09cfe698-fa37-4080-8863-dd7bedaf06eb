@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "schedule-duration");
@endphp
<fieldset
    class="basic_accommodation_step basic_watercraft_step accommodations_rules_step watercraft_schedule_duration_step experience_schedule_duration_step">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    @isset($step_data->sub_title)
                        <div class="content">
                            <p>{{ $step_data->sub_title ?? "" }}</p>
                        </div>
                    @endisset
                    <div class="allow_pets_field_wrapper">
                        <div class="allow_pets_input_wrapper">
                            <div class="allow_pets_input">
                                <label for="single_day">{{ translate('stepper.same_day') }}</label>
                                <input type="radio" class="radio_btn" id="single_day" value="same_day"
                                    {{ ($listing->detail->tour_day_type ?? '') == 'same_day' || !isset($listing) ? 'checked' : '' }}
                                    name="tour_day_type" />
                            </div>
                            <div class="allow_pets_input">
                                <label for="multiple_days">{{ translate('stepper.multiple_days') }}</label>
                                <input type="radio" class="radio_btn" id="multiple_days" value="multiple_days"
                                    name="tour_day_type"
                                    {{ ($listing->detail->tour_day_type ?? '') == 'multiple_days' ? 'checked' : '' }} />
                            </div>
                        </div>
                    </div>
                    <div class="space_details_wrapper fields_wrapper scrollable-section">

                        {{-- same day --}}
                        <div
                            class="space_detail_single d-block custom_single w-100 {{ ($listing->detail->tour_day_type ?? '') == 'same_day' || !isset($listing) ? 'd-block' : 'd-none' }} ">
                            <div class="row">
                                <div class="col-md-5 d-flex align-items-center">
                                    <div class="space_detail_title">
                                        <label for="start-time">{{ translate('stepper.start_end_time') }}</label>
                                    </div>
                                </div>
                                <div class="col-md-7 d-flex align-item-center">
                                    <div
                                        class="custom_time_wrapper d-flex gap-2 align-items-center justify-content-between w-100">
                                        <div class="duration_wrapper_input ">
                                            <input type="time" class="form-control start_time" id="start-time"
                                                name="start_time" placeholder="{{ translate('stepper.enter_start_time') }}"
                                                value="{{ $listing->detail->start_time ?? '' }}" />
                                        </div>
                                        <span class="grey">{{ translate('stepper.to') }}</span>
                                        <div class="duration_wrapper_input">
                                            <input type="time" class="form-control end_time" id="end-time"
                                                name="end_time" placeholder="{{ translate('stepper.enter_end_time') }}"
                                                value="{{ $listing->detail->end_time ?? '' }}" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{-- same day end --}}

                        {{-- multiple day --}}
                        <div
                            class="space_detail_single custom_multiple flex-column {{ ($listing->detail->tour_day_type ?? '') == 'multiple_days' ? 'd-block' : 'd-none' }}">
                            <div class="space_detail_single d-block select_days_wrapper w-100">
                                <div class="row">
                                    <div class="col-md-5 align-items-center d-flex">
                                        <div class="space_detail_title">
                                            <label class="px-0" for="">{{ translate('stepper.how_many_days') }}</label>
                                        </div>
                                    </div>
                                    <div class="col-md-7 align-items-center d-flex">
                                        <div class="space_detail_quantity_wrapper w-100">
                                            <select class="form-control no_validate" id="days" name="days">
                                                @for ($i = 2; $i <= 7; $i++)
                                                    <option value="{{ $i }}"
                                                        {{ count($listing->tour_durations ?? []) == $i ? 'selected' : '' }}>
                                                        {{ $i }}</option>
                                                @endfor
                                                {{-- <option value="2">2</option>
                                                <option value="3">3</option>
                                                <option value="4">4</option>
                                                <option value="5">5</option>
                                                <option value="6">6</option>
                                                <option value="7">7</option> --}}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="all_days w-100">
                                @forelse ($listing->tour_durations ?? [] as $tour_duration)
                                    <div class="custom_days_wrapper w-100">
                                        <div class="row">
                                            <div class="col-md-5 align-items-center d-flex">
                                                <div class="space_detail_title">
                                                    <label class="day-label" for="start-time[1]">{{ translate('stepper.day') }}
                                                        {{ $loop->iteration }} {{ translate('stepper.start_end_time') }}
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-7 align-items-center d-flex">
                                                <div
                                                    class="custom_time_wrapper d-flex align-items-center justify-content-between gap-2 w-100">
                                                    <div class="duration_wrapper_input">
                                                        <input type="time"
                                                            class="form-control start_time no_validate"
                                                            value="{{ $tour_duration->start_time ?? '' }}"
                                                            id="start-time[{{ $loop->index }}]"
                                                            placeholder="{{ translate('stepper.enter_start_time') }}"
                                                            name="tour_durations[{{ $loop->index }}][start_time]" />
                                                    </div>
                                                    <span class="grey">{{ translate('stepper.to') }}</span>
                                                    <div class="duration_wrapper_input ">
                                                        <input type="time" class="form-control end_time no_validate"
                                                            value="{{ $tour_duration->end_time ?? '' }}"
                                                            id="end-time[{{ $loop->index }}]"
                                                            placeholder="{{ translate('stepper.enter_end_time') }}"
                                                            name="tour_durations[{{ $loop->index }}][end_time]" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <div class="custom_days_wrapper w-100">
                                        <div class="row">
                                            <div class="col-md-5 align-items-center d-flex">
                                                <div class="space_detail_title">
                                                    <label class="day-label" for="start-time[1]">{{ translate('stepper.day_1_time') }}
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-7 align-items-center d-flex">
                                                <div
                                                    class="custom_time_wrapper d-flex align-items-center justify-content-between gap-2 w-100">
                                                    <div class="duration_wrapper_input">
                                                        <input type="time"
                                                            class="form-control start_time no_validate"
                                                            id="start-time[0]" name="tour_durations[0][start_time]"
                                                            placeholder="{{ translate('stepper.enter_start_time') }}" />
                                                    </div>
                                                    <span class="grey">{{ translate('stepper.to') }}</span>
                                                    <div class="duration_wrapper_input ">
                                                        <input type="time" class="form-control end_time no_validate"
                                                            id="end-time[0]" name="tour_durations[0][end_time]"
                                                            placeholder="{{ translate('stepper.enter_end_time') }}" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="custom_days_wrapper w-100">
                                        <div class="row">
                                            <div class="col-md-5 align-items-center d-flex">
                                                <div class="space_detail_title">
                                                    <label class="day-label" for="start-time[2]">{{ translate('stepper.day_2_time') }}
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-7 align-items-center d-flex">
                                                <div
                                                    class="custom_time_wrapper d-flex align-items-center justify-content-between gap-2 w-100">
                                                    <div class="duration_wrapper_input">
                                                        <input type="time"
                                                            class="form-control start_time no_validate"
                                                            id="start-time[1]" name="tour_durations[1][start_time]"
                                                            placeholder="{{ translate('stepper.enter_start_time') }}" />
                                                    </div>
                                                    <span class="grey">{{ translate('stepper.to') }}</span>
                                                    <div class="duration_wrapper_input ">
                                                        <input type="time"
                                                            class="form-control end_time no_validate" id="end-time[1]"
                                                            name="tour_durations[1][end_time]"
                                                            placeholder="{{ translate('stepper.enter_end_time') }}" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforelse
                            </div>
                        </div>
                        {{-- multiple day end --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>
@push('js')
    <script>
        $(document).ready(function() {

            function toggleTourDayType() {
                var tour_day_type = $('.experience_schedule_duration_step .allow_pets_input_wrapper input:checked').val();
                var single = $('.custom_single');
                var multiple = $('.custom_multiple');
                
                if (tour_day_type == 'multiple_days') {
                    setTimeout(() => {
                        multiple.removeClass('d-none');
                        multiple.find('input').removeClass('no_validate');
                        single.addClass('d-none');
                        single.find('input').addClass('no_validate');
                    }, 200);
                } else {
                    setTimeout(() => {
                        multiple.addClass('d-none');
                        multiple.find('input').addClass('no_validate');
                        single.removeClass('d-none');
                        single.find('input').removeClass('no_validate');
                    }, 200);
                }
            }

            toggleTourDayType();

            $('.experience_schedule_duration_step .allow_pets_input_wrapper input').on('change', toggleTourDayType);


            $('#days').attr('min', '2');
            checkDay();
            function checkDay(){
                var tour_day_type = $('.watercraft_schedule_duration_step .allow_pets_input_wrapper input').val();
                var single = $('.custom_single');
                var multiple = $('.custom_multiple');
                
                if (tour_day_type == 'multiple_days') {
                    multiple.find('input').removeClass('no_validate').val('');
                    single.find('input').addClass('no_validate');
                } else {  
                    multiple.find('input').addClass('no_validate');
                    single.find('input').removeClass('no_validate').val('');
                }
            }
        });
        // Handle manual input changes
        $('#days').on('input', function() {
            let value = $(this).val().trim();
            // Allow empty field temporarily while typing
            if (value === "") {
                return;
            }
            // Convert value to number and enforce min/max range
            let numValue = parseInt(value);
            if (isNaN(numValue) || numValue < 2) {
                $(this).val(2);
            } else if (numValue > 7) {
                $(this).val(7);
            }
            updateScheduleDays();
        });

        function updateScheduleDays() {
            var multiDay = $('#days').val();
            var se_time_html = $('.all_days .custom_days_wrapper').html();
            var dayCounter = 0;
            $('.all_days').html('');
            for (let i = 1; i <= multiDay; i++) {
                $('.all_days').append(
                    '<div class="custom_days_wrapper w-100">' +
                    se_time_html + '</div>');
            }
            // setTimeout(() => {
            $('.all_days .custom_days_wrapper').each(function(dayCounter) {
                dayCounter++;
                $(this).find('.duration_wrapper_input input.start_time')
                    .attr('name', `tour_durations[${dayCounter - 1}][start_time]`)
                    .attr('type', "time")
                    .attr('placeholder', @json(translate('stepper.enter_start_time')))
                    .attr('id', "start-time[" + dayCounter + "]");
                $(this).find('.duration_wrapper_input input.end_time')
                    .attr('name', `tour_durations[${dayCounter - 1}][end_time]`)
                    .attr('type', "time")
                    .attr('placeholder', @json(translate('stepper.enter_end_time')))
                    .attr('id', "end-time[" + dayCounter + "]");
                $(this).find('.space_detail_title .day-label')
                    .text(@json(translate('stepper.day')) + ' ' + dayCounter + ' ' + @json(translate('stepper.start_end_time')));
            });
            // }, 500);
            initializeTimePicker();
        }

        function initializeTimePicker() {
            $("input.start_time, input.end_time").each(function() {
                let defaultTime = $(this).attr("value") ? $(this).attr("value").trim() : ""; // Ensure valid time
                $(this).flatpickr({
                    enableTime: true,
                    noCalendar: true,
                    dateFormat: "h:i K",
                    time_24hr: false,
                    allowInput: true,
                    defaultDate: defaultTime,
                    onReady: function(selectedDates, dateStr, instance) {
                        setTimeout(() => {
                            let hourInput = instance.hourElement;
                            let minuteInput = instance.minuteElement;
                            $(hourInput).on("input", function() {
                                if (this.value.length >= 2) {
                                    $(minuteInput).focus();
                                }
                            });
                            $(minuteInput).on("input", function() {
                                if (this.value.length >= 2) {
                                    let hour = hourInput.value.padStart(2, "0");
                                    let minute = minuteInput.value.padStart(2, "0");
                                    let meridian = instance.amPM ? instance.amPM
                                        .textContent : "AM";
                                    let formattedTime = `${hour}:${minute} ${meridian}`;
                                    instance.setDate(formattedTime, true);
                                }
                            });
                        }, 100);
                    },
                    onChange: function() {
                        validateDurationTime();
                    }
                });
            });
        }

        function validateDurationTime() {
            $(".custom_days_wrapper, .custom_single").each(function() {
                let startTimeInput = $(this).find("input.start_time").val();
                let endTimeInput = $(this).find("input.end_time").val();
                if (!startTimeInput || !endTimeInput) return;
                let startTime = flatpickr.parseDate(startTimeInput, "h:i K");
                let endTime = flatpickr.parseDate(endTimeInput, "h:i K");
                if (endTime <= startTime) {
                    Swal.fire({
                        icon: "error",
                        title: @json(translate('stepper.invalid_time')),
                        text: @json(translate('stepper.end_time_after_start'))
                    });
                    $(this).find("input.end_time").val(""); // Clear only the end time
                }
            });
        }
        $(document).ready(function() {
            initializeTimePicker();

            showItineraryDays();
            getTourTypeDays();


            $(".experience_schedule_duration_step .space_details_wrapper .space_detail_quantity_wrapper select").on(
                "mousedown",
                function() {
                    $(this).parent().toggleClass("open");
                }).on("blur", function() {
                $(this).parent().removeClass("open");
            });

        });


        function showItineraryDays() {
            var tourDayType = $("input[name='tour_day_type']:checked").val();
            if (tourDayType == 'multiple_days') {
                $('.itinerary_multiple_days_dropdown').show();
                window.itinerary_multiple = true;
            } else {
                $('.itinerary_multiple_days_dropdown').hide();
                window.itinerary_multiple = false;
            }
        }

        $(document).on('change', 'input[name="tour_day_type"]', function() {
            showItineraryDays();
            $('.saved_features_list_wrapper').html('');
        });


        $(document).on('change', '#days', function() {
            getTourTypeDays();
        });

        function getTourTypeDays() {
            var selectedValue = $('#days').find("option:selected").val();
            $('.itinerary_multiple_days_dropdown #itinerary_days').html('');
            for (i = 1; i <= selectedValue; i++) {
                $('.itinerary_multiple_days_dropdown #itinerary_days').append(`<option value="${i}">${i}</option>`)
            }
        }
    </script>
@endpush
