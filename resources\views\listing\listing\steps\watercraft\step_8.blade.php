@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "amenities");
@endphp

<fieldset class="place_amenities_step" id="">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    <div class="step_description">
                        @isset($step_data->sub_title)
                            <p>{{ $step_data->sub_title }}</p>
                        @endisset
                    </div>

                    <div class="search_field_wrapper_amenities">
                        <select class="form-control select2 multi_select" multiple="multiple" name="amenities[]"
                            id="amenities_select">
                            @php($selected_amenity_ids = $listing?->amenity_detail->pluck('id')->toArray() ?? [])
                            @foreach ($category->amenities ?? [] as $amenity)
                                @foreach ($amenity->options as $option)
                                    <option value="{{ $option->id }}"
                                        data-desc="{{ $option->translations[0]->description }}"
                                        data-icon="{{ asset('website' . '/' . ($option->image ?? 'images/default-icon.png')) }}"
                                        @if (in_array($option->id, $selected_amenity_ids)) selected @endif>
                                        {{ $option->name }}
                                    </option>
                                @endforeach
                            @endforeach
                        </select>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 col-sm-12 col-md-12 col-lg-8">
                            <div class="inner_section_col_left">
                                <div class="amenities_wrapper scrollable-section">
                                    {{-- Data from ajax to ber inserted here --}}
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-12 col-md-12 col-lg-4">
                            <div class="inner_section_col_right">

                                <div class="top_heading text-start">
                                    <h4>{{ translate('stepper.selected_amenities') }}</h4>
                                </div>
                                <div class="selected_amenities_wrapper scrollable-section">
                                    @forelse ($listing->amenity_detail ?? [] as $amenity)
                                        @if ($amenity->amenity_id)
                                            <div class="single_amenity" data-id="{{ $amenity->amenity_id ?? '' }}">
                                                <div class="icon_name_wrapper"> <img
                                                        src="{{ asset('website/' . $amenity->image) }}
                                                        "onerror="this.onerror=null;this.src=`{{ asset('website/images/snowflake.png') }}`;">
                                                    <div class="ps-1 text-start content_parent">
                                                        <label class="p-0">{{ $amenity->name }}</label>
                                                        <p class="fs-14 text-start">{{ $amenity->description ?? '' }}
                                                        </p>
                                                    </div>
                                                    {{-- <input type="text" name="custom_amenities[]" value="{{ $amenity->name }}" id="custom_amenities_{{ $loop->index }}"> --}}
                                                </div> <a href="javascript:void(0)" class="remove-item"><i
                                                        class="far fa-times-circle"></i></a>
                                            </div>
                                        @endif
                                    @empty
                                    @endforelse
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1 " value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {

            function toggleNextButton() {
                // alert('Alert2');
                if ($('.selected_amenities_wrapper').children().length === 0) {
                    $('.place_amenities_step .next').prop('disabled', true);
                } else {
                    $('.place_amenities_step .next').prop('disabled', false);
                }
            }

            // toggleNextButton();

            function setPlaceholder() {
                // Find the search input within the Select2 container and set the placeholder
                var select2SearchField = $('.select2-container .select2-search__field');

                // Only update if it's not already set
                if (select2SearchField.length && !select2SearchField.prop('placeholder')) {
                    select2SearchField.prop('placeholder', @json(translate('stepper.search_amenities')));
                }
            }

            $('.select2').on('select2:open select2:close select2:select select2:unselect select2:clear',
                function() {
                    // $('input.select2-search__field').prop('placeholder', 'Search for amenities');
                    setPlaceholder();
                });

            setPlaceholder();

            $(document).on('click', '.amenity-modal .check-box .input_parent input[type="checkbox"]', function() {
                var amenityID = $(this).val();
                var isChecked = $(this).prop('checked'); // Check if the checkbox is checked

                console.log('amenityID: ' + amenityID + ' | Checked: ' + isChecked);

                if (isChecked) {
                    var amenity_options = $("#amenities_select").val() ||
                []; // Get the current value or an empty array if none
                    var amenity_option_icon = $(this).closest('.form-check-custom').find('.amenityIcon')
                        .attr('src');
                    var amenity_option_name = $(this).closest('.form-check-custom').find('label span')
                        .html();
                    var amenity_option_desc = $(this).closest('.form-check-custom').find('p.desc').html();
                    amenity_options.push(amenityID); // Add the new amenityID


                    if ($(`.selected_amenities_wrapper .single_amenity[data-id="${amenityID}"]`).length ==
                        0) {
                        // Add the selected amenity dynamically (optional part)
                        $('.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper')
                            .append(
                                `<div class="single_amenity" data-id="${amenityID}">
                                <div class="icon_name_wrapper">
                                    <img src="${amenity_option_icon}" />
                                    <div class="text-start content_parent">
                                        <label class="p-0">${amenity_option_name}</label>
                                        <p class="fs-14 text-start">${amenity_option_desc}</p>
                                        </div>
                                    </div> 
                                    <a href="javascript:void(0)" class="remove-item"><i class="far fa-times-circle"></i></a>
                            </div>`
                            );
                        toggleNextButton();
                    }
                    // Add the single_amenity (you can append it if needed or just ensure it's added)
                    // For example, you can dynamically append the corresponding amenity:
                    // $('.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper').append('<div class="single_amenity" data-id="' + amenityID + '">Amenity ' + amenityID + '</div>');
                } else {
                    // Remove the respective .single_amenity
                    $(`.single_amenity[data-id="${amenityID}"]`).remove();
                    toggleNextButton();
                }
            });


            // $(document).on('click','.amenity-modal .check-box .input_parent input[type="checkbox"]',function(){
            //     var amenityID = $(this).val();                
            //     console.log('amenityID: ' + amenityID);
            //     // $('.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity').each(function(){
            //     //     var dataID = $(this).data('id');
            //     //     console.log('data-id: ' + dataID);
            //     //     if(dataID == amenityID){
            //             $(`.single_amenity[data-id="${amenityID}"]`).remove();
            //     //     }
            //     // });
            // });

            function get_amenity_options(selected_val) {
                let amenity_options = selected_val;
                $.ajax({
                    url: "{{ route('get_amenity_option') }}",
                    type: "GET",
                    data: {
                        amenity_options,
                        category: "{{ $category->id }}"
                    },
                    success: function(response) {
                        console.log(response);
                        $(".amenities_wrapper").empty();
                        $(".amenities_wrapper").append(response.data);
                    }
                })
            }
            let amenity_options = @js($selected_amenity_ids ?? []);
            get_amenity_options(amenity_options)
            $(".amenity-option-select").on("change", function() {
                amenity_options = $(this).val();
                get_amenity_options($(this).val());
            })
            // Handle checkbox clicks
            $(document).on("click", ".amenity-checkbox", function() {
                let amenity_id = parseInt($(this).val());
                console.log(amenity_id);

                if (amenity_options.includes(amenity_id)) {
                    amenity_options = amenity_options.filter(id => id !== amenity_id);
                } else {
                    amenity_options.push(amenity_id);
                }
            });

            $(document).on('click', ".amenity-modal-close", function() {
                $("#amenities_select").val(amenity_options).trigger("change");
                get_amenity_options(amenity_options)
            });


            // $(document).on('change','.amenity-modal .check-box .input_parent input[type="checkbox"]',function(){
            //     // var amenityID = $(this).val();
            //     // $('.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity').each(function(){
            //     //     if($(this).data('id') == amenityID){
            //     //         $(this).remove();
            //     //     }
            //     // });
            //     $("#amenities_select").val(amenity_options).trigger("change");
            //     get_amenity_options(amenity_options)
            // });


            $(".select2").select2({
                closeOnSelect: false,
                placeholder: @json(translate('stepper.search_amenities')),
                allowClear: false,
                tags: false
            });

            $(document).on('select2:select', '.select2', function(e) {
                const selectedData = e.params.data;
                console.log("selectedData", selectedData);
                // console.log("selectedDataValue", e.target.value);

                if ($(`.selected_amenities_wrapper .single_amenity[data-id="${selectedData.id}"]`)
                    .length === 0) {
                    $('.selected_amenities_wrapper').append(
                        `<div class="single_amenity" data-id="${selectedData.id}">
                            <div class="icon_name_wrapper">
                                <img src="${selectedData.element.dataset.icon ?? '{{ asset('website') }}/images/snowflake.png'}" />
                                <div class="text-start content_parent">
                                    <label class="p-0">${selectedData.text}</label>
                                    <p class="fs-14  text-start">${selectedData.element.dataset.desc ?? ''}</p>
                                    </div>
                                 </div> 
                                 <a href="javascript:void(0)" class="remove-item"><i class="far fa-times-circle"></i></a>
                        </div>`
                    );
                }

                if (!$(`#box1-amenity-${selectedData.id}`).is(":checked")) {
                    $(`#box1-amenity-${selectedData.id}`).click();
                    const selectedValues = Array.isArray(e.target.value) ? e.target.value : [e.target
                        .value
                    ];
                    get_amenity_options(selectedValues);
                }

                $("#amenities_select").val(amenity_options).trigger("change");
                get_amenity_options(amenity_options);
                toggleNextButton();

            });

            $(document).on('select2:unselect', '.select2', function(e) {
                const deselectedData = e.params.data;
                $(`.selected_amenities_wrapper .single_amenity[data-id="${deselectedData.id}"]`).remove();

                if ($(`#box1-amenity-${deselectedData.id}`).is(":checked")) {
                    $(`#box1-amenity-${deselectedData.id}`).click();
                    const deselectedValues = Array.isArray(e.target.value) ? e.target.value : [e.target
                        .value
                    ];
                    get_amenity_options(deselectedValues);
                }

                $("#amenities_select").val(amenity_options).trigger("change");
                get_amenity_options(amenity_options)
                toggleNextButton();

            });

            $(document).on('click', '.remove-item', function() {
                if ($(this).closest('.single_amenity').attr('data-id')) {
                    const itemId = $(this).parent().data('id').toString();
                    $(this).parent().remove();
                    const select2Element = $('.select2');
                    const selectedValues = select2Element.val().map(value => value.toString());
                    console.log("Before filtering:", selectedValues);
                    const updatedValues = selectedValues.filter(value => value !== itemId);
                    console.log("After filtering:", updatedValues);
                    select2Element.val(updatedValues).trigger('change');

                    if ($(`#box1-amenity-${itemId}`).is(":checked")) {
                        $(`#box1-amenity-${itemId}`).click();
                        const deselectedValues = Array.isArray(updatedValues) ? updatedValues : [
                            updatedValues
                        ];
                        get_amenity_options(deselectedValues);
                        toggleNextButton();
                    }

                } else {
                    $(this).parent().remove();
                }
            });

            // $(document).on('click', '.listing_stepper .place_amenities_step .add_custom_amenities .add_amen_btn2',
            //     function() {
            //         var customAmenityCount = $('.custom_amenitiy_single').length + 1;
            //         var custom_amenity = $(this).closest('.add_btn_wrapper2').find('input[type="text"]').val();
            //         $('.listing_stepper .selected_amenities_wrapper').append(
            //             '<div class="single_amenity custom_amenitiy_single"> <div class="icon_name_wrapper"> <img src="{{ asset('website') }}/images/snowflake.png" /> <label>' +
            //             custom_amenity + '</label> <input type="text" name="custom_amenities[]" value="' +
            //             custom_amenity + '" id="custom_amenities_' + customAmenityCount +
            //             '"></div> <a href="javascript:void(0)" class="remove-item"><i class="far fa-times-circle"></i></a></div>'
            //         );
            //         $('.listing_stepper .place_amenities_step .add_custom_amenity_field').val('');
            //     });

            // $(document).on('change','.amenity-modal .check-box .input_parent input[type="checkbox"]',function(){
            //     var amenityID = $(this).val();
            //     $('.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity').each(function(){
            //         if($(this).data('id') == amenityID){
            //             $(this).remove();
            //         }
            //     });
            // });

        });
    </script>
@endpush
