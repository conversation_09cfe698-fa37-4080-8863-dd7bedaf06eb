<div class="modal fade upload_doc_download" id="uploaded_doc" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" id="view_document" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <span class="close" data-dismiss="modal">&times;</span>
                <h1 class="modal-title" id="">{{ translate('dashboard_listing.uploaded_documents') }}</h1>
                <div class="form_field_padding">
                    <div class="download_all d-flex justify-content-end">
                        <button class="btn btn_yellow " id="download_all_btn">{{ translate('dashboard_listing.download_all') }}</button>
                    </div>
                    <div class="mb-3 uploaded_doc_wrapper p_top">
                        <div id="spinner" class="spinner" style="display:none;">
                            <div class="double-bounce1"></div>
                            <div class="double-bounce2"></div>
                        </div>
                        @for ($d = 1; $d < 6; $d++)
                            <div class="single_doc d-flex justify-content-between align-items-center padding divider">
                                <a href="#!" target="_blank" class="upload_doc_view">
                                    <div class="doc d-flex justify-content-between align-items-center gap-3">
                                        {{-- Add image if needed --}}
                                        <img src="{{asset('website')}}" alt="Doc" class="doc_preview" height="80px" width="100px">
                                        <h6 class="doc_title">{{ translate('dashboard_listing.uploaded_document') }} #{{ $d }}</h6>
                                    </div>
                                </a>
                                <div class="menu_doc ">
                                    <button class="download trans_btn" type="button" id="download_btn">
                                        <i class="fas fa-download fs-20" style="color: var(--primary-color);"></i>
                                    </button>
                                </div>
                            </div>
                        @endfor
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
