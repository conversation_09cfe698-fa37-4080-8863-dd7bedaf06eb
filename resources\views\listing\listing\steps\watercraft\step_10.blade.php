@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "inclusion-exclusion");
@endphp
<fieldset class="accommodations_rules_step inclusion_exclusion_step">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    @isset($step_data->sub_title)
                        <div class="step_description">
                            <p>{{ $step_data->sub_title ?? "" }}</p>
                        </div>
                    @endisset
                    <div class="accommodation_custom_rules_wrapper scrollable-section">
                        <div class="row">
                            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-6">
                                <div class="inner_section_col_left">
                                    <div class="custom_add_tags_field_wrapper">
                                        <div class="label_addBtn_wrapper">
                                            <label for="">{{ translate('stepper.whats_included') }}</label>
                                            <a class="add_tag_btn cust_add" href="javascript:void(0)">{{ translate('stepper.add') }}</a>
                                        </div>
                                        <div class="txt_field">
                                            <input class="form-control field_input no_validate" type="text"
                                                placeholder="E.g., Life jackets, snorkeling gear, fuel, onboard refreshments">
                                        </div>
                                        <div class="custom_tags_wrapper">
                                            @foreach ($listing->includes ?? [] as $include)
                                                <div class="single_custom_tag">
                                                    <label for="">{{ $include->name }}</label>
                                                    <input value="{{ $include->name }}" type="hidden"
                                                        name="includes[]">
                                                    <a href="javascript:void(0)" class="remove-item"><i
                                                            class="far fa-times-circle"></i></a>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-6">
                                <div class="inner_section_col_left">
                                    <div class="custom_add_tags_field_wrapper">
                                        <div class="label_addBtn_wrapper">
                                            <label for="">{{ translate('stepper.whats_not_included') }}</label>
                                            <a class="add_tag_btn_notAllowed cust_add" href="javascript:void(0)">{{ translate('stepper.add') }}</a>
                                        </div>
                                        <div class="txt_field">
                                            <input class="form-control field_input no_validate" type="text"
                                                placeholder="E.g., Alcohol, additional fuel, docking fees, gratuity for crew">
                                        </div>
                                        <div class="custom_tags_wrapper">
                                            @foreach ($listing->not_includes ?? [] as $not_include)
                                                <div class="single_custom_tag">
                                                    <label for="">{{ $not_include->name }}</label>
                                                    <input value="{{ $not_include->name }}" type="hidden"
                                                        name="not_includes[]">
                                                    <a href="javascript:void(0)" class="remove-item"><i
                                                            class="far fa-times-circle"></i></a>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="Next" />
    <input type="button" name="previous" class="previous action-button-previous" value="Back" />
</fieldset>


@push('js')
    <script>


        function validateInclusionExclusionField() {
            let noteInputs = $('.inclusion_exclusion_step .custom_add_tags_field_wrapper .field_input');
            let allEmpty = false;

            noteInputs.each(function() {
                if ($(this).val().trim().length > 0) {
                    allEmpty = true;
                    return true;
                }
            });

            $('.inclusion_exclusion_step .next').prop("disabled", allEmpty);
        }

        $(document).on('input blur', '.inclusion_exclusion_step .custom_add_tags_field_wrapper .field_input', function() {
            validateInclusionExclusionField();
        });


        $(document).on('click', '.inclusion_exclusion_step .custom_add_tags_field_wrapper .add_tag_btn', function() {
            var field_value = $(this).closest('.custom_add_tags_field_wrapper').find('.field_input').val();
            var tags_count = $(this).closest('.custom_add_tags_field_wrapper').find('.single_custom_tag').length +
                1;
            if (field_value != "") {
                $(this).closest('.custom_add_tags_field_wrapper').find('.custom_tags_wrapper').append(
                    `<div class="single_custom_tag">
                        <label for="">${field_value}</label>
                        <input value="${field_value}" type="hidden" name="includes[]">
                        <a href="javascript:void(0)" class="remove-item"><i class="far fa-times-circle"></i></a>
                    </div>`
                );
                $(this).closest('.custom_add_tags_field_wrapper').find('.field_input').val('');
                validateInclusionExclusionField();
            }
        });

        $(document).on('click', '.inclusion_exclusion_step .custom_add_tags_field_wrapper .add_tag_btn_notAllowed', function() {
            var field_value = $(this).closest('.custom_add_tags_field_wrapper').find('.field_input').val();
            var tags_count = $(this).closest('.custom_add_tags_field_wrapper').find('.single_custom_tag').length +
                1;
            if (field_value != "") {
                $(this).closest('.custom_add_tags_field_wrapper').find('.custom_tags_wrapper').append(
                    `<div class="single_custom_tag">
                        <label for="">${field_value}</label>
                        <input value="${field_value}" type="hidden" name="not_includes[]">
                        <a href="javascript:void(0)" class="remove-item"><i class="far fa-times-circle"></i></a>
                    </div>`
                );
                $(this).closest('.custom_add_tags_field_wrapper').find('.field_input').val('');
                validateInclusionExclusionField();
            }
        });

        $(document).on('click', '.inclusion_exclusion_step .custom_add_tags_field_wrapper .remove-item', function() {
            $(this).closest('.single_custom_tag').remove();
            validateInclusionExclusionField();
        });
    </script>
@endpush
