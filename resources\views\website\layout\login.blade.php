<div class="modal fade login " id="login" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content p-4">
            <div class="modal-header b-none">
                <h4 class="modal-title mx-auto">{{ translate('home.login_to') }}   <span class="color luxu">LuxuStars</span></h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <form id="login-form" action="{{ route('login') }}" method="POST" onkeydown="if(event.key === 'Enter'){event.preventDefault(); document.getElementById('login-form').submit();}">
                    @csrf
                    <div class="form-card">
                        <div class="form-outline mb-4">
                            <input type="email" value="{{ old("email") }}" class="form-control" name="email" placeholder="{{ translate('home.email_address') }}" />
                            @error('email')
                                @php
                                    activity('User login Failed')
                                        ->performedOn(App\User::first())
                                        ->log($message);
                                @endphp
                                <p class="text-danger text-start">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="form-outline mb-4">
                            <input type="password" class="form-control" id="password" name="password"
                                placeholder="{{ translate('home.password') }}  " />
                            <i class="fas fa-eye eye_icon" id="pass_btn"></i>
                            @error('password')
                                @php
                                    activity('User login Failed')
                                        ->performedOn(App\User::first())
                                        ->log($message);
                                @endphp
                                <p class="text-danger text-start">{{ $message }}</p>
                            @enderror
                        </div>
                        <div class="forgot pb-3">
                            <button type="button" class="forgot not_btn blue fs-14" data-bs-toggle="modal"
                                data-bs-dismiss="modal" data-bs-target="#forgot" aria-hidden="true" tabindex="-1">
                                {{ translate('home.forgot_password') }}
                            </button>
                        </div>
                    </div>
                    <button type="submit" id="login-btn"
                        class="btn button login btn-block mb-4 action-button">{{ translate('home.login') }}  </button>
                    <!-- Register buttons -->
                    <div class="text-center">
                        <p class="fs-14">{{ translate('home.not_a_member') }}   <button type="button" class=" not_btn blue mx-2"
                                data-bs-toggle="modal" data-bs-dismiss="modal" data-bs-target="#signUp">
                                {{ translate('home.sign_up') }}
                            </button></p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@push('js')
    <script>
        $(document).ready(function() {
            // Handle login button click
            $('#login-btn').click(function(e) {
                e.preventDefault();
                document.getElementById('login-form').submit();
            });

            // Handle Enter key press on the form
            $('#login-form').on('keydown', function(e) {
                if (e.keyCode === 13) { // Enter key
                    e.preventDefault();
                    document.getElementById('login-form').submit();
                    return false;
                }
            });
        });
    </script>
    <script>
        $("#pass_btn").click(function() {
            var passwordInput = $("#password");

            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#pass_btn').addClass('fa-eye-slash')
                $('#pass_btn').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#pass_btn').removeClass('fa-eye-slash')
                $('#pass_btn').addClass('fa-eye')
            }
        });
    </script>
    @if ($errors->has('email') || $errors->has('password'))
        <script>
            $(function() {
                $('#login').modal('show');
                $('#signUp').modal('hide');
            });
        </script>
    @endif
@endpush
