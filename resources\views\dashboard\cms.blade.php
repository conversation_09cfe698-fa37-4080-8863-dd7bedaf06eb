@extends('layouts.master')
@push('css')
    <!-- <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" /> -->
    <!-- <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" /> -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    {{-- <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script> --}}
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="{{ asset('plugins/components/dropify/dist/css/dropify.min.css') }}">


    <link rel="stylesheet" href="{{ asset('website/assets_cdn/jodit_editor/jodit.min.css') }}">
    <link rel="stylesheet" href="{{ asset('website/assets_cdn/jodit_editor/plugins/speech-recognize/speech-recognize.min.css') }}">

    <style>
        #faqForm .btn.focus,
        #faqForm .btn:focus,
        #faqForms .btn:hover {
            color: #FFCE32;
        }

        .dropify-wrapper {
            border-radius: 22px;
        }

        .dropify-wrapper.disabled input {
            cursor: pointer;
        }

        input:disabled {
            cursor: not-allowed !important;
        }

        input:disabled+.dropify-preview {
            background-color: #e6e6e6;
        }

        /* input:disabled + .dropify-preview .dropify-render img  { opacity: 0.75;} */
        .dropify-wrapper .dropify-preview {
            background-color: #F8F9FA;
            border: 0;
        }

        .dropify-wrapper .dropify-preview .dropify-render img {
            mix-blend-mode: darken;
        }

        .content-between {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .inputRow {
            border: 1px solid #aeaeae7e;
            padding: 15px;
            border-radius: 15px;
            margin-bottom: 20px;
            background-color: white;
        }

        .inputRow input,
        .inputRow textarea {
            background-color: #ebebeb2e;
        }

        .custom_link_popup {display: flex; flex-direction: column; gap: 5px}
        .custom_link_popup * {font-family: "Poppins"}
        .custom_link_popup input[type="text"] {height: 45px; border: 1px solid #b8b8b8; border-radius: 10px; padding-left: 10px; padding-right: 10px; color: black; font-family: "Poppins"}
        .custom_link_popup button, .jodit-ui-button {width: 100%; padding: 10px 0; background-color: #ffce32; color: black; border-radius: 10px; font-weight: 500}
        .custom_link_popup br {display: none;}
        .jodit-ui-button {display: flex; justify-content: center; align-items: center; gap: 5px;}
        body .jodit-ui-button .jodit-ui-button__text {flex-grow: unset;}
        body .jodit-ui-button:hover, .custom_link_popup button:hover {background-color: #000 !important; color: white}
        body .jodit-ui-button:hover .jodit-ui-button__icon svg {fill: white;}

        .custom_anchor_popup {max-height: 240px; overflow-y: auto; min-width: 150px;}
        .custom_anchor_popup div:not(:last-child) {border-bottom: 1px solid lightgray; padding-bottom: 9px;}
        .custom_anchor_popup div:not(:first-child) {padding-top: 5px}

        .jodit-tabs__button {display: flex; justify-content: center; align-items: center;} 
        .jodit-tabs__button[aria-pressed="true"] {background-color: #ffce32 !important; color: black;}
        .jodit-tabs__button[aria-pressed="false"] {background-color: #000; color: white !important;}
        .jodit-tabs__button .jodit-ui-button__text {flex-grow: unset !important; flex-basis: fit-content; background: unset !important;}
        .jodit-tabs__button[aria-pressed="true"] .jodit-ui-button__text {color: black}
        .jodit-tabs__button[aria-pressed="true"]:hover .jodit-ui-button__text {color: black}
        .jodit .jodit-popup .jodit-tabs__button[aria-pressed="true"]:hover {background: #ffce32 !important;}
        .jodit-tabs__button[aria-pressed="false"] .jodit-ui-button__text {color: white;}
        .jodit-container .jodit-toolbar__box .jodit-ui-group_group_insert .jodit-toolbar-button_link {display: none;}
        .custom_anchor_popup:not(:has(div)) {position: relative; display: flex; justify-content: center; align-items: center}
        .custom_anchor_popup:not(:has(div)):before {content: "No anchors found";}
        body .jodit-dialog__panel {width: 500px;}
        .jodit-ui-group__file {display: none;}

    </style>
@endpush
@section('content')
    <section class="head_btn cms_btn">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <h1>{{ translate('content_management_system.content_management_system') }}</h1>
                    <div class="custom_btns">
                        <div class="head_bt">
                            <ul class="nav nav-pills">
                                <li class="active"><a data-toggle="pill" href="#home_pane">{{ translate('content_management_system.home') }}</a></li>
                                <li><a data-toggle="pill" href="#about_pane">{{ translate('content_management_system.about') }}</a></li>
                                <li><a data-toggle="pill" href="#contact_pane">{{ translate('content_management_system.contact') }}</a></li>
                                <li><a data-toggle="pill" href="#amenities_pane">{{ translate('content_management_system.amenities') }}</a></li>
                                <li><a data-toggle="pill" href="#categories_pane">{{ translate('content_management_system.categories') }}</a></li>
                                <li><a data-toggle="pill" href="#payment_pane">{{ translate('content_management_system.payment_fee') }}</a></li>
                                {{-- <li><a data-toggle="pill" href="#faq_pane">{{ trans('help_center') }}</a></li> --}}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="tab-content">
        <div id="home_pane" class="tab-pane fade in active">
            <section class="det_form cmsForms">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <form id="aboutForm" method="POST" action="{{ route('cms_home') }}"
                                enctype="multipart/form-data">
                                @csrf
                                <div class="row">
                                    <div class="col-md-12">
                                        <h2 class="semi-bold">{{ translate('content_management_system.navigation_bar') }}</h2>
                                    </div>
                                    <div class="col-sm-4 d-flex">
                                        <div class="form_field_padding">
                                            <label class="box-title">{{ translate('content_management_system.logo') }}</label>
                                            <input type="file" id="logo" name="dashboard_logo" class="dropify"
                                                accept="image/*" data-height="150"
                                                data-default-file="{{ asset('/') . $common_setting->dashboard_logo }}" />
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    {{-- <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">Logo</label>
                                            <div class="row image_preview" id="image_preview">
                                                <div class="col-md-4 form-check">
                                                    <input type="file" class="product_image d-none" accept="image/*"
                                                        id="logo" name="dashboard_logo" onchange="preview_logo();" />
                                                    <label class="form-check-label files " for="logo">
                                                        <img src="{{ asset('/') . $common_setting->dashboard_logo }}"
                                                            alt="your image" />
                                                    </label>
                                                </div>
                                                <div class="col-md-5">
                                                    <div class="actual_preview"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div> --}}
                                    <div class="col-sm-4 d-flex">
                                        <div class="form_field_padding">
                                            <label class="box-title">{{ translate('content_management_system.favicon') }}</label>
                                            <input type="file" id="favicon" name="favicon" class="dropify"
                                                accept="image/*" data-height="150"
                                                data-default-file="{{ asset('/') . $common_setting->favicon }}" />
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    {{-- <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">Favicon</label>
                                            <div class="row image_preview" id="fav_preview">
                                                <div class="col-md-4 form-check">
                                                    <input type="file" class="product_image d-none" accept="image/*"
                                                        id="favicon" name="favicon" onchange="preview_favicon();" />
                                                    <label class="form-check-label files " for="favicon">

                                                        <img src="{{ asset('/') . $common_setting->favicon }}"
                                                            alt="your image" />
                                                    </label>
                                                </div>
                                                <div class="col-md-5">
                                                    <div class="actual_preview"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div> --}}
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <h2 class="semi-bold">{{ translate('footer.all_rights_reserved') }}</h2>
                                    </div>
                                    <div class="col-md-4">
                                        <h3>{{ translate('content_management_system.about_site') }}</h3>
                                        <div class="d-flex">
                                            <div class="form_field_padding">
                                                <label for="">{{ translate('content_management_system.description') }}</label>
                                                <textarea class="form-control" name="description" id="" rows="7" column="25" placeholder="Text Field">{{ $common_setting->description }}</textarea>
                                            </div>
                                            <i class="fas fa-edit"></i>
                                        </div>
                                    </div>

                                    {{-- <div class="col-md-4">
                                        <h3>Contact</h3>
                                        <div class="d-flex">
                                            <div class="form_field_padding">
                                                <label for="">{{ trans('phone') }}</label>
                                                <input type="tel" name="phone" id="phone" class="form-control"
                                                    placeholder="0000 0000 0000" value="{{ $common_setting->phone }}">
                                            </div>
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="d-flex">
                                            <div class="form_field_padding">
                                                <label for="">{{ trans('address') }}</label>
                                                <input type="text" name="address" id="email" class="form-control"
                                                    placeholder="<EMAIL>"
                                                    value="{{ $common_setting->address }}">
                                            </div>
                                            <i class="fas fa-edit"></i>
                                        </div>
                                    </div> --}}
                                    <div class="col-md-4">
                                        <h3>{{ translate('content_management_system.social_links') }}</h3>
                                        <div class="d-flex">
                                            <div class="form_field_padding">
                                                <label for="">{{ translate('content_management_system.facebook') }}</label>
                                                <input type="url" name="facebook_link" id="facebook"
                                                    class="form-control" placeholder="www.example.com"
                                                    value="{{ $common_setting->facebook_link }}">
                                            </div>
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="d-flex">
                                            <div class="form_field_padding">
                                                <label for="">{{ translate('content_management_system.instagram') }}</label>
                                                <input type="url" name="instagram_link" id="facebook"
                                                    class="form-control" placeholder="www.example.com"
                                                    value="{{ $common_setting->instagram_link }}">
                                            </div>
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="d-flex">
                                            <div class="form_field_padding">
                                                <label for="">{{ translate('content_management_system.tiktok') }}</label>
                                                <input type="url" name="tiktok_link" id="facebook"
                                                    class="form-control" placeholder="www.example.com"
                                                    value="{{ $common_setting->tiktok_link }}">
                                            </div>
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="d-flex">
                                            <div class="form_field_padding">
                                                <label for="">{{ translate('content_management_system.youtube') }}</label>
                                                <input type="url" name="youtube_link" id="facebook"
                                                    class="form-control" placeholder="www.example.com"
                                                    value="{{ $common_setting->youtube_link }}">
                                            </div>
                                            <i class="fas fa-edit"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-10 d-flex">
                                        <h2>{{ translate('content_management_system.privacy_policy') }}</h2>
                                    </div>
                                    <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">{{ translate('content_management_system.title') }}</label>
                                            <input type="text" name="privacy_policy_title" id="title1"
                                                class="form-control" placeholder="{{ translate('content_management_system.title_here') }}"
                                                value="{{ $policy->title ?? '' }}">
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">{{ translate('content_management_system.description') }}</label>
                                            <textarea class="form-control not-disabled jodit_editor" name="privacy_policy_description" id="" rows="7"
                                                column="25" placeholder="Text Field">{!! htmlspecialchars_decode($policy->description) ?? '' !!}</textarea>
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-10 d-flex">
                                        <h2>{{ translate('content_management_system.terms_conditions') }}</h2>
                                    </div>
                                    <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">{{ translate('content_management_system.title') }}</label>
                                            <input type="text" name="term_condtion_title" id="title1"
                                                class="form-control" placeholder="Title Here"
                                                value="{{ $term_condition->title ?? '' }}">
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">{{ translate('content_management_system.description') }}</label>
                                            <textarea class="form-control not-disabled jodit_editor" name="term_condtion_description" id="" rows="7"
                                                column="25" placeholder="Text Field">{!! htmlspecialchars_decode($term_condition->description) ?? '' !!}</textarea>
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-10 d-flex">
                                        <h2>{{ translate('content_management_system.suppliers_agreement') }}</h2>
                                    </div>
                                    <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">{{ translate('content_management_system.title') }}</label>
                                            <input type="text" name="supplier_title" id="title1"
                                                class="form-control" placeholder="Title Here"
                                                value="{{ $supplier_aggreement->title ?? '' }}">
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">{{ translate('content_management_system.description') }}</label>
                                            <textarea class="form-control not-disabled jodit_editor" name="supplier_description" id="" rows="7"
                                                column="25" placeholder="Text Field">{{ htmlspecialchars_decode($supplier_aggreement->description ?? '') }}</textarea>
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form_field_padding">
                                            <div class="list_form_btn">
                                                {{-- <input class="btn cancel_btn" type="submit" value="Cancel"> --}}
                                                <a href="{{ route('cms') }}" class="btn cancel_btn">{{ translate('content_management_system.cancel') }}</a>
                                                <input class="btn create_btn" type="submit" value="{{ translate('content_management_system.save_and_update') }}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <div id="about_pane" class="tab-pane fade">
            <section class="det_form cmsForms">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <h2 class="semi-bold">{{ translate('content_management_system.about_us') }}</h2>
                            <form id="aboutForm" method="POST" action="{{ route('cms_about') }}"
                                enctype="multipart/form-data">
                                @csrf
                                <div class="row">
                                    <div class="col-sm-4 d-flex">
                                        <div class="form_field_padding">
                                            <label class="box-title">{{ translate('content_management_system.banner_image') }}</label>
                                            <input type="file" id="banner_image" name="banner_image" class="dropify"
                                                accept="image/*" data-height="150"
                                                data-default-file="{{ asset('website') . '/' . ($about->banner_image ?? 'images/fileclip.png') }}" />
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    {{-- <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">Banner Image</label>
                                            <div class="row image_preview" id="banner_preview">
                                                <div class="col-md-4 form-check">
                                                    <input type="file" class="product_image d-none" accept="image/*"
                                                        id="banner_image" name="banner_image"
                                                        onchange="preview_banner();" />
                                                    <label class="form-check-label files " for="banner_image">
                                                        <img src="{{ asset('website') . '/' . ($about->banner_image ?? 'images/fileclip.png') }}"
                                                            alt="banner image" />
                                                    </label>
                                                </div>
                                                <div class="col-md-5">
                                                    <div class="actual_preview"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div> --}}
                                    <div class="col-md-10 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">{{ translate('content_management_system.title') }}</label>
                                            <input type="text" name="main_title" id="title1" class="form-control"
                                                placeholder="{{ translate('content_management_system.title_here') }}" value="{{ $about->title ?? '' }}">
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                </div>
                                @foreach ($about_contents as $about_key => $about_content)
                                    <div class="row">
                                        <div class="col-md-10 d-flex">
                                            <h2>{{ translate('content_management_system.section') }} {{ $about_content->id }}</h2>
                                            <input type="hidden" class="about-content-id"
                                                name="about[{{ $about_key }}][id]" value="{{ $about_content->id }}">
                                        </div>
                                        <div class="col-md-10 d-flex">
                                            <div class="form_field_padding">
                                                <label for="">{{ translate('content_management_system.title') }}</label>
                                                <input type="text" name="about[{{ $about_key }}][title]"
                                                    id="title1" class="form-control" placeholder="{{ translate('content_management_system.title_here') }}"
                                                    value="{{ $about_content->title }}">
                                            </div>
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="col-md-10 d-flex">
                                            <div class="form_field_padding">
                                                <label for="">{{ translate('content_management_system.description') }}</label>
                                                <textarea class="form-control not-disabled jodit_editor" name="about[{{ $about_key }}][description]" id="about_description_{{ $about_key }}" rows="7"
                                                    column="25" placeholder="{{ translate('content_management_system.text_field') }}">{{ $about_content->description }}</textarea>
                                            </div>
                                            <i class="fas fa-edit"></i>
                                        </div>
                                    </div>
                                @endforeach
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form_field_padding">
                                            <div class="list_form_btn">
                                                {{-- <input class="btn cancel_btn" type="submit" value="Cancel"> --}}
                                                <a href="javascript:void(0)" class="btn cancel_btn">{{ translate('content_management_system.cancel') }}</a>
                                                <input class="btn create_btn" type="submit" value="Save & Update">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        <div id="contact_pane" class="tab-pane fade ">
            <section class="det_form cmsForms">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <h2 class="semi-bold">{{ translate('content_management_system.contact_us') }}</h2>
                            <form id="contactForm" method="POST" action="{{ route('cms_contact') }}">
                                @csrf
                                <div class="row">
                                    <div class="col-md-4 d-flex">
                                        <div class="form_field_padding">
                                            <label for="">{{ translate('content_management_system.phone') }}</label>
                                            <input type="phone" name="phone" id="phone" class="form-control"
                                                placeholder="0000000000000" value="{{ $common_setting->contact_phone }}">
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                    <div class="col-md-4 d-flex">
                                        <div class="form_field_padding">
                                            <label for="email">{{ translate('content_management_system.email') }}</label>
                                            <input type="email" name="email" id="email" class="form-control"
                                                placeholder="<EMAIL>"
                                                value="{{ $common_setting->contact_email }}">
                                        </div>
                                        <i class="fas fa-edit"></i>

                                    </div>
                                    <div class="col-md-4 d-flex">
                                        <div class="form_field_padding">
                                            <label for="address">{{ translate('content_management_system.address') }}</label>
                                            <input type="text" name="address" id="address" class="form-control"
                                                placeholder="{{ translate('content_management_system.address_here') }}"
                                                value="{{ $common_setting->contact_address }}">
                                        </div>
                                        <i class="fas fa-edit"></i>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form_field_padding">
                                            <div class="list_form_btn">
                                                {{-- <input class="btn cancel_btn" type="submit" value="Cancel"> --}}
                                                <a href="javascript:void(0)" class="btn cancel_btn">{{ translate('content_management_system.cancel') }}</a>
                                                <input class="btn create_btn" type="submit" value="Save & Update">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        {{-- Amenity --}}
        <div id="amenities_pane" class="tab-pane fade ">
            <section class="table_sect">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-flex justify-content-between p_bottom">
                                <h2 class="semi-bold">{{ translate('content_management_system.amenities') }}</h2>

                                <div class="head_bt d-flex flex-wrap align-items-center gap-2">
                                    <div class="nav_search main me-2">
                                        <!-- Actual search box -->
                                        <div class="form-group has-feedback has-search m-0">
                                            <form class="example" action="/action_page.php" style="width: 100%">
                                                <button type="button"><i class="fa fa-search"></i></button>
                                                <input type="text" placeholder="{{ translate('content_management_system.search') }}" id="amenity-search-input"
                                                    class="searchBar" name="search">
                                                {{-- <i class="fa-solid fa-filter" style="color: #000000;"></i> --}}
                                            </form>
                                        </div>
                                    </div>
                                    <a href="{{ route('amenities.create') }}"
                                        class="btn btn_yellow">{{ translate('content_management_system.add_amenity_category') }}</a>
                                </div>
                            </div>
                            <div class="table_wrraper">
                                <div class="tabel_hdng_icon">
                                    <!-- <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i> -->
                                </div>
                                <div class="table-responsive">
                                    <table id="amenities-table" class="table  custom_table">
                                        <thead>
                                            <tr>
                                                <th scope="col">{{ trans('#') }}</th>
                                                <th scope="col">{{ translate('content_management_system.name') }}</th>
                                                <th scope="col">{{ translate('content_management_system.categories') }}</th>
                                                <th scope="col">{{ translate('content_management_system.action') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {{-- @forelse ($amenities as $amenity)
                                                <tr>
                                                    <td>{{ $loop->iteration ?? $amenity->id }}</td>
                                                    <td>
                                                        <a class="options_link"
                                                            href="{{ route('amenity-option.index', ['amenity_id' => $amenity->ids]) }}">
                                                            {{ $amenity->name }}
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <p class="limit">
                                                            @foreach ($amenity->categories as $amenityCategory)
                                                                {{ $amenityCategory->display_name }}{{ $loop->last ? '' : ',' }}
                                                            @endforeach
                                                        </p>
                                                    </td>
                                                    <td class="form_btn ">
                                                        <div class="dropdown">
                                                            <button class=" dropdown-toggle" type="button"
                                                                id="dropdownMenuButton" data-toggle="dropdown"
                                                                aria-haspopup="true" aria-expanded="false">
                                                                <i class="fa-solid fa-ellipsis"
                                                                    style="color: #a0aec0;"></i>
                                                            </button>
                                                            <div class="dropdown-menu"
                                                                aria-labelledby="dropdownMenuButton">
                                                                <a class="dropdown-item"
                                                                    href="{{ route('amenity-option.index', ['amenity_id' => $amenity->ids]) }}">
                                                                    {{ trans('view') }}</a>
                                                                <a class="dropdown-item"
                                                                    href="{{ route('amenities.edit', $amenity->ids) }}">
                                                                    {{ trans('edit') }}</a>
                                                                <div class="dropdown-item ">
                                                                    <form id="delete-form-{{ $amenity->id }}"
                                                                        method="POST"
                                                                        action="{{ url('/amenities/amenities/' . $amenity->id) }}"
                                                                        accept-charset="UTF-8" style="display:inline">
                                                                        {{ method_field('DELETE') }}
                                                                        {{ csrf_field() }}
                                                                        <button type="button"
                                                                            class="dropdown-item delete-btn"
                                                                            data-id="{{ $amenity->id }}"
                                                                            title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Amenities') }}">
                                                                            {{ trans('delete') }}
                                                                        </button>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr class="text-center">
                                                    <td colspan="4">
                                                        <p>No ammentites added</p>
                                                    </td>
                                                </tr>
                                            @endforelse --}}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            {{-- New Modal --}}
            {{-- <section class="amen reject">
                <div class="modal fade add_amenity_category_modal" id="amen" tabindex="-1" role="dialog"
                    aria-labelledby="exampleModalLabel1">
                    <div class="modal-dialog" id="amenModal" role="document">
                        <div class="modal-content">
                            <div class="modal-body">
                                <span class="close" data-dismiss="modal">&times;</span>
                                <h1 class="modal-title" id="amenity_title">{{ trans('amenity') }}</h1>
                                <div class="form_field_padding">
                                    <div class="mb-3 mod_cust_text">
                                        <form id="amenity_form" method="POST">
                                            @csrf
                                            {{ method_field('PATCH') }}

                                            <div class="form_field_padding">
                                                <div class=" mb-3">
                                                    <input class="form-control" name="en[name]" id="amenity_name_en"
                                                        placeholder="English Name" />
                                                </div>
                                                <div class=" mb-3">
                                                    <input class="form-control" name="es[name]" id="amenity_name_es"
                                                        placeholder="Spanish Name" />
                                                </div>
                                                <div class="cust_select mb-3">
                                                    <select class="form-control multi_select" name="category_id[]"
                                                        id="amenCtg">
                                                        @foreach (App\Category::get() as $category)
                                                            <option value="{{ $category->id }}">
                                                                {{ $category->display_name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class=" modal_btn  ">
                                                <button type="button " class="btn btn_yellow btn-block"
                                                    id="amenity_btn_title">{{ trans('amenity') }}</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section> --}}
        </div>
        {{-- Amenity End --}}

        <div id="categories_pane" class="tab-pane fade ">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <h2 class="semi-bold">{{ translate('content_management_system.categories') }}</h2>
                        <div class="table_wrraper">
                            <div class="tabel_hdng_icon">
                            </div>
                            <div class="table-responsive">
                                <table id="myTable" class="table  custom_table">
                                    <thead>
                                        <tr>
                                            <th scope="col">{{ translate('content_management_system.s_no') }}</th>
                                            <th scope="col">{{ translate('content_management_system.image') }}</th>
                                            <th scope="col">{{ translate('content_management_system.categories_english') }}</th>
                                            <th scope="col">{{ translate('content_management_system.categories_spanish') }}</th>
                                            <th scope="col">{{ translate('content_management_system.action') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($categories as $category)
                                            @isset($category)
                                                <tr>
                                                    <td>{{ $loop->iteration }}</td>
                                                    <td> <img src="{{ asset('website') . '/' . $category->image }}"
                                                            style="mix-blend-mode: difference;" alt=""> </td>
                                                    <td>
                                                        <a class="options_link"
                                                            href="{{ route('listing-type.index', ['category_id' => $category->ids]) }}">
                                                            {{ ucfirst($category->translateOrNew('en')->display_name) }}</a>
                                                    </td>
                                                    <td>
                                                        <a class="options_link"
                                                            href="{{ route('listing-type.index', ['category_id' => $category->ids]) }}">
                                                            {{ ucfirst($category->translateOrNew('es')->display_name) }}</a>
                                                    </td>
                                                    <td class="form_btn ">
                                                        <div class="dropdown">
                                                            <button class=" dropdown-toggle" type="button"
                                                                id="dropdownMenuButton" data-toggle="dropdown"
                                                                aria-haspopup="true" aria-expanded="false">
                                                                <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                            </button>
                                                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <a class="dropdown-item "
                                                                    href="{{ route('listing-type.index', ['category_id' => $category->ids]) }}">
                                                                    {{ translate('content_management_system.view') }}</a>
                                                                <button class="dropdown-item " data-toggle="modal"
                                                                    data-target="#ctg{{ $category->id }}"
                                                                    data-category-id="{{ $category->id }}">
                                                                    {{ translate('content_management_system.edit') }}</button>
                                                            </div>
                                                            {{-- Modal --}}
                                                            <section class="amen ctg reject">
                                                                <div class="modal fade" id="ctg{{ $category->id }}"
                                                                    tabindex="-1" role="dialog"
                                                                    aria-labelledby="exampleModalLabel1">
                                                                    <div class="modal-dialog" id="ctgModal" role="document">
                                                                        <div class="modal-content">
                                                                            <div class="modal-body">
                                                                                <span class="close"
                                                                                    data-dismiss="modal">&times;</span>
                                                                                <h1 class="modal-title" id="title">
                                                                                    {{ translate('content_management_system.edit_category') }}</h1>
                                                                                <div class="form_field_padding">
                                                                                    <div class="mb-3 mod_cust_text">
                                                                                        <form
                                                                                            action="{{ url('/category/category/' . $category->id) }}"
                                                                                            method="POST">
                                                                                            {{ method_field('PATCH') }}
                                                                                            {{ csrf_field() }}
                                                                                            <div class="form_field_padding">
                                                                                                <div
                                                                                                    class="mb-3 position-relative">
                                                                                                    <input
                                                                                                        class="form-control tax"
                                                                                                        type="number"
                                                                                                        name="tax"
                                                                                                        placeholder="{{ translate('content_management_system.service_fee') }}"
                                                                                                        onKeyPress="if(this.value.length==3) return false;"
                                                                                                        value="{{ $category->tax }}" />
                                                                                                    <i
                                                                                                        class="fas fa-percent percent-icon"></i>

                                                                                                </div>
                                                                                                <div class="mb-3">
                                                                                                    <input class="form-control"
                                                                                                        name="en[display_name]"
                                                                                                        placeholder="{{ translate('content_management_system.name') }}"
                                                                                                        value="{{ $category->translateOrNew('en')->display_name }}" />
                                                                                                </div>
                                                                                                <div class="mb-3">
                                                                                                    <input class="form-control"
                                                                                                        name="es[display_name]"
                                                                                                        placeholder="{{ translate('content_management_system.name') }}"
                                                                                                        value="{{ $category->translateOrNew('es')->display_name }}" />
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class=" modal_btn  ">
                                                                                                <button type="button "
                                                                                                    class="btn yellow"
                                                                                                    style="background-color: #FFCE32 !important">{{ trans('update') }}</button>
                                                                                            </div>
                                                                                        </form>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </section>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endisset
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="payment_pane" class="tab-pane fade">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <h2 class="semi-bold">{{ translate('content_management_system.payment_fee') }}</h2>
                        <div class="table_wrraper">
                            <div class="tabel_hdng_icon">
                            </div>
                            <div class="table-responsive">
                                <table id="myTable" class="table  custom_table">
                                    <thead>
                                        <tr>
                                            <th scope="col">{{ translate('content_management_system.s_no') }}</th>
                                            <th scope="col">{{ translate('content_management_system.name') }}</th>
                                            <th scope="col">{{ translate('content_management_system.percentage') }}</th>
                                            <th scope="col">{{ translate('content_management_system.action') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for ($payment = 0; $payment < 3; $payment++)
                                            <tr>
                                                <td>1</td>
                                                <td>
                                                    Stripe
                                                </td>
                                                <td>
                                                    10%
                                                </td>
                                                <td class="form_btn ">
                                                    <div class="dropdown">
                                                        <button class=" dropdown-toggle" type="button"
                                                            id="dropdownMenuButton" data-toggle="dropdown"
                                                            aria-haspopup="true" aria-expanded="false">
                                                            <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                        </button>
                                                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                            <a class="dropdown-item " href="{{ url('listing-type') }}">
                                                                {{ trans('view') }}</a>
                                                            <button class="dropdown-item " data-toggle="modal"
                                                                data-target="#paymentFee" data-category-id="stripe">
                                                                {{ translate('content_management_system.edit') }}</button>
                                                        </div>
                                                        {{-- Modal --}}
                                                        <section class="amen ctg reject">
                                                            <div class="modal fade" id="paymentFee" tabindex="-1"
                                                                role="dialog" aria-labelledby="exampleModalLabel1">
                                                                <div class="modal-dialog" id="ctgModal" role="document">
                                                                    <div class="modal-content">
                                                                        <div class="modal-body">
                                                                            <span class="close"
                                                                                data-dismiss="modal">&times;</span>
                                                                            <h1 class="modal-title" id="title">
                                                                                {{ translate('content_management_system.edit_percentage') }}</h1>
                                                                            <div class="form_field_padding">
                                                                                <div class="mb-3 mod_cust_text">
                                                                                    <form action="" method="POST">
                                                                                        {{ method_field('PATCH') }}
                                                                                        {{ csrf_field() }}
                                                                                        <div class="form_field_padding">
                                                                                            <div
                                                                                                class="mb-3 position-relative">
                                                                                                <input
                                                                                                    class="form-control tax"
                                                                                                    type="number"
                                                                                                    name="tax"
                                                                                                    placeholder="{{ translate('content_management_system.payment_fee') }}"
                                                                                                    onKeyPress="if(this.value.length==3) return false;"
                                                                                                    value=""
                                                                                                    max="100" />
                                                                                                <i
                                                                                                    class="fas fa-percent percent-icon d-none"></i>
                                                                                            </div>
                                                                                        </div>
                                                                                        <div class=" modal_btn  ">
                                                                                            <button type="button "
                                                                                                class="btn yellow"
                                                                                                style="background-color: #FFCE32 !important">{{ trans('update') }}</button>
                                                                                        </div>
                                                                                    </form>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </section>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endfor
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{-- <div id="faq_pane" class="tab-pane fade ">
            <section class="det_form cmsForms">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <h2 class="semi-bold">{{ trans('help_center') }}</h2>
                            <form id="faqForm" action="{{ route('add_faq') }}" method="POST">
                                @csrf
                                @php
                                    $faqs = App\Models\Faq::all();
                                @endphp
                                <div id="inputFormRow">
                                    @forelse ($faqs as $faq)
                                        <div class="inputRow">
                                            <div class="content-between">
                                                <h1>FAQ #{{ $loop->iteration }}</h1>
                                                @if (!$loop->first)
                                                    <button type="button"
                                                        class="btn btn-danger removeRow">Remove</button>
                                                @endif
                                            </div>
                                            <div class="form_field_padding">
                                                <label for="">Title</label>
                                                <input type="text" class="form-control m-input"
                                                    name="items[{{ $loop->index }}][name]"
                                                    value="{{ $faq->title }}" placeholder="Enter item name"
                                                    autocomplete="off" />
                                            </div>
                                            <div class="form_field_padding">
                                                <label for="">Description</label>
                                                <textarea class="form-control m-input editor" name="items[{{ $loop->index }}][description]"
                                                    placeholder="Enter description" autocomplete="off">{!! htmlspecialchars_decode($faq->description) !!}</textarea>
                                            </div>
                                        </div>
                                    @empty
                                        <div class="inputRow">
                                            <div class="content-between">
                                                <h1>FAQ #1</h1>
                                            </div>
                                            <div class="form_field_padding">
                                                <label for="">Title</label>
                                                <input type="text" class="form-control m-input editor"
                                                    name="items[0][name]" placeholder="Enter item name"
                                                    autocomplete="off" />
                                            </div>
                                            <div class="form_field_padding">
                                                <label for="">Description</label>
                                                <textarea class="form-control m-input editor" name="items[0][description]" placeholder="Enter description"
                                                    autocomplete="off"></textarea>
                                            </div>
                                        </div>
                                    @endforelse
                                </div>


                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form_field_padding">
                                            <div class="list_form_btn">
                                                <input class="btn cancel_btn add" id="addRow" type="button"
                                                    value="Add More Questions">
                                                <input class="btn create_btn" type="submit" value="Save">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>
        </div> --}}

    </div>
    {{-- </div> --}}
@endsection
@push('js')
    {{-- <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajaxd/libs/twitter-bootstrap/4.2.1/js/bootstrap.min.js"></script> --}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>
    <script src="{{ asset('js/jasny-bootstrap.js') }}"></script>

    <script src="{{ asset('plugins/components/dropify/dist/js/dropify.min.js') }}"></script>

    <script>
        $(document).ready(function() {
            $(document).on('keyup', '[name="tax"]', function() {
                var taxRate = $(this).val();
                console.log(taxRate)
                $('.position-relative .percent-icon').removeClass('d-none');
                if (taxRate == '' || taxRate == 0) {
                    $('.position-relative .percent-icon').addClass('d-none');
                }
            });


            function getAmenities(search = null) {
                $.ajax({
                    url: "{{ route('cms_amenities') }}",
                    type: "GET",
                    data: {search},
                    success: function(response) {
                        if (response.status == true) {
                            $("#amenities-table tbody").html(response.data);
                        }
                    }
                })
            }
            getAmenities()
            //  Amenity Search
            let debounceTimeout;
            $(document).on("keyup", "#amenity-search-input", function(event) {
                event.preventDefault();
                let search = $(this).val();
                clearTimeout(debounceTimeout);
                debounceTimeout = setTimeout(function() {
                    getAmenities(search);
                }, 300);
            });
        });
    </script>


    <script>
        // function preview_logo() {
        //     var total_file = document.getElementById("logo").files.length;
        //     // for (var i = 0; i < total_file; i++) {
        //     // $('#image_preview').append("<div class='col-md-3'><img style='width:100%' class='img-responsive' src='"+URL.createObjectURL(event.target.files[i])+"'></div>");
        //     $('#image_preview .actual_preview').html(
        //         '<div class="col-md-3 preview"><div class="close">x</div><div style=" padding-block: 10px;width: 100%; height: 150px; overflow: hidden; "><img style="width:100%;object-fit: contain;height: 100%;object-position: top;" class="img-responsive img-fluid" src="' +
        //         URL.createObjectURL(document.getElementById("logo").files[0]) + '"></div></div>');
        //     // }
        // }

        // function preview_favicon() {
        //     var total_file = document.getElementById("favicon").files.length;
        //     for (var i = 0; i < total_file; i++) {
        //         // $('#image_preview').append("<div class='col-md-3'><img style='width:100%' class='img-responsive' src='"+URL.createObjectURL(event.target.files[i])+"'></div>");
        //         $('#fav_preview .actual_preview').html(
        //             '<div class="col-md-3 preview"><div class="close">x</div><div style=" padding-block: 10px;width: 100%; height: 150px; overflow: hidden; "><img style="width:100%;object-fit: contain;height: 100%;object-position: top;" class="img-responsive img-fluid" src="' +
        //             URL.createObjectURL(document.getElementById("favicon").files[0]) + '"></div></div>');
        //     }
        // }

        // function preview_banner() {
        //     var total_file = document.getElementById("banner_image").files.length;
        //     for (var i = 0; i < total_file; i++) {
        //         // $('#image_preview').append("<div class='col-md-3'><img style='width:100%' class='img-responsive' src='"+URL.createObjectURL(event.target.files[i])+"'></div>");
        //         $('#banner_preview .actual_preview').html(
        //             '<div class="col-md-3 preview"><div class="close">x</div><div style=" padding-block: 10px;width: 100%; height: 150px; overflow: hidden; "><img style="width:100%;object-fit: contain;height: 100%;object-position: top;" class="img-responsive img-fluid" src="' +
        //             URL.createObjectURL(document.getElementById("banner_image").files[0]) + '"></div></div>');
        //     }
        // }


        $(document).on('click', ".close", function() {
            $(this).closest('.preview').remove();
        });
        $(document).ready(function() {
            $('input, textarea').prop('disabled', true);
            $('input.btn, .modal input, .not-disabled, .editor, #faq_pane input, #faq_pane textarea, .searchBar')
                .prop(
                    'disabled', false);

            $(document).on('click', '.fa-edit', function() {
                var parent = $(this).closest('div');
                var inputs = parent.find('input, textarea');

                inputs.removeAttr('disabled');
            });

            $(document).on('click', '.cancel_btn', function() {
                window.location.reload();
            });

        });
        $(document).ready(function() {
            $('[name="_token"],[name="_method"], .about-content-id, .not-disabled').prop('disabled', false);

            // function tab(active) {
            //     $('.tab-pane').hide(650);
            //     if (active == "home") {
            //         $('#home').show(300);
            //     } else if (active == "about") {
            //         $('#about').show(300);
            //     } else if (active == "contact") {
            //         $('#contact').show(300);
            //     } else if (active == "amen") {
            //         $('#amen').show(300);
            //     } else if (active == "ctg") {
            //         $('#ctg').show(300);
            //     } else if (active == "faq") {
            //         $('#faq').show(300);
            //     }
            // }
        });
        $(".multi_select").select2({
            closeOnSelect: false,
            placeholder: "Select categories",
            // allowHtml: true,
            allowClear: true,
            tags: true,
            multiple: true
        });
        $('.multi_select').val(null).trigger('change');

        $(document).on("click", ".amenity-modal", function() {
            let amenity_id = $(this).attr("data-amenity-id");
            let amenity_title, amenity_url, amenity_btn_title;
            if (amenity_id) {
                amenity_title = "Edit Amenity";
                $.ajax({
                    url: `{{ url('/') }}/amenities/amenities/${amenity_id}/edit`,
                    type: "GET",
                    success: function(response) {
                        if (response.status == true) {
                            $("#amenity_name").val(response.data.name)
                            $("#amenCtg").val(JSON.parse(response.data.categories)).trigger("change");
                        }
                    }
                })
                amenity_url = `{{ route('amenities.update', '') }}/${amenity_id}`;
                amenity_btn_title = "Update Amenity";
                $('#amenity_form input[name="_method"]').prop('disabled', false);

            } else {
                amenity_title = "Add Amenity";
                amenity_url = "{{ route('amenities.store') }}",
                    amenity_btn_title = "Create Amenity";
                $("#amenity_name").val("")
                $('.multi_select').val(null).trigger('change');
                $('#amenity_form input[name="_method"]').prop('disabled', true);
            }
            $("#amenity_title").html(amenity_title);
            $("#amenity_btn_title").html(amenity_btn_title);
            $("#amenity_form").attr("action", amenity_url);
        })
        // Initial row index based on the count of existing FAQs
        var rowIdx = $('.inputRow').length

        // Add more FAQs
        $('#addRow').on('click', function() {
            $('#inputFormRow').append(`
        <div class="inputRow">
            <div class="content-between">
                <h1>FAQ #${rowIdx + 1}</h1>
                <button type="button" class="btn btn-danger removeRow">Remove</button>
            </div>
            <div class="form_field_padding">
                <label>Title</label>
                <input class="form-control" type="text" name="items[${rowIdx}][name]" placeholder="Enter item name"/>
            </div>
            <div class="form_field_padding">
                <label>Description</label>
                <textarea class="form-control editor" name="items[${rowIdx}][description]" placeholder="Enter description"></textarea>
            </div>
        </div>`);
            // initializeCKEditor()
            rowIdx++;
        });
        // initializeCKEditor();

        // $(document).on('click','.cms_btn .custom_btns .nav-pills li a',function(){
        //     initializeCKEditor();
        // })

        // function initializeCKEditor() {
        //     $('.editor').each(function() {
        //         const textareaId = $(this).attr('name'); // Use the 'name' attribute for dynamic initialization

        //         // Only initialize CKEditor if it is not already initialized
        //         // if (!CKEDITOR.instances[textareaId]) {
        //         var url = "{{ route('faq_image_upload', ['_token' => csrf_token()]) }}";
        //         CKEDITOR.replace(this, {
        //             allowedContent: true,
        //             extraAllowedContent: 'p h1 h2 h3 h4 h5 h6 strong em; a[!href]; ul ol li; img[!src,alt,width,height]',
        //             disallowedContent: 'script; *[on*]',
        //             removePlugins: 'paste,sourcearea,scayt,templates,about,forms,table,tabletools,tableselection,iframe,div,language',
        //             removeButtons: 'ExportPdf,NewPage,Save',
        //             filebrowserUploadUrl: url, // Your image upload URL
        //             filebrowserUploadMethod: 'form', // Upload method
        //             filebrowserUploadParams: {
        //                 type: 'image' // Ensures only image types are allowed in file dialog
        //             }

        //         });
        //         // }
        //     });
        // }

        // Remove an FAQ row and reindex the remaining rows
        $(document).on('click', '.removeRow', function() {
            var textareaName = $(this).closest('.inputRow').find('textarea').attr('name');

            // Check if CKEditor instance exists for the textarea and destroy it
            if (CKEDITOR.instances[textareaName]) {
                CKEDITOR.instances[textareaName].destroy(true); // Destroy the CKEditor instance
            }

            // Remove the row from the DOM
            // $(this).closest('.inputRow').remove();

            $(this).closest('.inputRow').remove();
            reindexRows();
        });

        // Function to reindex FAQs
        function reindexRows() {
            $('.inputRow').each(function(index) {
                // Update the FAQ header number
                $(this).find('h1').text('FAQ #' + (index + 1));

                // Update the name attributes for inputs and textareas
                $(this).find('input, textarea').each(function() {
                    var name = $(this).attr('name');
                    name = name.replace(/items\[\d+\]/, 'items[' + index + ']');
                    $(this).attr('name', name);
                });
            });

            // Update rowIdx to reflect the current count of rows for next addition
            // initializeCKEditor()
            rowIdx = $('.inputRow').length;
        }
        setInterval(() => {
            $("#inputFormRow input, #inputFormRow textarea").removeAttr("disabled");
        }, 100);
        // add more and remove end



        $(document).ready(function() {
            function showTabByHash() {
                var currentHash = window.location.hash || '#home_pane';
                var $targetTab = $('.nav-pills a[href="' + currentHash + '"]');
                if ($targetTab.length > 0) {
                    $('.nav-pills li').removeClass('active');
                    $('.tab-pane').removeClass('active show in');
                    $targetTab.closest('li').addClass('active');
                    $(currentHash).addClass('active show in');
                    sessionStorage.setItem('activeTab', currentHash);
                    setTimeout(function() {
                        sessionStorage.removeItem('activeTab');
                    }, 3000);
                }
                $('html, body').animate({
                    scrollTop: 130
                }, 0);
            }
            var activeTab = sessionStorage.getItem('activeTab');
            if (activeTab) {
                window.location.hash = activeTab;
                showTabByHash();
            } else {
                showTabByHash();
            }
            $(window).on('hashchange', showTabByHash);
            $('.nav-pills a').on('click', function(e) {
                e.preventDefault();
                window.location.hash = this.hash;
                showTabByHash();
            });
            $('form').on('submit', function(e) {
                var currentHash = window.location.hash || '#home_pane';
                window.location.hash = currentHash;
                showTabByHash();
            });

            $(".delete-btn").click(function() {
                var amenityId = $(this).data("id");
                Swal.fire({
                    title: "Are you sure?",
                    text: "This action cannot be revert!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#d33",
                    cancelButtonColor: "#3085d6",
                    confirmButtonText: "Yes, delete it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        $("#delete-form-" + amenityId).submit();
                    }
                });
            });
        });
    </script>
    <script>
        $(function() {
            // Basic
            $('.dropify').dropify();
            // Translated
            $('.dropify-fr').dropify({
                messages: {
                    default: 'Glissez-déposez un fichier ici ou cliquez',
                    replace: 'Glissez-déposez un fichier ou cliquez pour remplacer',
                    remove: 'Supprimer',
                    error: 'Désolé, le fichier trop volumineux'
                }
            });
            // Used events
            var drEvent = $('#input-file-events').dropify();
            drEvent.on('dropify.beforeClear', function(event, element) {
                return confirm("Do you really want to delete \"" + element.file.name + "\" ?");
            });
            drEvent.on('dropify.afterClear', function(event, element) {
                alert('File deleted');
            });
            drEvent.on('dropify.errors', function(event, element) {
                console.log('Has Errors');
            });
            var drDestroy = $('#input-file-to-destroy').dropify();
            drDestroy = drDestroy.data('dropify')
            $('#toggleDropify').on('click', function(e) {
                e.preventDefault();
                if (drDestroy.isDropified()) {
                    drDestroy.destroy();
                } else {
                    drDestroy.init();
                }
            })
        });
    </script>


    <script type="text/javascript" src="{{ asset('website/assets_cdn/jodit_editor/jodit.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('website/assets_cdn/jodit_editor/plugins/speech-recognize/speech-recognize.min.js') }}"></script>

    <script>

        function initializeJoditEditor(element) {

            const anchorLinks = {};
        
            const editor = new Jodit(element, {
                controls: {
                    anchorManager: {
                        iconURL: `{{ asset('website/images/bookmark.svg') }}`
                    }
                },
                events: {
                    afterInit: function (editor) {
                        const style = editor.createInside.element('style');
                        style.textContent = `
                            .jodit-anchor {
                                background-color: rgba(173, 216, 230, 0.3);
                                border-bottom: 1px dashed #4a90e2;
                                padding: 0 2px;
                            }
                            .jodit-anchor:hover::after {
                                content: ' #' attr(id);
                                color: #666;
                                font-size: 0.8em;
                            }
                        `;
                        editor.editorDocument.head.appendChild(style);
                    }
                },
                uploader: {
                    insertImageAsBase64URI: false,
                    url: `{{ url('help-center-image') }}`,
                    format: 'json',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    prepareData: function (data) {
                        return data;
                    },
                    isSuccess: function (resp) {
                        return resp.success;
                    },
                    getMsg: function (resp) {
                        return resp.error || 'Unknown error';
                    },
                    process: function (resp) {
                        return {
                            files: [resp.url],
                            path: resp.url,
                            baseurl: resp.baseurl,
                            error: resp.error || null,
                            message: resp.message || null
                        };
                    },
                    defaultHandlerSuccess: function (data) {
                        if (data.files && data.files.length) {
                            this.selection.insertImage(data.files[0]);
                        }
                    },
                    error: function (e) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Upload Failed',
                            text: 'Error uploading image: ' + e.message,
                            confirmButtonText: 'OK'
                        });
                    }
                },
                toolbar: [
                    'bold', 'italic', 'underline', 'strikethrough', '|',
                    'font', 'fontsize', 'brush', 'paragraph', '|',
                    'align', 'outdent', 'indent', '|',
                    'insertOrderedList', 'insertUnorderedList', '|',
                    'link', 'unlink', 'image', 'video', 'table', '|',
                    'undo', 'redo', '|',
                    'cut', 'copy', 'paste', 'selectall', '|',
                    'subscript', 'superscript', '|',
                    'hr', 'fontcolor', 'backcolor', '|',
                    'left', 'center', 'right', 'justify', '|',
                    'fullscreen', 'source', 'print', '|',
                    'anchor', 'paragraphFormat', 'blockquote', 'code', 'emoticon', 'indent', 'outdent', '|',
                    '|', 'ul', 'ol',
                    'customLink', 'anchorManager'
                ],
                extraButtons: [
                    {
                        name: 'customLink',
                        icon: 'link',
                        tooltip: 'Insert Link',
                        group: 'insert',
                        exec: (editor) => {
                            const domSelection = window.getSelection();
                            const domRange = domSelection.rangeCount > 0 ? domSelection.getRangeAt(0) : null;
                            const selectedText = domSelection.toString();
                            console.log('Initial Selection:', selectedText, 'Range Start:', domRange?.startContainer);

                            const popup = document.createElement('div');
                            popup.classList.add('custom_link_popup');
                            popup.style.padding = '10px';

                            const typeSelect = document.createElement('select');
                            typeSelect.innerHTML = `
                                <option value="external">External Link</option>
                                <option value="internal">Internal Link</option>
                            `;

                            const urlInput = document.createElement('input');
                            urlInput.type = 'text';
                            urlInput.placeholder = 'Enter URL or anchor';
                            urlInput.style.marginTop = '5px';

                            const btn = document.createElement('button');
                            btn.innerText = 'Insert Link';
                            btn.style.marginTop = '10px';

                            btn.addEventListener('mousedown', (event) => {
                                event.preventDefault();
                                event.stopPropagation();

                                const contentEditable = editor.editor.querySelector('[contenteditable="true"]');
                                if (contentEditable) {
                                    contentEditable.focus();
                                }

                                let href = urlInput.value.trim();
                                if (!href) {
                                    alert('Please enter a valid URL or anchor');
                                    return;
                                }
                                if (typeSelect.value === 'internal') {
                                    href = '#' + href.replace(/^#*/, '');
                                }

                                const textToUse = selectedText || href; // Use highlighted text, fallback to href
                                console.log('Inserting Link:', href, 'Text:', textToUse, 'Range:', domRange);

                                setTimeout(() => {
                                    if (domRange) {
                                        const newSelection = window.getSelection();
                                        newSelection.removeAllRanges();
                                        newSelection.addRange(domRange);
                                    }

                                    const existing = editor.s.sel?.focusNode?.parentElement?.closest('a[href]');
                                    if (existing) {
                                        const parent = existing.parentNode;
                                        const textContent = existing.textContent;
                                        const textNode = document.createTextNode(textContent);
                                        parent.replaceChild(textNode, existing);
                                    }

                                    const a = document.createElement('a');
                                    a.href = href;
                                    a.textContent = textToUse;
                                    a.classList.add(typeSelect.value === 'external' ? 'jodit-link' : 'jodit-anchor');

                                    if (domRange && selectedText) {
                                        try {
                                            domRange.deleteContents();
                                            domRange.insertNode(a);
                                            console.log('Link inserted at range:', a.parentNode);
                                        } catch (e) {
                                            console.error('DOM insertNode failed:', e);
                                            contentEditable.appendChild(a); // Fallback
                                        }
                                    } else {
                                        const newRange = document.createRange();
                                        const selection = window.getSelection();
                                        if (selection.rangeCount) {
                                            newRange.setStart(selection.focusNode, selection.focusOffset);
                                            newRange.insertNode(a);
                                        } else {
                                            contentEditable.appendChild(a);
                                        }
                                    }

                                    const newRange = document.createRange();
                                    newRange.setStartAfter(a);
                                    const selection = window.getSelection();
                                    selection.removeAllRanges();
                                    selection.addRange(newRange);

                                    editor.synchronizeValues();

                                    setTimeout(() => {
                                        const newLink = editor.editor.querySelector(`a[href="${href}"]`);
                                        if (newLink) {
                                            newLink.scrollIntoView({ behavior: 'smooth' });
                                        } else {
                                            console.error('Link not found:', href);
                                        }
                                    }, 100);

                                    popup.remove();
                                }, 100);
                            });

                            popup.appendChild(typeSelect);
                            popup.appendChild(document.createElement('br'));
                            popup.appendChild(urlInput);
                            popup.appendChild(document.createElement('br'));
                            popup.appendChild(btn);

                            const button = editor.toolbar.buttons.find(button => button.name === 'customLink');
                            const buttonRect = button.container.getBoundingClientRect();
                            const popupPosition = {
                                top: buttonRect.bottom + window.scrollY + 5,
                                left: buttonRect.left + window.scrollX
                            };

                            popup.style.position = 'absolute';
                            popup.style.top = `${popupPosition.top}px`;
                            popup.style.left = `${popupPosition.left}px`;
                            popup.style.background = '#fff';
                            popup.style.border = '1px solid #ccc';
                            popup.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
                            document.body.appendChild(popup);

                            const spaceRight = window.innerWidth - popupPosition.left - popup.offsetWidth;
                            if (spaceRight < 20) {
                                popupPosition.left = Math.max(buttonRect.left - popup.offsetWidth, 0);
                                popup.style.left = `${popupPosition.left}px`;
                            }

                            document.addEventListener('click', function closePopup(event) {
                                if (!popup.contains(event.target) && !button.container.contains(event.target)) {
                                    popup.remove();
                                    document.removeEventListener('click', closePopup);
                                }
                            });
                        }
                    },
                    {
                        name: 'anchorManager',
                        icon: 'bookmark',
                        tooltip: 'Manage Anchors',
                        group: 'insert',
                        exec: (editor) => {
                            const domSelection = window.getSelection();
                            const domRange = domSelection.rangeCount > 0 ? domSelection.getRangeAt(0) : null;
                            const selectedText = domSelection.toString();
                            console.log('Initial Selection:', selectedText, 'Range Start:', domRange?.startContainer);

                            const popup = document.createElement('div');
                            popup.classList.add('custom_anchor_popup');
                            popup.style.padding = '10px';

                            const anchors = getAnchorsWithUsageStatus(editor);

                            Object.keys(anchors).forEach(link => {
                                const item = document.createElement('div');
                                item.innerText = link;
                                item.style.color = anchors[link] ? 'gray' : 'black';
                                item.style.cursor = 'pointer';
                                item.style.margin = '5px 0';

                                item.addEventListener('mousedown', (event) => {
                                    event.preventDefault(); // Prevent focus shift
                                    event.stopPropagation();

                                    const contentEditable = editor.editor.querySelector('[contenteditable="true"]');
                                    if (contentEditable) {
                                        contentEditable.focus();
                                    }

                                    const id = link.slice(1);
                                    const textToUse = selectedText || id;

                                    console.log('Inserting Anchor:', id, 'Text:', textToUse, 'Range:', domRange);

                                    setTimeout(() => {
                                        if (domRange) {
                                            const newSelection = window.getSelection();
                                            newSelection.removeAllRanges();
                                            newSelection.addRange(domRange);
                                        }

                                        const existing = editor.editor.querySelector(`[id="${id}"]`);
                                        if (existing) {
                                            const parent = existing.parentNode;
                                            const textContent = existing.textContent;
                                            const textNode = document.createTextNode(textContent);
                                            parent.replaceChild(textNode, existing);
                                        }

                                        const anchorEl = document.createElement('a');
                                        anchorEl.id = id;
                                        anchorEl.textContent = textToUse;
                                        anchorEl.classList.add('jodit-anchor');

                                        if (domRange && selectedText) {
                                            try {
                                                domRange.deleteContents();
                                                domRange.insertNode(anchorEl);
                                                console.log('Anchor inserted at range:', anchorEl.parentNode);
                                            } catch (e) {
                                                console.error('DOM insertNode failed:', e);
                                                contentEditable.appendChild(anchorEl); // Fallback
                                            }
                                        } else {
                                            const newRange = document.createRange();
                                            const selection = window.getSelection();
                                            if (selection.rangeCount) {
                                                newRange.setStart(selection.focusNode, selection.focusOffset);
                                                newRange.insertNode(anchorEl);
                                            } else {
                                                contentEditable.appendChild(anchorEl);
                                            }
                                        }

                                        const newRange = document.createRange();
                                        newRange.setStartAfter(anchorEl);
                                        const selection = window.getSelection();
                                        selection.removeAllRanges();
                                        selection.addRange(newRange);

                                        editor.synchronizeValues();

                                        setTimeout(() => {
                                            const newAnchor = editor.editor.querySelector(`[id="${id}"]`);
                                            if (newAnchor) {
                                                newAnchor.scrollIntoView({ behavior: 'smooth' });
                                            } else {
                                                console.error('Anchor not found:', id);
                                            }
                                        }, 100);

                                        popup.remove();
                                    }, 100);
                                });

                                popup.appendChild(item);
                            });

                            const button = editor.toolbar.buttons.find(button => button.name === 'anchorManager');
                            const buttonRect = button.container.getBoundingClientRect();
                            const popupPosition = {
                                top: buttonRect.bottom + window.scrollY + 5,
                                left: buttonRect.left + window.scrollX
                            };

                            popup.style.position = 'absolute';
                            popup.style.top = `${popupPosition.top}px`;
                            popup.style.left = `${popupPosition.left}px`;
                            popup.style.background = '#fff';
                            popup.style.border = '1px solid #ccc';
                            popup.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
                            document.body.appendChild(popup);

                            const spaceRight = window.innerWidth - popupPosition.left - popup.offsetWidth;
                            if (spaceRight < 20) {
                                popupPosition.left = Math.max(buttonRect.left - popup.offsetWidth, 0);
                                popup.style.left = `${popupPosition.left}px`;
                            }

                            document.addEventListener('click', function closePopup(event) {
                                if (!popup.contains(event.target) && !button.container.contains(event.target)) {
                                    popup.remove();
                                    document.removeEventListener('click', closePopup);
                                }
                            });
                        }
                    },
                    {
                        name: 'fileUpload',
                        icon: 'file',
                        tooltip: 'Upload File',
                        group: 'insert',
                        exec: (editor) => {
                            const input = document.createElement('input');
                            input.type = 'file';
                            input.accept = '*/*';
                            input.style.display = 'none';
                            document.body.appendChild(input);

                            input.addEventListener('change', (event) => {
                                const file = event.target.files[0];
                                if (!file) return;

                                const formData = new FormData();
                                formData.append('file', file);

                                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
                                if (!csrfToken) {
                                    console.error('CSRF token not found');
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Upload Failed',
                                        text: 'CSRF token not found',
                                        confirmButtonText: 'OK'
                                    });
                                    document.body.removeChild(input);
                                    return;
                                }

                                fetch(`{{ url('help-center-image') }}`, {
                                    method: 'POST',
                                    headers: {
                                        'X-CSRF-TOKEN': csrfToken,
                                        'Accept': 'application/json'
                                    },
                                    body: formData
                                })
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`HTTP error! Status: ${response.status}`);
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    if (data.success) {
                                        const a = document.createElement('a');
                                        a.href = data.url;
                                        a.textContent = data.filename || file.name || 'Download File';
                                        a.classList.add('jodit-file-link');
                                        a.setAttribute('download', '');
                                        editor.selection.insertNode(a);

                                        const newRange = document.createRange();
                                        newRange.setStartAfter(a);
                                        const selection = window.getSelection();
                                        selection.removeAllRanges();
                                        selection.addRange(newRange);

                                        editor.synchronizeValues();
                                    } else {
                                        throw new Error(data.error || 'File upload failed');
                                    }
                                })
                                .catch(err => {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Upload Failed',
                                        text: 'Error uploading file: ' + err.message,
                                        confirmButtonText: 'OK'
                                    });
                                })
                                .finally(() => {
                                    document.body.removeChild(input);
                                });
                            });

                            input.click();
                        }
                    },
                ]
            });

            if (!editor.data) editor.data = {};
            editor.data.previousImages = [];

            editor.events.on('afterRemoveNode', function (node) {
                try {
                    console.log('afterRemoveNode triggered, node:', node.nodeName);
                    if (node.nodeName === 'IMG') {
                        const deletedImageUrl = node.src;
                        console.log('Image removed:', deletedImageUrl);

                        // Get the CSRF token
                        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
                        if (!csrfToken) {
                            console.error('CSRF token not found');
                            return;
                        }

                        // Send the fetch request to delete the image
                        console.log('Attempting to delete image:', deletedImageUrl);
                        const deleteUrl = `{{ url('help-center-image/delete') }}`;
                        console.log('Delete URL:', deleteUrl);

                        fetch(deleteUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({ image_url: deletedImageUrl })
                        })
                        .then(response => {
                            console.log('Fetch response status:', response.status);
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Delete Response:', data);
                            if (!data.success) {
                                console.error('Server error:', data.error);
                            }
                        })
                        .catch(error => {
                            console.error('Error deleting image:', error);
                        });
                    } else if (node.nodeName === 'A' && node.classList.contains('jodit-file-link')) {
                        const deletedFileUrl = node.href;
                        console.log('File link removed:', deletedFileUrl);

                        fetch(deleteUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': csrfToken,
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({ file_url: deletedFileUrl })
                        })
                        .then(response => {
                            console.log('File delete response status:', response.status);
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('File delete response:', data);
                            if (!data.success) {
                                console.error('Server error:', data.error);
                            }
                        })
                        .catch(error => {
                            console.error('Error deleting file:', error);
                        });
                    }
                } catch (error) {
                    console.error('Error in afterRemoveNode event:', error);
                }
            });

            function getInternalAnchorLinks(editor) {
                const anchors = {};
                editor.editor.querySelectorAll('a[href^="#"]').forEach(a => {
                    const href = a.getAttribute('href');
                    if (href && href.startsWith('#')) {
                        anchors[href] = false;
                    }
                });
                return anchors;
            }

            function getAnchorsWithUsageStatus(editor) {
                const anchors = getInternalAnchorLinks(editor);

                Object.keys(anchors).forEach(href => {
                    const id = href.slice(1);
                    const existing = editor.editor.querySelector(`[id="${id}"]`);
                    anchors[href] = !!existing;
                });

                return anchors;
            }

        }

        document.querySelectorAll('.jodit_editor').forEach(element => {
            initializeJoditEditor(element);
        });

    </script>

@endpush
