@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "type");
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="inner_section_col">
            <div class="main_step_title">
                <h2>{{ $step_data->title ?? "" }}</h2>
            </div>
            @isset($step_data->sub_title)
                <p>
                    {{ $step_data->sub_title }}
                </p>
            @endisset
            <div class="categories_search_field">
                <div class="txt_field">
                    <input class="form-control no_validate categories_search" type="text" name="categories_search"
                        id="" placeholder="{{ translate('stepper.search_categories') }}">
                    <i class="fa fa-search" aria-hidden="true"></i>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="inner_section_categories_main_col listing_types_main_wrapper scrollable-section">
            <div class="row">
                {{-- Loop to be insert here --}}
                @foreach ($category->listing_types->sortBy('name') as $key => $type)
                    <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 single_category_col single_type_col">
                        <div class="inner_section_single_category">
                            <input type="radio" name="type_id" id="category_{{ $type->id }}"
                                value="{{ $type->id }}" class="no_validate"
                                {{ ($listing->type_id ?? '') == $type->id ? 'checked' : '' }}>
                            <label for="category_{{ $type->id }}">
                                <div class="category_icon_wrapper">
                                    <img src="{{ asset('website') . '/' . $type->image }}" alt=""
                                        onerror="this.onerror=null;this.src=`{{ asset('website/images/home_icon.png') }}`;">
                                </div>
                                <div class="category_title">
                                    <h5>{{ $type->name }}</h5>
                                </div>
                            </label>
                        </div>
                    </div>
                @endforeach
                {{-- Loop ends here --}}
            </div>
        </div>
    </div>
</div>



@push('js')
    <script>
        // $(document).on('keyup', '.categories_search', function() {
        //     let input = $(this).val().toLowerCase();

        //     $('.single_type_col').each(function() {
        //         if ($(this).find('.category_title h5').text().toLowerCase().includes(input)) {
        //             $(this).css('display', 'block');
        //         } else {
        //             $(this).css('display', 'none');
        //         }
        //     });
        // });
        $(document).on('keyup', '.categories_search', function() {
            let input = $(this).val().toLowerCase();

            $('.single_type_col').each(function() {
                if (input === '') {
                    $(this).css('display', 'block'); // Show all if input is empty
                } else if ($(this).find('.category_title h5').text().toLowerCase().includes(input)) {
                    $(this).css('display', 'block');
                } else {
                    $(this).css('display', 'none');
                }
            });
        });

        $(document).ready(function() {
            $('.single_type_col').css('display', 'block');
            function listingTypeValidation() {
                var checkedOptionsCount = 0;
                var test = 0;
                $('.select_categories_step .listing_types_main_wrapper .single_type_col').each(function() {
                    if ($(this).find('input[type="radio"]').is(':checked')) {
                        checkedOptionsCount++;
                    }
                });
                if (checkedOptionsCount == 0) {
                    $('.select_categories_step:has(.listing_types_main_wrapper) .next').prop('disabled', true);
                } else {
                    $('.select_categories_step:has(.listing_types_main_wrapper) .next').prop('disabled', false);
                }
            }

            listingTypeValidation();

            $(document).on('change',
                '.select_categories_step .listing_types_main_wrapper .single_type_col input[type="radio"]',
                function() {
                    listingTypeValidation();
                });

        });
    </script>
@endpush
