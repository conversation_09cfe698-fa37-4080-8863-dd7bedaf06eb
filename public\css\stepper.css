* {  box-sizing: border-box; margin: 0;  padding: 0;  border: 0;}

body{ background-color: #f8f9fa;}

:root {
    --primary-color: #FFCE32;
    --secondary-color: #000;
    --text-color-light-grey: #A0AEC0;
    --text-color-dark-grey: #9B9B9B;
    --text-grey: #949494;
    --head-text-color:#4A4A4A;
    --dark-text-color: #2D3748;
    --white-color: #fff;
    --black_color:#000;
    --light-yellow: #FCDF73;
    --lime-yellow: #FFD324;
    --comment-grey: #E9E9E9;
    --black: #000;
    --border-grey: #D9D9D9;
    --grey: #E6E6E6;
    --light-grey: #4A4A4A;
    --blue: #0085FF;
}

/* ----------Scrollbar------------------*/
::-webkit-scrollbar {  width: 10px;  background-color: transparent;  }
::-webkit-scrollbar-track {  background-color: transparent;  }
::-webkit-scrollbar-thumb {  background-color: #FCDF73;  border-radius: 20px;  }
::-webkit-scrollbar-thumb:hover {  background-color: #FFCE32;  }

/* Font Sizes */

/* h1 { font-size: 20px; }
h2 { font-size: 18px; }
h3 { font-size: 14px; }
h4 { font-size: 12px; }
h5 { font-size: 10px; } */

.fs-24 { font-size: 24px;}
.fs-20 { font-size: 20px;}
.fs-16 { font-size: 16px;}
.fs-14 { font-size: 14px;}
.fs-12 { font-size: 12px;}
.fs-10 { font-size: 10px;}

/* Font Weight */
.bold { font-weight: 700;}
.semi-bold, .semi_bold { font-weight: 600;}
.light_bold, .light-bold { font-weight: 500;}
.normal { font-weight: 400;}


/* Colors */
.yellow{ background: var(--primary-color); color: #fff;}
.colorWhite { color: var(--white-color);}
.dark-yellow { color: var(--primary-color)}
.red, .error { color: red;}

/* Custom Classes */
.box-shadow { box-shadow: 0 8.5685949325562px 22.492561340332px 0 #00000012; }
.b-radius { border-radius: 12px;}
.white-box { border-radius: 20px; border: none;}
.border-grey { border: 1px solid #9b9b9b;}

/* Backdrop */
body:has(.custom_modal.bg_white_blur.show) .modal-backdrop.show {background-color: #D9D9D9; }
.custom_modal.bg_white_blur.modal {backdrop-filter: blur(5px);}

/* Input Css */
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {  -webkit-appearance: none;  margin: 0;  }
a { text-decoration: none;}

 /* Loader */
 #sec-loader .load { position: fixed; background-color: #fff; z-index: 999999; display: flex; align-items: center; justify-content: center; top: 0; left: 0; height: 100%; width: 100%; margin: auto;}
 #sec-loader .load img { height: 120px;width: 120px;object-fit: contain;}

/* Old Stepper Css */

/* Global Styling */
.listing_stepper :is(h1, h2, h3, h4, h5, h6, p, input, label, span, a, button, li) { font-family: 'Poppins'; color: #4A4A4A; font-style: normal; line-height: normal; }

/* Button */
.button, .button1 { padding: 12px 20px; border-radius: 70px; border: 1px solid var(--light-grey); color: var(--light-grey);  text-decoration: none; font-size: 15px; font-weight: 400;  background-color: var(--white); position: relative; overflow: hidden; display: inline-block;}
.button:not(.transparent):hover, .button1:hover { box-shadow: 0 0 4px -1px var(--light-grey);}
a.button:hover, button.button:not(.transparent):hover:not(.nav-button .button:hover, .modal_btn:hover)  { color: var(--black); background-color: transparent; border: 1px solid var(--primary-color); transition: 0.55s; }

.btn_yellow { background-color: var(--primary-color); border-radius: 12px; border-color: var(--primary-color);}

/* Header */
.listing_stepper .save-header .save-btn { font-size: 13px;font-weight: 600; line-height: 0; border-radius: 33px; background: var(--primary-color); padding: 20px 30px; }
.listing_stepper .save-header{background: #171717;padding: 10px; justify-content: space-between; align-items: center; padding-inline: 35px; position: fixed; top: 0; width: 100%; z-index: 10;}
.listing_stepper .save-header img { height: auto; width: 160px; object-fit: contain; object-position: left;}
.listing_stepper .save-header img.favicon { display: none;}


/* Heading */
.listing_stepper .stepper h4 { font-size: 25px; font-weight: 500;  }
.listing_stepper .stepper h2 { font-size: 32px; font-weight: 600; line-height: 40px;}
.listing_stepper .stepper p:not(.error p), .listing_stepper .stepper p strong:not(.error p) { font-size: 16px; font-weight: 400; color: var(--text-grey); line-height: 27px; }

/* Form Parent */
.listing_stepper .stepper { text-align: center; position: relative; height: 93vh; overflow: hidden; overflow-y: auto; padding-bottom: 60px; /* <- Was 36px before */ padding-top: 5em;}

/* Inputs and textarea */
.listing_stepper .stepper label { font-size: 14px; font-weight: 500; padding-left: 5px;}
.listing_stepper .stepper label:first-letter { text-transform: capitalize;}
.listing_stepper input, .listing_stepper textarea, .listing_stepper select{ font-weight: 600; border-radius: 12px; padding-block: 20px; margin: 10px 0; font-size: 13px; line-height: 15px; color: #000;}
.listing_stepper input::placeholder, .listing_stepper textarea::placeholder { color: #A0AEC0; font-weight:300;}
.listing_stepper select { padding-block: 0;}

/* Button Next and Back */
.listing_stepper .action-button, .listing_stepper .action-button-previous { position: fixed; bottom: 8px; padding: 15px 40px; background-color: transparent; margin: 0; border-radius: 33px; color: #fff; border: 1px solid #000; }
.listing_stepper .action-button-previous { color: #000; left: 37px; }
.listing_stepper .action-button { background-color: #000; right: 37px; }

/* Category Page */
.listing_stepper .ctg_box_wrapper { gap: 25px; }
.listing_stepper .ctg_box_wrapper .ctg_box { min-width: 46%; }
.listing_stepper .ctg_box_wrapper .ctg_box:nth-child(odd) { text-align: end;}
.listing_stepper .ctg_box_wrapper .ctg_box:nth-child(even) { text-align: start;}
.listing_stepper .ctg_box_wrapper .ctg_box .single_ctg { color: var(--text-color-light-grey); padding: 25px; min-width: 47%; border: 2px solid transparent; border-radius: 90px; cursor: pointer; transition: 0.3s ease-in; }
.listing_stepper .ctg_box_wrapper .ctg_box .single_ctg img { height: 45px; object-fit: contain; /* filter: grayscale(0.5); */}
.listing_stepper .ctg_box_wrapper .ctg_box .single_ctg:hover, .listing_stepper .ctg_box_wrapper .ctg_box .single_ctg:focus { border-color: var(--primary-color); background: var(--primary-color); transition: 0.3s ease-in; box-shadow: 0 8.5685949325562px 22.492561340332px 0 #e0c24a12; color: #000;}




/* Amenities */

/* .listing_stepper .inner-box1 .standout_ameneties a { font-size: 18px; color: #4A4A4A; text-decoration: underline;} */
.add_custom_amenities .options_wrapper2 .add_btn_wrapper2, .add_custom_activity .activity_wrapper .options_txt_field_activity { position: relative;}
.add_custom_amenities .options_wrapper2 .add_btn_wrapper2 a, .add_custom_activity .activity_wrapper .options_txt_field_activity .add_btn_activity { color: #4A4A4A; font-family: Poppins; font-size: 12px; font-style: normal; font-weight: 500; line-height: normal; text-transform: capitalize; border-radius: 33px; background: var(--primary-color); position: absolute; right: 5px; top: 5px; padding: 5px 15px; }


/* .add_custom_amenities .options_wrapper2 .selected_options_wrapper { display: flex; align-items: center; justify-content: flex-start; gap: 10px;} */
/* .add_custom_amenities .options_wrapper2 .single_option { margin-bottom: 10px;} */
/*.add_custom_amenities .options_wrapper2 { text-align: left;} */

.amenity-modal .check-box .amen-box2 .wrapper input { width: 15px; height: 15px; padding: 0; opacity: 1; background-color: #fff; border: 1px solid #4D4D4D;}
.amenity-modal .check-box .amen-box2 .wrapper input:checked { background-color: #4D4D4D; border-color: #4D4D4D;}

.place_amenities_step .amenities_wrapper .amenity-modal .check-box .checkbox_wrapper {display: flex; align-items: center; justify-content: space-between;}

/* ---------------- Modal Css ----------------- */

.custom_modal .modal-body .btn-close { position: absolute; right: 12px; top: 10px;}
.listing_stepper .amenity-modal .modal-body .check-box { padding-top: 30px; padding-right: 25px; max-height: 400px; overflow: scroll;}
.listing_stepper .amenity-modal .modal-body .check-box label { cursor: pointer;}
.listing_stepper .amenity-modal .modal-body .check-box .divider:last-child { border: 0;}
.listing_stepper .amenity-modal .modal-body { padding: 25px 35px;}
.listing_stepper .amenity-modal .modal-header .count::before {counter-increment: section;content: counter(section); background-repeat: no-repeat; position: absolute; background-color: #000; color: #fff; font-family: "Font Awesome 6 Pro";font-size: 10px; font-style: normal;font-weight: 900;padding: 3px;border-radius: 100%;width: 5%;height: 24px;right: 9px;top: 8px;}
.listing_stepper .amenity-modal .check-box .form-check {width: 100%;}
.listing_stepper .amenity-modal .check-box .form-check-input[type=checkbox] { padding: 10px; box-shadow: none; border: 2px solid var(--primary-color);}
.listing_stepper .amenity-modal .check-box .form-check-input:checked {background-color: #000;border-color: #000;}

.listing_stepper .amenity-modal .modal-body .check-box .form-check-custom p {word-break: break-word; padding-right: 60px;}

/* .listing_stepper .amenity-modal { text-align: center;border-radius: 16px;background: #FFF;} */
/* .amenity-modal .modal {top: 30%;} */
/* .listing_stepper .amenity-modal .modal-body .check-box { display: flex;justify-content: space-between;align-items: center;} */
/* .listing_stepper .amenity-modal .modal-header { border-bottom: 1px solid transparent;} */
/* .listing_stepper .amenity-modal h5 { font-style: normal; line-height: normal; text-transform: capitalize;} */
/* .listing_stepper .amenity-modal .check-box .form-check label { display: flex; justify-content: space-between; align-items: center;} */
/* .listing_stepper .amenity-modal .check-box .form-check>input[type=checkbox]:checked {accent-color: #000;} */
/* .listing_stepper .amenity-modal .check-box .form-check-input:focus {border-color: transparent;outline: 0;box-shadow:unset;} */
/* .listing_stepper .amenity-modal .amen-box{text-align: left;}  */
/* .listing_stepper .amenity-modal .amen-box .form-check-custom{margin-bottom: 10px;display: flex;align-items: center;justify-content: start;gap: 6px;} */

/* Key Feature */
.listing_stepper .key_features_step .single_saved_feature { background-color: #fff; padding: 10px 15px; border-radius: 12px; border: 2px solid var(--primary-color);}
.listing_stepper .key_features_step .single_saved_feature:not(:last-child) { margin-bottom: 15px;}
.listing_stepper .key_features_step .single_saved_feature .content_cross_wrapper, .listing_stepper .key_features_step-tour .saved_features_list_wrapper-tour .single_saved_feature-tour .content_cross_wrapper {display: flex; text-align: left; align-items: flex-start; justify-content: space-between; }
.listing_stepper .key_features_step .single_saved_feature .content_cross_wrapper .feature_description p { font-size: 14px; margin-bottom: 0;}
.listing_stepper .key_features_step .saved_features_list_wrapper .single_saved_feature .content_cross_wrapper .feature_description p:first-letter, .listing_stepper .key_features_step .saved_features_list_wrapper .single_saved_feature .content_cross_wrapper .feature_title :first-letter { text-transform: capitalize;}
.listing_stepper .key_features_step .fields_content_wrapper {max-width: 70%; margin: 0 auto;}

/* .listing_stepper .key_features_step .saved_features_list_wrapper, .listing_stepper .key_features_step-tour .saved_features_list_wrapper-tour {margin-top: 20px} */
/* .listing_stepper .key_features_step .saved_features_list_wrapper .single_saved_feature .content_cross_wrapper .feature_title label, .listing_stepper .key_features_step-tour .saved_features_list_wrapper-tour .single_saved_feature-tour .content_cross_wrapper .feature_title label {padding-left: 0;} */


/* Custom radio style starts from here */
.label_radios_wrapper {display: flex; justify-content: space-between; align-items: center;}
.radios_wrapper {display: flex; align-items: center; gap: 15px;}
.custom_radio {display: flex; gap: 15px; align-items: center;}
.custom_radio label {position: relative; padding-right: 30px}
.custom_radio input[type="radio"] {display: none}
.custom_radio label:before {content: ""; display: block; position: absolute; width: 19px; height: 19px; border: 2px solid #000; right: 0; border-radius: 5px; top: 0;}
.custom_radio:has(input[type="radio"]:checked) label:before {background: var(--primary-color);}
.custom_radio:has(input[type="radio"]:checked) label:after {content: "\f00c"; font-family: "Font Awesome 5 Free"; font-weight: 900; display: block; position: absolute; width: 19px; height: 19px; right: 0; border-radius: 5px; top: 0; color: #fff; display: flex; justify-content: center; align-items: center; font-size: 13px}


/* Date Picker */
#ui-datepicker-div { z-index: 9 !important;} /* To overwrite inline css */
.ui-datepicker .ui-datepicker-header { background: var(--primary-color); }
.ui-datepicker .ui-state-default.ui-state-highlight, .ui-datepicker .ui-widget-content .ui-state-highlight, .ui-datepicker  .ui-widget-header .ui-state-highlight, .ui-state-default.ui-state-active {border: 1px solid var(--primary-color); background: var(--primary-color); color: #fff; border-radius: 4px;}
.ui-datepicker .ui-state-default, .ui-widget-content .ui-state-default, .ui-datepicker .ui-widget-header .ui-state-default,.ui-datepicker .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active{border: 1px solid #fff; background: #fff;text-align: center;}
.ui-datepicker .ui-datepicker-prev, .ui-datepicker .ui-datepicker-next { top: 5px; background-color: transparent; color: #000;}
.ui-datepicker .ui-icon.ui-icon-circle-triangle-e:before, .ui-datepicker .ui-datepicker-prev span:before { content: ''; height: 12px; width: 16px; border-top: 2px solid #000; border-right: 2px solid #000; rotate: 45deg; position: absolute; top: 0; right: 3px; color: #000; }
.ui-datepicker .ui-datepicker-prev span:before { rotate:217deg; height: 11px; width: 11px; top: 2px; right: unset; left: 5px; }
.ui-datepicker .ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus, .ui-button:hover, .ui-button:focus{ border: 0;}
.ui-datepicker .ui-state-default.ui-state-hover { background-color: var(--primary-color); color: #fff; border-radius: 4px;}


/* New changes starts from here */
/* Global stepper style starts from here */

.h_100 {height: 100%;}
.custom_h { height: 100vh;}

/* Global stepper style ends here */

/* Stepper Global changes starts from here */
.listing_stepper .save-header {right: 0;}
.listing_stepper .custom-rw {height: unset;}
.listing_stepper .custom-rw .topbar {height: unset;}

.txt_field {margin-bottom: 20px;}
.txt_field input:not([type="checkbox"], [type="radio"], [type="submit"]) { width: 100%; border-radius: 12px; border: .5px solid #9b9b9b; background: #FFF; height: 42px; padding-inline: 10px; color: #000;}

/* Stepper Global changes ends here */

/* Supplier Agreement modal style starts from here */

.modal_extra_width .modal-dialog {width: 900px;}
.add_listing_disclaimer_modal .modal_title_cross_icon_wrapper .close {opacity: 1;}
.add_listing_disclaimer_modal .agreement_content {max-height: 450px; overflow-y: auto; padding-right: 20px; margin: 20px 0 40px;}

/* Supplier Agreement modal style ends here */

/* WaterCraft stepper style starts from here */
.listing_stepper fieldset.owner_documents_step .inner_section_ownership_docs {width: 100%;}
.listing_stepper fieldset.owner_documents_step .top_box1 {max-width: 100%; /* padding-left: 30px; */ padding-right: 30px;}
.listing_stepper fieldset.owner_documents_step .inner_section_ownership_docs .list_of_accepted_doc_btn {font-size: 16px; color: var(--blue); font-family: "Poppins";}

/* Activity Slide */
.listing_stepper .basic_accommodation_step .activity_wrapper { min-width: 56%;}

/* WaterCraft stepper style ends here */

/* Experiences stepper style starts from here */

.listing_stepper fieldset.select_categories_step {height: 100%;}
/* .listing_stepper .select_categories_step .inner_section_fieldset {padding-top: 40px;} */
.listing_stepper .select_categories_step .categories_search_field .txt_field {position: relative;}
.listing_stepper .select_categories_step .categories_search_field .txt_field input {padding-left: 40px; border: 1px solid #000;}
.listing_stepper .select_categories_step .categories_search_field .txt_field i {position: absolute; left: 15px; top: 0; bottom: 0; height: fit-content; margin: auto 0; color: #000;}

.listing_stepper .select_categories_step .inner_section_single_category label {border: 2px solid #000; border-radius: 20px; width: 100%; height: 150px; display: flex; justify-content: center; align-items: center; flex-direction: column; cursor: pointer;}
.listing_stepper .select_categories_step .inner_section_single_category:has(input[type="radio"]:checked) label, .listing_stepper .select_categories_step .inner_section_single_category:has(input[type="checkbox"]:checked) label {background-color: var(--primary-color);}
.listing_stepper .select_categories_step .inner_section_single_category input[type="radio"], .listing_stepper .select_categories_step .inner_section_single_category input[type="checkbox"] {display: none;}
.listing_stepper .select_categories_step .inner_section_single_category label .category_icon_wrapper {width: 50px; height: 50px; overflow: hidden; margin: 0 auto;}
.listing_stepper .select_categories_step .inner_section_single_category label .category_icon_wrapper img {width: 100%; height: 100%; object-fit: contain; object-position: center; filter: brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(7454%) hue-rotate(124deg) brightness(79%) contrast(109%);}

.listing_stepper fieldset.watercraft_step1_step, .listing_stepper fieldset.owner_documents_step {height: 100%; padding-top: 0;}
.listing_stepper fieldset.watercraft_step1_step .inner_section_fieldset .col_left, .listing_stepper fieldset.watercraft_step1_step .inner_section_fieldset .col_right, .listing_stepper fieldset.owner_documents_step .col_left, .listing_stepper fieldset.owner_documents_step .col_right  {display: flex; justify-content: center; align-items: center;}
.listing_stepper fieldset.watercraft_step1_step .inner_section_fieldset .inner_section_right, .listing_stepper fieldset.owner_documents_step .inner_section_fieldset .inner_section_right {width: 100%;}
.listing_stepper fieldset.watercraft_step1_step .inner_section_fieldset .inner_section_left, .listing_stepper fieldset.owner_documents_step .inner_section_fieldset .inner_section_left {text-align: left;}
.listing_stepper fieldset.watercraft_step1_step .inner_section_fieldset .step_image_wrapper, .listing_stepper fieldset.owner_documents_step .inner_section_fieldset .step_image_wrapper {width: 100%; height: 430px; overflow: hidden; border-radius: 20px;}
/* .listing_stepper fieldset.owner_documents_step .inner_section_fieldset .step_image_wrapper { width: 100%; height: 340px; overflow: hidden; border-radius: 20px;} */
.listing_stepper fieldset.watercraft_step1_step .inner_section_fieldset .step_image_wrapper img, .listing_stepper fieldset.owner_documents_step .inner_section_fieldset .step_image_wrapper img {width: 100%; height: 100%; object-fit: cover; object-position: bottom;}
.listing_stepper fieldset.owner_documents_step .inner_section_fieldset .step_image_wrapper { height: 480px;}
.listing_stepper .inner_section_fieldset {padding-inline: 30px;}

.listing_stepper .select_categories_step .inner_section_single_category label .category_title h5 { font-size: 18px; font-weight: 500; color: #4A4A4A; font-family: 'Poppins'; }
.listing_stepper .select_categories_step .inner_section_fieldset .inner_section_single_category {margin-bottom: 30px;}
.listing_stepper .select_categories_step .inner_section_categories_main_col { overflow: hidden; overflow-x: clip; overflow-y: auto; padding-right: 15px; height: 55vh; }

.listing_stepper .select_categories_step .inner_section_fieldset {max-width: 75%; margin: 0 auto;}

.listing_stepper .inner_section_fieldset {padding-top: 0;} /* Was 40px before */
.listing_stepper .experiences_location_step .inner_section_main_col {max-width: 75%; margin: 0 auto;}
.listing_stepper .experiences_location_step .location_map_wrapper {border-radius: 20px; overflow: hidden; box-shadow: 10px 10px 20px #0000000f; position: relative;}
.listing_stepper .experiences_location_step .location_map_wrapper #map {height: calc(50vh - 20px);}

.listing_stepper .experiences_location_step .location_map_wrapper #map .gmnoprint:not(:has(.gm-control-active)), .listing_stepper .experiences_location_step .location_map_wrapper #map .gm-fullscreen-control {display: none;}
.listing_stepper .experiences_location_step .location_map_wrapper #map .gm-style-cc, .listing_stepper .experiences_location_step .location_map_wrapper #map .gm-svpc {display: none;}
.listing_stepper .experiences_location_step .location_map_wrapper #map a:has(img[alt="Google"]) {display: none !important;}

.listing_stepper .experiences_location_step .inner_section_main_col .location_map_wrapper .pac-target-input {position: absolute; top: 40px; left: 0; z-index: 10; max-width: 50%; margin: 0 auto; right: 0; border: 3px solid var(--primary-color);}
.listing_stepper .experiences_location_step .inner_section_main_col .location_map_wrapper .pac-target-input::placeholder {color: #666 !important; font-weight: 600 !important;}

/* .listing_stepper .experiences_location_step .inner_section_main_col .location_map_wrapper .gmnoprint {display: none;} */

.listing_stepper .confirm_location_step .fields_wrapper, .listing_stepper .basic_watercraft_step .fields_wrapper { height: calc(100vh - 380px); overflow: hidden; overflow-y: auto; padding-right: 10px; text-align: left; margin-top: 30px;}
.listing_stepper .confirm_location_step .inner_section_main_col, .listing_stepper .add_videos_step .inner_section_main_col {max-width: 65%; margin: 0 auto;}

.listing_stepper fieldset.basic_accommodation_step {height: 100%;}

.listing_stepper .basic_accommodation_step .space_details_wrapper {margin-top: 40px; max-width: 60%; margin-left: auto; margin-right: auto;}
.listing_stepper .basic_accommodation_step .space_details_wrapper .space_detail_single {display: flex; justify-content: space-between; align-items: center; padding: 15px 0;}
.listing_stepper .basic_accommodation_step .space_details_wrapper .space_detail_single:not(:last-child) {border-bottom: 1px solid var(--border-grey);}
.listing_stepper .basic_accommodation_step .space_details_wrapper .space_detail_quantity_wrapper {display: flex; align-items: center; gap: 10px}
.listing_stepper #msform .basic_accommodation_step .space_details_wrapper .space_detail_quantity_wrapper .file {width: 60px; text-align: center; font-size: 16px}
.listing_stepper .basic_accommodation_step .space_details_wrapper .space_detail_quantity_wrapper .plus_btn, .listing_stepper .basic_accommodation_step .space_details_wrapper .space_detail_quantity_wrapper .minus_btn {padding: 0; width: 32px; height: 32px; border: 2px solid #000; border-radius: 50%;}
.listing_stepper .basic_accommodation_step .space_details_wrapper .space_detail_quantity_wrapper .plus_btn i:before {content: "\f067"; color: unset; border: unset; font-size: 14px; border-radius: unset; width: unset; display: unset ; justify-content: unset; font-weight: unset;}
.listing_stepper .basic_accommodation_step .space_details_wrapper .space_detail_title label {font-size: 25px; font-weight: 500; color: #4A4A4A; font-family: 'Poppins';}

.listing_stepper .basic_vehicle_step .space_details_wrapper .space_detail_single {border-bottom: 1px solid var(--border-grey);}


.listing_stepper fieldset.place_amenities_step {height: 100%; padding-top: 0;}
.listing_stepper fieldset.place_amenities_step .inner_section_main_col .inner_section_col_left .amenities_wrapper {display: flex; gap: 15px; flex-wrap: wrap; justify-content: center; padding-top: 7px;}
.listing_stepper fieldset.place_amenities_step .amenities_wrapper .amenity-div {width: 180px; height: 125px; display: flex; flex-direction: column; align-items: center; justify-content: center; margin-bottom: 10px; position: relative; box-shadow: 0 0 10px -5px #0000074f;}
.listing_stepper fieldset.place_amenities_step .amenities_wrapper .amenity-div > h5 { color: #4D4D4D; font-family: Poppins; font-size: 17px; font-style: normal; font-weight: 400; line-height: normal; text-transform: capitalize; }
.listing_stepper fieldset.place_amenities_step .amenities_wrapper .amenity-div .amen_icon {width: 50px; height: 25px; overflow: hidden;}
.listing_stepper fieldset.place_amenities_step .amenities_wrapper .amenity-div .amen_icon img {width: 100%; height: 100%; object-fit: contain; object-position: center;}
.listing_stepper fieldset.place_amenities_step .inner_section_main_col .inner_section_col_left .amenities_wrapper .amenity-modal:has(.fade:not(.show)) {display: none;}
.listing_stepper fieldset.place_amenities_step .count { position: absolute; background-color: var(--primary-color); color: #000; font-family: "Font Awesome 6 Pro"; font-size: 10px; font-weight: 900; padding: 6px; border-radius: 100%; width: 12%; height: 23px; right: 8px; top: 10px; }


body:has(.listing_stepper) .select2-results__option[aria-selected=true]:before { font-family:fontAwesome; content: unset; color: #000; background-color: var(--primary-color); border: 1.65px solid #000;border-radius: 7px; display: inline-block; padding-left: 3px; font-size: 12px;}
.listing_stepper fieldset.place_amenities_step .search_field_wrapper_amenities .select2 {width: 100% !important;}
.listing_stepper fieldset.place_amenities_step .search_field_wrapper_amenities .select2 .select2-search {width: 100%;}
.listing_stepper fieldset.place_amenities_step .search_field_wrapper_amenities .select2 .select2-search__field {width: 100% !important; border-radius: 12px; font-size: 12px; border: .5px solid #9b9b9b; background: #FFF; padding: 12px 18px; color: #000; margin: 0;}
.listing_stepper fieldset.place_amenities_step .search_field_wrapper_amenities .select2 .select2-search__field::placeholder { color: var(--text-color-dark-grey); font-weight: 400;}
.listing_stepper fieldset.place_amenities_step .search_field_wrapper_amenities .select2 .select2-selection__rendered {padding: 0;}
.listing_stepper fieldset.place_amenities_step .search_field_wrapper_amenities .select2.select2-container--open .select2-search__field {border-bottom-left-radius: 0; border-bottom-right-radius: 0; border-bottom: 0;}

.listing_stepper fieldset.place_amenities_step .search_field_wrapper_amenities .select2:before { content: "\f078"; position: absolute; right: 15px; top: 0; bottom: 0; margin: auto 0; font-size: 15px; font-family: 'FontAwesome'; font-weight: 900; width: 30px; height: fit-content; transition: .4s ease; }
.listing_stepper fieldset.place_amenities_step .search_field_wrapper_amenities .select2.select2-container--open:before { content: "\f078"; transform: rotate(180deg); transition: .4s ease; }

.listing_stepper fieldset.place_amenities_step .search_field_wrapper_amenities .select2 .select2-selection {height: 40px;}
.listing_stepper .select2-container--default .select2-selection--multiple {border: 0; background-color: transparent;}
.listing_stepper .select2-container--default .select2-selection--multiple:focus {border: 0; background-color: transparent; outline: 0; box-shadow: 0;}
.listing_stepper .select2-container--default.select2-container--focus .select2-selection--multiple {border: 0;}
body:has(.listing_stepper) .select2-results__option:before {content: unset;}

.listing_stepper .place_amenities_step .select2 .select2-selection__rendered .select2-selection__choice {display: none;}

/* .listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper {display: flex; justify-content: flex-start; flex-wrap: wrap; gap: 5px;} */
.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper { /*display: flex; justify-content: flex-start; flex-wrap: wrap; gap: 15px;*/ margin-top: 20px;}
.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity:not(:last-child) {border-bottom: 1px solid #e2e2e2;}
/* .listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity {padding: 10px 20px; border: 2px solid var(--primary-color); border-radius: 30px; background-color: #000; color: var(--primary-color); font-weight: 600} */
.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity .remove-item {padding: 0; background: transparent;}
.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity .remove-item i {color: #000; font-size: 18px;}
.listing_stepper fieldset.place_amenities_step .inner_section_col_right {margin-left: 15px; padding-left: 25px; border-left: 1px solid #000;}

.listing_stepper .place_amenities_step .selected_amenities_wrapper .custom_amenitiy_single input {display: none;}

/* .select2-container .select2-results__options {gap: 10px} */
/* .select2-container .select2-results__options .select2-results__option {color: #000; padding: 10px 15px;; font-family: "Poppins"; font-size: 16px; font-weight: 500} */
/* .select2-container .select2-results__options .select2-results__option:not(:last-child) {margin-bottom: 0px; margin-top: 0px; border-radius: 0px; border-bottom: 1px solid #000; } */
/* .select2-container .select2-results__options .select2-results__option[aria-selected="true"] {background-color: #fcdf73ba;} */
/* .select2-container .select2-results__options .select2-results__option.select2-results__option--highlighted {background-color: #fcdf73ba;} */

body:has(.listing_stepper) .select2-container .select2-dropdown .select2-results__options {padding: 0;}
body:has(.listing_stepper) .select2-container .select2-dropdown .select2-results__options .select2-results__option {color: #000; font-family: 'Poppins'; padding-left: 20px; padding-right: 20px; padding-block: 8px; position: relative;}
body:has(.listing_stepper) .select2-container .select2-dropdown .select2-results__options .select2-results__option.select2-results__option--highlighted {background: #ffff0061;}
body:has(.listing_stepper) .select2-container .select2-dropdown .select2-results__options .select2-results__option[aria-selected="true"]:before {content: "\f00c"; display: block;}
body:has(.listing_stepper) .select2-container .select2-dropdown .select2-results__options .select2-results__option[aria-selected="true"] {background-color: #ffce32;}
/* body:has(.listing_stepper) .select2-container .select2-dropdown .select2-results__options .select2-results__option:last-child {} */
body:has(.listing_stepper) .select2-container--open .select2-dropdown--below {box-shadow: unset; border-bottom-right-radius: 10px; border-bottom-left-radius: 10px; overflow: hidden;}

/* Old One */
body:has(.listing_stepper) .select2-results__option:before { content: ""; display: inline-block; position: relative; float: right; height: 20px; width: 20px; border: 1.65px solid #000; border-radius: 4px; background-color: #fff; /* margin-right: 20px; */vertical-align: middle;}
body:has(.listing_stepper) .select2-results__option[aria-selected=true]:before { font-family:fontAwesome; content: "\f00c"; color: #000; background-color: var(--primary-color); border: 1.65px solid #000;border-radius: 7px; display: inline-block; padding-left: 3px; font-size: 12px;}
/* Old end here */

.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity {position: relative; display: flex; align-items: flex-start; justify-content: space-between; width: 100%; padding: 10px 0;}
.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity img {width: 30px; height: 30px; overflow: hidden;}
.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity .icon_name_wrapper {display: flex; align-items: start; gap: 10px; min-width: 90%;}
.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity .icon_name_wrapper label {font-family: "Poppins"; font-size: 16px;}

.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity .icon_name_wrapper .content_parent { max-width: 75%;}
.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper .single_amenity .icon_name_wrapper p { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; font-size: 15px; }

.listing_stepper fieldset.place_amenities_step .inner_section_main_col .inner_section_col_left .amenities_wrapper {height: calc(100vh - 450px); overflow-y: auto;}
.listing_stepper fieldset.place_amenities_step .selected_amenities_wrapper {height: calc(100vh - 470px); overflow-y: auto; padding-right: 15px;}

.listing_stepper fieldset.key_features_step {height: 100%; padding-top: 0;}
/* .listing_stepper fieldset.key_features_step .add_note {text-transform: capitalize;} */

/* .listing_stepper .key_features_step .key_features_fields_wrapper .ck-editor__editable { border-radius: 12px !important; border: 0.5px solid #E2E8F0 !important; }
.listing_stepper .key_features_step .key_features_fields_wrapper .ck-editor__editable p {font-family: poppins; font-weight: 600; font-size: 13px; line-height: 15px; color: #000;}
.listing_stepper .key_features_step .key_features_fields_wrapper .ck-editor__editable:focus {box-shadow: none}
.listing_stepper .key_features_step .key_features_fields_wrapper .ck.ck-editor .ck-toolbar {border: 0;} */

.listing_stepper #msform .key_features_step .ck-editor__editable { border-radius: 12px; border: .5px solid #9b9b9b; box-shadow: unset;}
.listing_stepper #msform .key_features_step .ck-editor__editable p {font-family: 'Poppins'; font-weight: 600; font-size: 13px; line-height: 15px; color: #000;}
.listing_stepper #msform .accommodation_description_step .ck-editor__editable p {font-family: 'Poppins'; font-weight: 600; font-size: 16px; line-height: 20px; color: #000;}
.listing_stepper .key_features_step .ck-editor__editable:focus {box-shadow: none}
.listing_stepper .key_features_step .ck.ck-editor .ck-toolbar {border: 0;}

.listing_stepper .key_features_step .inner_section_main_col {max-width: 70%; margin: 0 auto;}
.listing_stepper .key_features_step .key_features_fields_wrapper {margin-top: 40px; max-height: calc(100vh - 380px); overflow-y: auto; padding-right: 15px;}
.listing_stepper .key_features_step .features_add_btn_wrapper {display: flex; justify-content: space-between; align-items: center;}

.listing_stepper .key_features_step .features_add_btn_wrapper .add_feature_btn {color: #4A4A4A; font-family: 'Poppins'; font-size: 15px; font-style: normal; font-weight: 600; line-height: normal; text-transform: capitalize; border-radius: 33px; background: var(--primary-color); padding: 6px 25px;}
.listing_stepper .key_features_step .saved_features_list_wrapper .single_saved_feature .content_cross_wrapper .feature_title h6 {color: #000; font-family: 'Poppins'; font-weight: 600; font-size: 13px; line-height: 15px; color: #000; margin-bottom: 5px;}
.listing_stepper .key_features_step .saved_features_list_wrapper .single_saved_feature .cross_icon_wrapper { opacity: 0; transition: .3s ease-in; } 
.listing_stepper .key_features_step .saved_features_list_wrapper .single_saved_feature:hover .cross_icon_wrapper { opacity: 1; transition: .3s ease-in; }

.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper {margin-top: 40px;}
.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper .allow_pets_input_wrapper {display: flex; justify-content: center; align-items: center; gap: 20px; margin-top: 25px}
.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper .allow_pets_input_wrapper .allow_pets_input input {display: none;}
.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper .allow_pets_input_wrapper .allow_pets_input label {color: #4D4D4D; font-size: 22px !important; font-weight: 500; line-height: 33px; padding: 6px; border-radius: 16px; border: 1px solid #BDBDBD; width: 300px; cursor: pointer;}
.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper .allow_pets_input_wrapper .allow_pets_input:has(input[type="radio"]:checked) label {background: var(--lime-yellow); border: 1px solid var(--lime-yellow); color: #4D4D4D;}

.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper {margin-top: 40px;}
.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper .allow_pets_input_wrapper { display: flex; justify-content: center; align-items: center; gap: 20px; margin-top: 25px }
.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper .allow_pets_input_wrapper .allow_pets_input input {display: none;}
.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper .allow_pets_input_wrapper .allow_pets_input label { color: #4D4D4D; font-size: 22px !important; font-weight: 500; line-height: 33px; padding: 6px; border-radius: 16px; border: 1px solid #BDBDBD; width: 300px; cursor: pointer; }
.listing_stepper .accommodations_rules_step .allow_pets_field_wrapper .allow_pets_input_wrapper .allow_pets_input:has(input[type="radio"]:checked) label { background: var(--lime-yellow); border: 1px solid var(--lime-yellow); color: #4D4D4D; }
.listing_stepper .accommodations_rules_step .accommodation_custom_rules_wrapper {margin-top: 50px;}
.custom_add_tags_field_wrapper {text-align: left;}
.custom_add_tags_field_wrapper .label_addBtn_wrapper {display: flex; align-items: center; justify-content: space-between;}
.custom_add_tags_field_wrapper .label_addBtn_wrapper .cust_add {color: #4A4A4A; font-family: Poppins; font-size: 13px; font-style: normal; font-weight: 600; line-height: normal; text-transform: capitalize; border-radius: 33px; background: var(--primary-color); padding: 5px 15px;}
.custom_add_tags_field_wrapper .custom_tags_wrapper {display: flex; justify-content: flex-start; align-items: flex-start; flex-wrap: wrap; gap: 10px;}
.custom_add_tags_field_wrapper .custom_tags_wrapper .single_custom_tag, .yellow_bubble { /* border: 2px solid #000; */ background-color: var(--primary-color);  border-radius: 30px; padding:8px 15px}
.custom_add_tags_field_wrapper .custom_tags_wrapper .single_custom_tag .remove-item, .yellow_bubble .remove-item {color: rgb(233, 69, 69); font-size: 14px; margin-inline: 6px;}

.listing_stepper .accommodations_rules_step .accommodation_custom_rules_wrapper { margin-top: 50px; max-height: calc(100vh - 470px); overflow: hidden; overflow-y: auto; padding-right: 10px; }
.listing_stepper .accommodations_rules_step  .inner_section_main_col { max-width: 70%; margin: 0 auto; }
.listing_stepper fieldset.accommodations_rules_step {height: 100%; padding-top: 0;}

.listing_stepper .add_photos_step .inner_section_main_col {max-width: 70%; margin: 0 auto;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .add_photo_box {height: 400px; width: 100%; border: 2px dashed #c2c2c2; display: flex; flex-direction: column; justify-content: center; padding: 20px; gap: 80px}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .add_photo_box .photo_icon {width: 110px; height: 110px; overflow: hidden; margin: 0 auto;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .add_photo_box .photo_icon img {width: 100%; height: 100%; object-fit: contain; object-position: center;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .add_photo_box .add_photo_btn label {color: #4A4A4A; font-family: Poppins; font-size: 13px; font-style: normal; font-weight: 600; line-height: 0; border-radius: 33px; background: var(--primary-color); padding: 25px 30px; cursor: pointer;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .add_photo_box .add_photo_btn input[type="file"] {display: none;}

.listing_stepper .accommodation_title_step .accommodation_title_field {margin-top: 60px;}
.listing_stepper .accommodation_title_step .inner_section_main_col {max-width: 70%; margin: 0 auto;}
.listing_stepper fieldset.accommodation_title_step {height: 100%; padding-top: 0;}
.listing_stepper #msform .accommodation_title_step .accommodation_title_field .txt_field input {font-size: 30px; height: 80px; line-height: 40px; text-align: left; }
.listing_stepper #msform .accommodation_title_step .accommodation_title_field .txt_field input::placeholder {font-size: 30px; height: unset; line-height: 60px;}

.listing_stepper .set_price_step .setting_price_input .icon_input_wrapper {position: relative; min-width: 150px; margin: 0 auto; padding: 0; display: flex; gap: 5px; align-items: center;}
.listing_stepper .set_price_step .setting_price_input .icon_input_wrapper i {/*position: absolute; top: 0; left: 10px; bottom: 0;*/ height: fit-content; margin: auto 0; color: #000; font-size: 80px;}
.listing_stepper #msform .set_price_step .setting_price_input .icon_input_wrapper input {font-size: 80px; height: unset; /*padding: 0; padding-left: 70px;*/ background-color: transparent; padding: 0 10px; flex-grow: 1; flex: 1; flex-shrink: 0; width: 150px;}
.listing_stepper #msform .set_price_step .setting_price_input .icon_input_wrapper input:not(.valid_error) {border: 1px solid transparent !important;}
/* .listing_stepper #msform .set_price_step .setting_price_input .icon_input_wrapper input:focus {background-color: #fff; border: .5px solid #E2E8F0 !important;} */
.listing_stepper #msform .set_price_step .setting_price_input .icon_input_wrapper input::placeholder {font-size: 80px; font-family: poppins; font-weight: 600;}

.listing_stepper .set_price_step .inner_section_main_col .price_hidden_fields_wrapper .setting_price_input .txt_field {display: inline-flex; align-items: center; gap: 10px; }
.listing_stepper .set_price_step .inner_section_main_col .price_hidden_fields_wrapper .setting_price_input .txt_field > span {font-size: 80px; line-height: 145px; font-family: "Poppins"; font-weight: 600; color: #000;}

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table {border-collapse: separate; border-spacing: 0;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr th {text-align: left; background: transparent;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr td {text-align: right; background: transparent;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr th, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr td {font-size: 14px; font-weight: 400; font-family: "Poppins"}

/* .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table th {border: solid 1px #000; border-style: none solid solid none; padding: 10px;} */
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead td {border: solid 1px #000; border-left: 0; border-style: none solid solid none; padding: 10px;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead th {border: solid 1px #000; border-right: 0; border-style: none solid solid none; padding: 10px;}

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr:not(:last-child) td {border: solid 1px #000; border-left: 0; border-top: 0; border-bottom: 0; border-style: none solid solid none; padding: 10px;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr:not(:last-child) th {border: solid 1px #000; border-right: 0; border-top: 0; border-bottom: 0; border-style: none solid solid solid; padding: 10px;}

.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr:last-child td {border: solid 1px #000; border-left: 0; border-top: 0; border-bottom: solid 1px #000; border-style: none solid solid none;}
.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr:last-child th {border: solid 1px #000; border-right: 0; border-top: 0; border-bottom: solid 1px #000; border-style: none solid solid solid;}

.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child td {border-top: solid 1px #000; border-bottom: 0; border-style: solid solid none none;}
.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child th {border-top: solid 1px #000; border-bottom: 0; border-style: solid solid none solid;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:not(:last-child) td {border: solid 1px #000; border-left: 0; border-top: 0; border-bottom: 0; border-style: none solid solid none;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:not(:last-child) th {border: solid 1px #000; border-right: 0; border-top: 0; border-bottom: 0; border-style: none solid solid solid;}
.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:last-child td {border: solid 1px #000; border-left: 0; border-top: 0; border-bottom: solid 1px #000; border-style: none solid solid none;}
.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:last-child th {border: solid 1px #000; border-right: 0; border-top: 0; border-bottom: solid 1px #000; border-style: none solid solid solid;}
.listing_stepper .set_price_step .inner_section_main_col .price_hidden_fields_wrapper .price_breakdown_wrapper {max-width: 55%; margin: 0 auto;}


/* .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child td {border: solid 1px #000; border-left: 0; border-top: solid 1px #000; border-style: solid solid solid solid;} */
/* .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child th {border: solid 1px #000; border-right: 0; border-top: solid 1px #000; border-style: solid solid solid solid;} */
/* .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:last-child td {border: solid 1px #000; border-left: 0; border-top: 0; border-bottom: 0; border-style: solid solid solid solid;} */
/* .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:last-child th {border: solid 1px #000; border-right: 0; border-top: 0; border-bottom: 0; border-style: solid solid solid solid;} */

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:first-child td:first-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:first-child th:first-child { border-top-left-radius: 10px; }
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:first-child td:last-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:first-child th:last-child { border-top-right-radius: 10px; }

/* .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child td:first-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child th:first-child { border-top-left-radius: 10px; } */
/* .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child td:last-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child th:last-child { border-top-right-radius: 10px; } */

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:last-child td:first-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:last-child th:first-child { border-bottom-left-radius: 0; }
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:last-child td:last-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:last-child th:last-child { border-bottom-right-radius: 0; }

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr:last-child td:first-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr:last-child th:first-child { border-bottom-left-radius: 10px; }
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr:last-child td:last-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr:last-child th:last-child { border-bottom-right-radius: 10px; }

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child td:first-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child th:first-child { border-top-left-radius: 10px; border-top: 1px solid #000 !important;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child td:last-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child th:last-child { border-top-right-radius: 10px; border-top: 1px solid #000 !important;;}

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr:first-child td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr:first-child th { border-top-style: solid; }
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr td:first-child, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tr th:first-child { border-left-style: solid; }

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr th {padding-bottom: 2px; padding-top: 2px;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:first-child td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:first-child th {padding-top: 15px;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr th {border-bottom: 0}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:last-child td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table thead tr:last-child th {padding-bottom: 15px;}

.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr td, .listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr th {padding-bottom: 2px; padding-top: 2px;}
.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr:first-child td, .listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr:first-child th {padding-top: 15px; border-top: 1px solid rgb(216, 216, 216);}
.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr td, .listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr th {border-bottom: 0;}
.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr:last-child td, .listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr:last-child th {padding-bottom: 15px;}

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr th {padding-bottom: 2px; padding-top: 2px;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:first-child th {padding-top: 15px;}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr th {border-bottom: 0}
.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:last-child td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot tr:last-child th {padding-bottom: 15px;}

.listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot th, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tfoot td, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody th, .listing_stepper .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody td {font-weight: 700;}

.listing_stepper #msform .set_price_step .hidden_price_breakdown_wrapper .price_breakdown_wrapper table tbody tr.spacer td {border: 0;}

.listing_stepper .set_price_step .inner_section_main_col {max-width: 70%; margin: 0 auto;}
.listing_stepper .set_price_step .inner_section_main_col .price_hidden_fields_wrapper {/*max-width: 50%;*/ margin: 0 auto; overflow-y: auto; /*max-height: 340px;*/ padding-right: 15px; max-height: calc(100vh - 450px); overflow-x: hidden;}

.listing_stepper .set_price_step .learn_more_wrapper {margin-top: 30px;}
.listing_stepper .set_price_step .learn_more_wrapper .learn_more_pricing_btn, .listing_stepper .learn_more_btn { color: var(--blue); font-family: 'Poppins'; font-size: 18px; font-style: italic; font-weight: 500; line-height: 150%; text-decoration-line: underline;}
.listing_stepper .learn_more_btn { font-style: normal; text-decoration: none;}

.listing_stepper .min_stay_requirement_step .inner_section_categories_main_col {margin-top: 40px;}

/* .listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper {margin-top: 50px; max-width: 70%; margin-left: auto; margin-right: auto} */

.listing_stepper .checkIn_checkOut_step .checkin_checkout_price_main_wrapper {margin-top: 50px; max-width: 70%; margin-left: auto; margin-right: auto; overflow: hidden; overflow-y: auto; max-height: calc(100vh - 400px); padding-right: 10px;}

.listing_stepper .checkIn_checkOut_step .inner_section_main_col {max-width: 70%; margin: 0 auto;}
.listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper .checkin_title label {color: #4A4A4A; font-family: 'Poppins'; font-size: 22px; font-style: normal; font-weight: 500; line-height: 140%; }
.listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper .col_left, .listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper .col_right {display: flex; align-items: flex-start;}
.listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper .col_left .inner_section_left_col, .listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper .col_right .inner_section_right_col {width: 100%;}
.listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper .col_left .inner_section_left_col {text-align: left;}

.listing_stepper #msform .seasonal_pricing_step .time_field_wrapper input {width: 100%; padding-left: 15px; font-size: 17px; font-weight: 500; border: 1px solid #9b9b9b; height: 42px;}
.listing_stepper .seasonal_pricing_step .checkin_checkout_fields_wrapper .inner_section_right_col .time_field_wrapper {position: relative;}
.listing_stepper .seasonal_pricing_step .checkin_checkout_fields_wrapper .inner_section_right_col .time_field_wrapper label:not(.custom_radio label) {position: absolute; top: -8px; right: 18px; bottom: 0; height: fit-content; margin: auto 0;}
.listing_stepper .seasonal_pricing_step .checkin_checkout_fields_wrapper .inner_section_right_col .time_field_wrapper label i {color: var(--lime-yellow); font-size: 18px;}

/* .listing_stepper .seasonal_pricing_step .price_change_field_wrapper {max-width: 70%; margin: 0 auto} */
.listing_stepper .seasonal_pricing_step .price_change_field_wrapper .txt_field {text-align: left; display: flex; align-items: center; gap: 50px;}
.listing_stepper .seasonal_pricing_step .price_change_field_wrapper .txt_field > label {flex-shrink: 0; margin-bottom: 0; font-size: 24px;}
.listing_stepper .seasonal_pricing_step .price_change_field_wrapper .txt_field .input_icon_wrapper {position: relative; width: 100%;}
.listing_stepper .seasonal_pricing_step .price_change_field_wrapper .txt_field .input_icon_wrapper .radios_wrapper {position: absolute; right: 15px; top: 0; bottom: 0;}
.listing_stepper .seasonal_pricing_step .price_change_field_wrapper .txt_field .input_icon_wrapper i {position: absolute; right: 250px; top: 0; bottom: 0; height: fit-content; margin: auto 0; color: #000; font-size: 20px;}

.listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper .add_price_btn {text-align: right; margin-bottom: 10px}
.listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper .add_price_btn a {color: #4A4A4A; font-family: Poppins; font-size: 15px; font-style: normal; font-weight: 600; line-height: normal; text-transform: capitalize; border-radius: 33px; background: var(--primary-color); padding: 5px 15px; display: inline-block;}

.listing_stepper .seasonal_pricing_step .added_seasonal_prices .added_seasonal_price:not(:last-child) {margin-bottom: 15px;}
.listing_stepper .seasonal_pricing_step .added_seasonal_prices .added_seasonal_price {background: #fff; border-radius: 10px; border: 1px solid lightgray; padding: 10px;}
.listing_stepper .seasonal_pricing_step .added_seasonal_prices .added_seasonal_price label {font-weight: 600; font-family: "Poppins";}
.listing_stepper .seasonal_pricing_step .added_seasonal_prices .added_seasonal_price .price_delete_wrapper {display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;}
.listing_stepper .seasonal_pricing_step .added_seasonal_prices .added_seasonal_price .season_starts_ends_wrapper {display: flex; justify-content: space-between; align-items: center;}

.listing_stepper .add_discounts_step .inner_section_main_col {max-width: 70%; margin: 0 auto}
.listing_stepper .add_discounts_step .discounts_fields_wrapper {max-width: 60%; margin: 0 auto;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount:not(:last-child) {margin-bottom: 15px;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount {display: flex; background: #fff; border-radius: 10px; padding: 15px; gap: 20px; text-align: left; border: 1px solid lightgray; align-items: center;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_field input {width: 70px; padding: 15px 10px;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_details_wrapper {display: flex; justify-content: space-between; align-items: center; flex: 1;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_details_wrapper .discount_description p {margin-bottom: 0;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_details_wrapper .discount_title h6 {color: #4A4A4A; font-size: 20px; font-style: normal; font-weight: 600; text-transform: capitalize; margin-top: 0;}

.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_field {position: relative;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_field i {position: absolute; right: 10px; top: 0; bottom: 0; height: fit-content; margin: auto 0;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_field:has(input:focus) i {color: #000;}

.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_check {display: flex; align-items: center;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_check label {position: relative; width: 20px; height: 20px; cursor: pointer;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_check label:before {content: ""; display: block; width: 100%; height: 100%; border-radius: 5px; border: 1px solid #000; position: absolute; right: 0; top: 0; bottom: 0; margin: auto 0}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_check:has(input:checked) label:after {content: "\f00c"; font-family: "Font Awesome 5 Free"; font-weight: 900; width: 100%; height: 100%; position: absolute; right: 0; top: 0; bottom: 0; margin: auto 0; display: flex; justify-content: center; align-items: center; color: #000;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount .discount_check input {display: none;}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount:has(.discount_check input:checked) {background-color: var(--lime-yellow);}
.listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount:has(.discount_check input:checked) .discount_field input {background-color: #fff;}
/* .listing_stepper .add_discounts_step .discounts_fields_wrapper .single_dicount:has(.discount_check input:checked) * {color: white} */

.listing_stepper .review_details_step .inner_section_left_col .review_card_wrapper {background: #fff; padding: 15px; border-radius: 30px; box-shadow: 0 0 10px #00000005;}
.listing_stepper .review_details_step .inner_section_left_col .review_card_wrapper .cover_image {height: 300px; overflow: hidden; border-radius: 30px;}
.listing_stepper .review_details_step .inner_section_left_col .review_card_wrapper .cover_image img {width: 100%; height: 100%; object-fit: cover; object-position: center;}
.listing_stepper .review_details_step .inner_section_left_col .review_card_wrapper .title_new_wrapper {display: flex; align-items: center; justify-content: space-between;}
.listing_stepper .review_details_step .inner_section_left_col .review_card_wrapper .review_title h6 {color: #4A4A4A; font-family: Poppins; font-size: 25px; font-style: normal; font-weight: 600;}
.listing_stepper .review_details_step .inner_section_left_col .review_card_wrapper .new_label span {color: #4A4A4A; font-family: Poppins; font-size: 18px; font-style: normal; font-weight: 600;}
.listing_stepper .review_details_step .inner_section_left_col .review_card_wrapper .review_price {text-align: left;}
.listing_stepper .review_details_step .inner_section_left_col .review_card_wrapper .review_price span {color: #4A4A4A; font-family: Poppins; font-size: 20px; font-style: normal; font-weight: 500;}
.listing_stepper .review_details_step .inner_section_left_col .review_card_wrapper .review_card_body {padding: 20px 15px 5px;}

.listing_stepper .review_details_step .inner_section_right_col {text-align: left;}
.listing_stepper .review_details_step .inner_section_right_col .icon_confirm_details_wrapper {display: flex; align-items: flex-start; gap: 20px;}
.listing_stepper .review_details_step .inner_section_right_col .icon_confirm_details_wrapper .confirm_detials_content_wrapper .confirmation_title h6 {margin-top: 0; color: #4A4A4A; font-family: Poppins; font-size: 20px; font-style: normal; font-weight: 500;}
.listing_stepper .review_details_step .inner_section_right_col .icon_confirm_details_wrapper .confirm_detials_content_wrapper .confirmation_description p {font-family: Poppins; color: var(--text-grey); font-size: 17px; font-style: normal; font-weight: 500;}
.listing_stepper .review_details_step .inner_section_main_col {max-width: 70%; margin: 0 auto; border-radius: 12px; overflow-y: scroll; overflow-x: hidden; padding: 0 30px;  box-shadow: 0 0 10px -4px #74696921;}

.listing_stepper .review_details_step .listing_amenities .parent-box .box img { object-fit: contain; width: 30px; height: 30px; object-position: center; flex-shrink: 0; }

.review_details_step .sec-2-detail .listing_itinerary .tab-pane ol li:last-child p {margin-bottom: 0;}

.listing_stepper .add_photos_step .drag_drop_photos_wrapper {display: flex; flex-wrap: wrap; gap: 20px 15px; max-height: calc(100vh - 420px); overflow: hidden; overflow-y: auto; padding-right: 8px;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single {flex: 0 0 32.2%; overflow: visible; border-radius: 20px; height: 300px; overflow: hidden;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single img {width: 100%; height: 100%; object-fit: cover; object-position: center;}
.listing_stepper .add_documents_step .drag_drop_photos_wrapper .drag_drop_photo_single canvas {width: 100%; height: 100%; object-fit: contain; object-position: center;}
.listing_stepper .add_documents_step .drag_drop_photos_wrapper .drag_drop_photo_single:has(canvas):before { content: ""; width: 30px; height: 30px; display: block; position: absolute; left: 10px; bottom: 10px; background-image: url('../website/images/pdf_icon.png'); background-position: center; background-repeat: no-repeat; background-size: contain; z-index: 90; }
.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photos_wrapper .drag_drop_photo_single:first-child {flex: 0 0 100%; height: 450px;}
.listing_stepper .add_documents_step .drag_drop_photos_wrapper:not(:has(.drag_drop_photo_single:nth-child(2))) .drag_drop_photo_single:first-child {flex: 0 0 100%; height: 100%;}
.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photos_wrapper:not(:has(.drag_drop_photo_single:nth-child(2))) .drag_drop_photo_single:first-child {height: 100%;}

.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box .add_photo_btn .plus_icon_lbl {display: none}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .photo_icon {display: none;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .plus_icon_lbl {display: block;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single.add_photo_box:not(:first-child) .add_photos_lbl {display: none;}

.listing_stepper #msform .add_photos_step .drag_drop_photos_wrapper .add_photo_box .plus_icon_lbl {background: none;}
.listing_stepper #msform .add_photos_step .drag_drop_photos_wrapper .add_photo_box .plus_icon_lbl i:before {border: 0; font-size: 50px; color: #a4a4a4;}

.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single, .listing_stepper .add_videos_step .video_wrapper .video_single {position: relative;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown, .listing_stepper .add_videos_step .video_wrapper .video_single .delete, .listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single .bulk_del {position: absolute; right: 15px; top: 15px; z-index: 9;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown button, .listing_stepper .add_videos_step .video_wrapper .delete button  {background-color: #ffffffb3; color: #000; border-radius: 50%; box-shadow: 0 0 10px #0000000d; backdrop-filter:blur(2px); padding: 0; width: 35px; height: 35px; font-size: 15px;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu, .listing_stepper .add_videos_step .video_wrapper .video_single .delete .dropdown-menu {border-radius: 15px; box-shadow: 0 0 10px #0000000d; padding: 0;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu li:first-child a, .listing_stepper .add_videos_step .video_wrapper .video_single .delete .dropdown-menu a {border-top-left-radius: 15px; border-top-right-radius: 15px;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single:first-child .dropdown-menu li .make_cover_btn {display: none;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu li:last-child a {border-bottom-left-radius: 15px; border-bottom-right-radius: 15px;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single:first-child .dropdown-menu li a, .listing_stepper .add_videos_step .video_wrapper .delete .dropdown-menu a:hover {border-radius: 15px;}
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu li a {font-family: "Poppins"; font-size: 13px; text-align: left; font-weight: 500; color: #000;}

.listing_stepper .add_videos_step .video_wrapper .video_single { max-width: 30%;}

.listing_stepper .add_photos_step:not(.add_documents_step) .drag_drop_photos_wrapper .drag_drop_photo_single:first-child:not(:last-child):before { content: "Cover Photo"; display: block; position: absolute; top: 10px; left: 10px; background: #fffc; backdrop-filter: blur(5px); padding: 8px 15px; color: #000; font-family: 'Poppins'; font-weight: 500; border-radius: 10px; box-shadow: 0 0 10px #0000000d; font-size: 14px;}

.listing_stepper fieldset.add_photos_step {height: unset; padding-top: 0;}
.listing_stepper .add_documents_step .drag_drop_photos_wrapper .drag_drop_photo_single img[alt="pdfImage"] {object-fit: contain; width: 60%; height: 100%;}
.listing_stepper .add_documents_step .drag_drop_photos_wrapper .drag_drop_photo_single .file-name { display: block; position: relative; top: -22px; font-size: 14px;}

.listing_stepper .key_features_step .saved_features_list_wrapper .single_saved_feature .content_cross_wrapper .note_count span {font-family: "Poppins"; font-size: 14px;}

.listing_stepper .cancellation_policies_step .inner_section_single_category label {text-align: left; align-items: flex-start;}
.listing_stepper .cancellation_policies_step .inner_section_single_category label .description ul {padding-left: 0; list-style: none;}

.listing_stepper .cancellation_policies_step .inner_section_single_category .img_title_wrapper {display: flex; gap: 10px; align-items: center; margin-bottom: 20px;}
.listing_stepper .cancellation_policies_step .inner_section_single_category label {padding: 20px; height: unset; min-height: 150px;}

.listing_stepper .cancellation_policies_step .inner_section_single_category label .category_title h5 {font-weight: 700;}

.listing_stepper #msform .checkIn_checkOut_step .checkin_checkout_fields_wrapper .col_right .inner_section_right_col .time_field_wrapper input {margin-top: 0;}
.listing_stepper .checkIn_checkOut_step .checkin_checkout_fields_wrapper .col_left .inner_section_left_col {margin-top: 8px;}
.listing_stepper .checkIn_checkOut_step .inner_section_main_col .checkin_checkout_fields_wrapper > .row {margin-bottom: 12px;}
.listing_stepper .checkIn_checkOut_step:not(.seasonal_pricing_step) .inner_section_main_col .checkin_checkout_fields_wrapper {margin-top: 35px;}

.listing_stepper .add_documents_step .step_description p a {font-size: 16px; color: var(--blue); font-family: "Poppins";}


/* Youtube Video Screen */

.listing_stepper .add_videos_step .youtubeError { text-align: left; display: block; width: 100%;padding-left: 2px; }
.listing_stepper .add_videos_step .addLink {  position: absolute;  right: 0;  top: 0; bottom: 0; right: 5px; font-size: 12px; font-weight: 500; border-radius: 33px; padding: 0 18px; height: 70%; margin: auto 0;} 
.listing_stepper .add_videos_step .video_wrapper { display: flex; flex-wrap: wrap; gap: 30px; max-height: calc(100vh - 400px); overflow: hidden; overflow-y: auto;}
.listing_stepper .add_videos_step .video_wrapper iframe { border-radius: 20px; height: 240px;}
.listing_stepper .add_videos_step .video_wrapper .delete { text-align: end; padding-right: 10px; }
.listing_stepper .add_videos_step .video_wrapper .delete button { background-color: #fff; color: #000; }

.listing_stepper .add_videos_step .video_wrapper .video-fixed { left: 0; z-index: 1; height:15px; padding: 23px; position: absolute; background: #000; width: calc(100%); border-top-right-radius: 15px; border-top-left-radius: 15px;}
.listing_stepper .add_videos_step .video_wrapper .video-title:is(.video-fixed) { top: 0;  padding: 27px;}
.listing_stepper .add_videos_step .video_wrapper .video-end:is(.video-fixed) { bottom: 0; border-radius: 0; border-bottom-right-radius: 15px; border-bottom-left-radius: 15px;}

/* Experiences stepper style ends here */


/* Progressbar */
.listing_stepper .cust_progress { /* border-top: 4px solid #000; */ position: fixed; bottom: 68px; left: 30px; z-index: 4; border-radius: 4px; width: 96%; height: 25px; overflow: visible; display: flex; gap: 30px; align-items: center; justify-content: space-between; counter-reset: step-counter;}
.listing_stepper .cust_progress_line { background-color: #000; position: absolute; z-index: -1; border-radius: 4px; width: 100%; height: 5px;}
.listing_stepper .cust_progress_bar { height: 14px; width: 14px; border-radius: 50%; cursor: no-drop;  border: 3px solid #000; /*  transform: translate(0, -9px); */ background-color: #fff;}
.listing_stepper .cust_progress_bar.big_circle { height: 22px; width: 22px; /* transform: translate(0, -18px); */ }
.listing_stepper .cust_progress_bar.prev { background-color: var(--black);}
.listing_stepper .cust_progress_bar.active { background-color: var(--primary-color);}
.listing_stepper .cust_progress_bar.active, .listing_stepper .cust_progress_bar.prev { cursor: pointer; }
.listing_stepper .cust_progress_bar.big_circle:before, .listing_stepper .cust_progress_bar:nth-last-child(2):before { counter-increment: step-counter; content: 'Step ' counter(step-counter); display: inline-block; width: 48px; height: 20px; text-align: start; color: var(--light-grey); font-size: 14px; transform: translate(-11px, -26px); font-family: "Poppins";}
.listing_stepper .cust_progress_bar:nth-last-child(2):before { content: 'Submit'; transform: translate(-11px, -26px); font-family: "Poppins";}
.listing_stepper .cust_progress_bar[data-step="1"], .listing_stepper .cust_progress_bar:last-of-type { display: none;}

.listing_stepper:has(fieldset.owner_documents_step.active) .cust_progress { width: 0; opacity: 0; overflow-x: hidden; }

/* To show Listing stepper slide */
.listing_stepper #msform fieldset:not(.active) { display: none;}
/* .listing_stepper #msform fieldset:not(.active) .action-button, .listing_stepper #msform fieldset:not(.active) .action-button-previous  { display: none; }
.listing_stepper #msform fieldset:not(.active) { display: block; opacity: 0; height: 0; transition: opacity .3s ease-in}
.listing_stepper #msform fieldset.active { opacity: 1;transition: opacity .3s ease-in} */

 
 /* Time Slide  */
 .listing_stepper .custom_time_wrapper { min-width: 45%; justify-content: end;}
 .listing_stepper .custom_time_wrapper .duration_wrapper_input { min-width: 45%;}

 .listing_stepper .experience_schedule_duration_step .custom_time_wrapper .duration_wrapper_input {min-width: unset; flex: 30;}
 .listing_stepper .experience_schedule_duration_step .custom_time_wrapper span {flex: 1;}

/* New changes ends here */

/* CK Editor */
.ck.ck-balloon-panel.ck-balloon-panel_position_border-side_right.ck-powered-by-balloon { display: none !important;}


/* Stepper last page listing detail style starts from here */

/* Detail page Old */



.divider { border-bottom: 1px solid var(--border-grey); }
.divider.listing_data { padding: 30px 0;}
.divider.listing_data:nth-of-type(1) { padding-top: 10px;}

.v_divide{position: relative;}
.v_divide:before {  content: '|';  width: 10px;  height: 100%;  z-index: 1;  position: absolute;  top: 0;  left: -6px;  }
.vertical_divider { border-left: 1px solid #BDBDBD; }

.listing_stepper .swiper-button-next, .listing_stepper .swiper-button-prev {  background-color: #fff;  padding: 30px; color: var(--light-grey); border-radius: 50%;}
.listing_stepper .swiper-button-next:after, .listing_stepper .swiper-button-prev:after { font-size: 18px;}
.listing_stepper .mobile .swiper-button-next, .listing_stepper .mobile .swiper-button-prev { padding: 25px;}
.listing_stepper .mobile .swiper-button-next:after, .listing_stepper .mobile .swiper-button-prev:after { font-size: 14px;}

.laptop .sec1_detail .slide_img { height: 40em; width: 100%; overflow: hidden;border-radius: 20px; }
.laptop .sec1_detail .slides_img { height: 9em; }
.mobile .sec1_detail .slide_img { height: 20em; width: 100%; overflow: hidden; border-radius: 20px; }
.mobile .sec1_detail .slides_img { height: 7em; }
.sec1_detail .slide_img img, .sec1_detail .slides_img img { height: 100%; width: 100%; object-fit: cover; }
.sec1_detail .swiper-slide img{ border-radius: 20px ; height: 98%; object-fit: cover;}
.sec1_detail .info, .sec2_detail .book .info{  border-radius: 20px; box-shadow: 0 4px 44px 0 rgba(0, 0, 0, 0.05);}
.sec1_detail .info { min-height: 315px;}
.sec1_detail .info.medical_info .company_info p { max-width: 300px;}

.sec2_detail .categories{ padding-left: 0;  flex-wrap: wrap;  }
.sec2_detail .book h5 { padding-inline: 0;}
/* .sec-2-detail .col-md-12 p { display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;} */
.sec-2-detail .col-md-12 p { word-break: break-all;}
.sec2_detail .pay_info ul {padding-inline: 0;}
.sec2_detail .pay_info li{ padding-bottom: 10px;  list-style: none;  }
.sec2_detail .button.reserve{padding: 12px 35px;}
.sec2_detail .fa-check-square{color: var(--light-yellow); font-size: 18px;}

/* .sec2_detail ol { counter-reset: item; position: relative;}
.sec2_detail ol>li{  position: relative;  list-style: none; counter-increment: item;}
.sec2_detail ol>li::before, .sec2_detail ol>li::after, .sec2_detail ol::after{ content: ""; position: absolute; left: -50px;  top: 0;}
.sec2_detail ol>li::before{  content: counter(item) '';  z-index: 2; color: var(--white);  left: -41px;  top: 2px;  }
.sec2_detail ol>li:nth-child(1)::before, .sec2_detail ol>li:last-child::before{ content: '';}
.sec2_detail ol>li::after {  width: 30px;  height: 30px;  display: inline-block;  background-color: var(--light-grey);  border-radius: 50%;  }
.sec2_detail ol>li:nth-child(1)::after, .sec2_detail ol>li:last-child::after{  content: "\f3c5";  font-family: "Font Awesome 5 Free";  width: 40px;  height: 40px;  padding: 6px 8px 8px 14px;  left: -55px;  background-color: var(--light-yellow);  }
.sec2_detail ol::after { content: '|';  background-image: url(../images/Lines.png);  background-repeat: no-repeat; background-size: 3px 93%;  display: block;  width: 100%;  height: 100%;  left: 12px;  top: 2px;  z-index: -1;  } */

.sec2_detail ol { counter-reset: item; position: relative;}
.sec2_detail ol>li{  position: relative;  list-style: none; counter-increment: item;}
.sec2_detail ol>li::before, .sec2_detail ol>li::after, .sec2_detail ol::after{ content: ""; position: absolute; left: -50px;  top: 0;}
.sec2_detail ol>li::before { content: counter(item) ''; z-index: 2; color: var(--white); left: -48px; top: 2px; background-color: var(--light-grey); width: 35px; height: 35px; border-radius: 50%; display: flex; justify-content: center; align-items: center; }
.sec2_detail ol>li:not(:last-child)::after { content: ""; background-image: url(../website/images/timeline_line_dot.svg); background-repeat: repeat; height: 100%; width: 5px; left: -33px; top: 10px; background-size: contain;}

.review_details_step .sec2_detail ol>li::before {color: white; font-weight: 800;}

.review_details_step .tabs .nav-tabs .nav-item .nav-link.active {
  border: 2px solid #FED402;
  background-color: #FED402;
}

.review_details_step .tabs .nav-tabs .nav-item .nav-link {
  border: 2px solid transparent;
  border-radius: 8px;
  background-color: #EBEBEB;
  padding-inline: 25px;
  cursor: pointer;
  color: #000;
}

/* .review_details_step .sec2_detail ol>li:last-child::after { content: ""; background-image: url(../images/timeline_line_dot.png); background-repeat: repeat; height: 100%; width: 2px; left: -32px; top: 10px; background-color: black; } */
.review_details_step .sec2_detail .custom_btns .end_time {position: relative;}
.review_details_step .sec2_detail .custom_btns .end_time:before {content: ""; left: 16px; top: 0; bottom: 0; margin: auto 0; position: absolute; display: block; background-color: black; height: 2px; width: 25px;}
.review_details_step .sec2_detail .custom_btns .end_time:after {content: ""; left: 16px; top: 0; bottom: 18px; margin: auto 0; position: absolute; display: block; background-color: black; height: 20px; width: 2px;}

.review_details_step .sec2_detail .custom_btns .start_time {position: relative;}
.review_details_step .sec2_detail .custom_btns .start_time:before {content: ""; left: 16px; top: 0; bottom: 0; margin: auto 0; position: absolute; display: block; background-color: black; height: 2px; width: 25px;}
.review_details_step .sec2_detail .custom_btns .start_time:after {content: ""; left: 16px; top: 47px; bottom: 0; margin: auto 0; position: absolute; display: block; background-color: black; height: 70px; width: 2px;}
.review_details_step .sec2_detail ol>li:first-child:before, .review_details_step .sec2_detail ol>li:last-child:before {content: "\f3c5"; font-family: "Font Awesome 5 Free"; font-weight: 900; color: #000; background-color: #fed402;}

.sec2_detail .book .info .book_timing { width: 45%; }

/* Listing Detail page style starts from here */

/* Box style */
.sec-2-detail .listing_data .parent-box .box { border-radius: 12px; border: 1px solid var(--comment-grey); padding: 10px 20px; min-height: 45px; font-size: 12px;} 
.sec-2-detail .listing_data .parent-box.parent-feature .box { flex-grow: 1;}
.sec-2-detail .listing_data:has(.listing_data_heading) .parent-box { padding-top: 15px;}
.sec-2-detail .listing_data .parent-box .box span:first-letter {  text-transform: capitalize;}

.sec-2-detail .listing_data.listing_amenities .parent-box, .modal.all-amenties .parent-box { gap: 15px;} 
.sec-2-detail .listing_data.listing_amenities .parent-box .box { width: calc(100% - 50px); border: 0; } 
.sec-2-detail .listing_data ol.parent-box  { list-style-position: inside; } 
.sec2_detail .button.reserve:not([disabled]) { background-color: var(--btn-yellow); border-color: var(--btn-yellow);}

/* All amenties Modal */
.modal.all-amenties .modal-body { max-height: 70vh; overflow: scroll;}

/* Feature Box style */
/* .sec-2-detail .listing_data .parent-box.parent-feature .box { max-width: 330px; flex: 1;} */


.cancelation_policy_timeline_wrapper, .cancelation_policy_timeline_wrapper .timeline_step { margin: 0; padding: 0; color: #000; font-family: verdana; font-size: 14px; }
.cancelation_policy_timeline_wrapper { display: flex; max-width: 800px; width: 96%; justify-content: space-between; position: relative; padding: 70px 0; margin-left: 10px;}
.cancelation_policy_timeline_wrapper:after { content: ''; display: block; width: 100%; height: 3px; background-color: #000; position: absolute; top: 50%; left: 0; transform: translate(0, -50%); z-index: 1; }
.cancelation_policy_timeline_wrapper .timeline_step { list-style-type: none; position: relative; cursor: pointer; z-index: 2; }
.cancelation_policy_timeline_wrapper .timeline_step:after { content: ''; display: block; width: 18px; height: 18px; border-radius: 50%; border: 2px solid #000; }
.cancelation_policy_timeline_wrapper .timeline_step .refund_date { position: absolute; top: calc(100% + 20px); left: 50%; transform: translate(-50%, 0); display: inline-block; text-align: center; width: calc(800px / 8); }
.cancelation_policy_timeline_wrapper .timeline_step:first-child:after {background-color: #000;}
.cancelation_policy_timeline_wrapper .timeline_step:after {background-color: #fff;}
.cancelation_policy_timeline_wrapper .timeline_step:last-child:after {background-color: #fff; border: 2px solid #929292;}
.cancelation_policy_timeline_wrapper:has(.timeline_step:nth-child(4)) .timeline_step:first-child:before {content:"Full Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 3);}
.cancelation_policy_timeline_wrapper:has(.timeline_step:nth-child(4)) .timeline_step:nth-child(2):before {content:"Partial Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 3);}
.cancelation_policy_timeline_wrapper:has(.timeline_step:nth-child(4)) .timeline_step:nth-child(3):before {content:"No Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 3);}
.cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(4))) .timeline_step:first-child:before {content:"Full Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 2);}
.cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(4))) .timeline_step:nth-child(2):before {content:"No Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px / 2);}
.cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(3))) .timeline_step:first-child:before {content:"No Refund"; position: absolute; bottom: calc(100% + 20px); left: 50%; transform: translate(0%, 0); display: inline-block; text-align: center; width: calc(800px);}
.cancelation_policy_timeline_wrapper:not(:has(.timeline_step:nth-child(3))) .timeline_step:nth-child(2):before {content: ""}
.listing_cancelation .timeline_detailed_wrapper { max-width: 820px; width: 96%; margin-top: 20px;}
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single:not(:last-child) { border-bottom: 1px solid lightgray; margin-bottom: 30px;}
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single { display: flex; align-items: flex-start; padding-bottom: 15px;}
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .timeline_date { flex: 2; }
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .description { flex: 12; }
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .timeline_date .date_tag h5, .listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .description h5 { font-weight: 600; font-size: 14px;}
.listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .timeline_date .date_tag span, .listing_cancelation .timeline_detailed_wrapper .timeline_detail_single .description p { font-size: 12px; margin-bottom: 0;}

.listing_cancelation .details-box .icon {width: 21px; height: 21px; display: flex; align-items: center;}
.listing_cancelation .details-box .icon img { width: 100%; height: 100%;}

body .divider.listing_data.listing_cancelation {padding: 30px 0 10px 0;}

.listing_cancelation .details-box .box {flex-wrap: nowrap;}

.laptop .listing_gallery .slide_img {height: 620px; border-radius: 20px; overflow: hidden;}
.laptop .listing_gallery .slide_img img {height: 100%;}

.listing_meta .rating *:not(:last-child) {flex-shrink: 0;}
.listing_meta .report-listing img {width: 13px; height: auto; object-fit: contain; object-position: center;}
.listing_meta .report-listing {text-decoration: none;}
.listing_meta .report-listing span {font-size: 14px; text-decoration: underline; color: #5e5e5e;}

/* .listing_map #map_location .gmnoprint:not(:has(.gm-control-active)), .listing_map #map_location .gm-fullscreen-control {display: none;} */
.listing_map #map_location .gm-style-cc, .listing_map #map_location .gm-svpc {display: none;}
.listing_map #map_location a:has(img[alt="Google"]) {display: none !important;}

.location_map_wrapper .gm-style-iw-c[role="dialog"] .gm-style-iw-chr {position: absolute; top: 0; right: 0;}
.location_map_wrapper .gm-style-iw-c[role="dialog"] .gm-style-iw-d {padding-top: 15px;}
.location_map_wrapper .gm-style-iw-c[role="dialog"] .gm-style-iw-chr .gm-style-iw-ch {display: none;}
.location_map_wrapper .gm-style-iw-c[role="dialog"] .gm-style-iw-chr button {width: fit-content !important; height: fit-content !important;}
.location_map_wrapper .gm-style-iw-c[role="dialog"] .gm-style-iw-chr button span {margin: 0 !important; width: 20px !important; height: 20px !important;}

.listing_rule .box img {width: 20px; height: 20px; object-fit: contain; object-position: center;}

.sec1_detail .user_img { height: 70px; width: 70px; overflow: hidden; border-radius: 50%;}
.mobile .sec1_detail .user_img { height: 50px; width: 50px;}
.sec1_detail .user_img img { height: 100%;}

.listing_stepper .mobile.tab-pane p:not(.error p)  { font-size: 12px; line-height: 24px;}
.listing_stepper .mobile .button { padding: 8px 16px; font-size: 12px;}
.listing_stepper .mobile { /* border: 2px solid #000; zoom: 0.7;*/ border-radius: 10px; max-height: 100%; overflow-y: scroll; overflow-x: hidden; margin-inline: 20px; /* box-shadow: 0 0 10px -4px #74696921; */ }
.listing_stepper .review_details_step .inner_section_main_col:has(.mobile.active) { box-shadow: none; overflow: hidden;}

.review_details_step .preview_wrapper_main .phone_frame {width: 100%;}
.review_details_step .preview_wrapper_main #mobile .sec1_detail, .review_details_step .preview_wrapper_main #mobile .sec-2-detail {zoom: 0.6;}

.mobile .fs-22, .mobile h3 { font-size: 16px;}

.listing_stepper .review_details_step .parent_tabs {margin-bottom: 10px;}
.listing_stepper .review_details_step .parent_tabs .nav-link {border: 1px solid #000; border-radius: 5px;}
.listing_stepper .review_details_step .parent_tabs .nav-link.active {background-color: #ffce32;}

.mockup-container {position: relative; width: 430px; height: 620px; transform: scale(0.8); opacity: 0; zoom: 0.65; margin: 0 auto; margin-top: 15px; }

.html-content {position: absolute; top: 85px; left: 33px; width: 370px; height: 750px; background-color: white; overflow: auto; clip-path: inset(0 0 0 0); overflow-x: hidden; border-bottom-left-radius: 50px; border-bottom-right-radius: 50px; padding-inline: 10px;}

/* Preview with heading and desc */
/* .mockup-container { width: 385px; }
.html-content { width: 328px; height: 668px;} */


/* Listing Detail page style ends here */

/* Stepper last page listing detail style ends here */

/* Listing Loader style */

#list_preview_loader {width: 100%; /*height: 100vh; position: fixed; top: 0; left: 0; z-index: 99999;*/ text-align: center; display: flex; justify-content: center; align-items: center; flex-direction: column;} 
#list_preview_loader .load { width: 150px; height: 150px;} 
#list_preview_loader .load img { width: 100%; height: 100%; object-fit: contain; object-position: center;}
#list_preview_loader .step_description {margin-top: 20px; margin-bottom: 120px;}
/* #list_preview_loader .step_description p {font-size: 22px;} */

/* Listing Loader style ends */


/* Watercraft stepper style starts from here */

.listing_stepper .basic_watercraft_step .watercraft_boat_length_field input {border: 1px solid #9b9b9b; height: 42px; padding: 0 10px;}
.listing_stepper .basic_watercraft_step .watercraft_boat_length_field span {color: #000;}
.listing_stepper .set_price_step .inner_section_main_col .radios_wrapper {justify-content: center;}

/* Watercraft stepper style ends here */

/* Save & Exit */

.stepper:has(#step-1.active) .save-btn.save_and_exit_btn, .stepper:has(fieldset.active:last-of-type) .save-btn.save_and_exit_btn { display: none;}

.listing_stepper fieldset.active .next.loading { cursor: no-drop;}
/* fieldset:after { content: '. . .'; color: #000; background-color: #fff; height: 100%; width: 100%; display: none; position: absolute; z-index: 999; font-size: 28px; bottom: -4px; right: 44px; opacity: 1; animation: 1.2s loading infinite; } */
fieldset.active:has(.next.loading) { opacity: 0.4;}

@keyframes loading {
  from { opacity:0; transition: 0.3s ease-in;}
  to { opacity:1; transition: 0.3s ease-in;}
}

/* Alerts */

div:where(.swal2-container) * { font-family: 'Poppins';}
div:where(.swal2-container) div:where(.swal2-popup) { border-radius: 22px !important;}
div:where(.swal2-icon).swal2-warning, div:where(.swal2-icon).swal2-success .swal2-success-ring{  border-color: var(--primary-color) !important; color: var(--primary-color) !important; background-color: var(--primary-color) !important;}
div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm { background-color: var(--primary-color) !important; font-family: 'Poppins'; color: #000 !important; border-radius: 7px !important; padding: 13px 40px 13px 40px;}
div:where(.swal2-container) button:where(.swal2-styled).swal2-cancel { background-color: #000 !important; color: #fff;  border-radius: 7px !important; padding: 13px 40px 13px 40px ;}
div:where(.swal2-container) button:where(.swal2-styled).swal2-default-outline:focus, div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm:focus { box-shadow: none !important;}
div:where(.swal2-container) button:where(.swal2-styled).swal2-confirm { background-color: var(--primary-color); font-family: 'Poppins';}
.swal2-actions button { border-radius: 7px; font-family: 'Poppins'; }
div:where(.swal2-icon).swal2-success [class^=swal2-success-line] { background-color: #fff !important; z-index: 4 !important;}
div:where(.swal2-icon).swal2-error { background-color: var(--primary-color) !important; border-color: var(--primary-color) !important;}
div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line] { background-color: #fff !important;}
.swal2-timer-progress-bar { background-color: var(--primary-color) !important;}
body div:where(.swal2-container) div:where(.swal2-loader) { border-top-color: var(--primary-color); border-bottom-color: var(--primary-color);}
body div:where(.swal2-icon).swal2-info { border-color: var(--primary-color); background-color: var(--primary-color); color: var(--primary-color);}
body div:where(.swal2-container) div:where(.swal2-html-container) { font-size: 14px;}
body div:where(.swal2-icon) .swal2-icon-content { color: #fff;}


.listing_amenities .amenity-data { font-size: 15px;}
.listing_amenities .amenity-data .amenties_desc:not(.error p) { font-size: 12px; display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden; line-height: 1.2;}
.detail_contact_host .detail_contact_host_btn { background-color: var(--primary-color); border-color: var(--primary-color); }
.detail_contact_host .detail_contact_host_btn:hover { background-color: #fff; border-color: transparent; }


/* Input type time flat picker style starts from here */

.flatpickr-calendar {border-radius: 10px; overflow: hidden; box-shadow: 6px 6px 10px rgba(0,0,0,0.2); border: 0.5px solid black;}
.flatpickr-calendar .flatpickr-time {background-color: #ffce32;}
.flatpickr-calendar .flatpickr-time .numInput:hover, .flatpickr-calendar .flatpickr-time .numInput:focus, .flatpickr-calendar .flatpickr-time .flatpickr-am-pm:focus, .flatpickr-calendar .flatpickr-time .flatpickr-am-pm:hover {background-color: #ffce32; box-shadow: inset 20px 20px 35px #ffce32;}
.flatpickr-calendar .flatpickr-time .numInput, .flatpickr-calendar .flatpickr-time .flatpickr-am-pm {font-family: "poppins"; font-weight: 700;}

/* Input type time flat picker style ends from here */

/* map options fixings */

.review_details_step .listing_map #map_location .gm-style-mtc-bbw .gm-style-mtc:first-of-type>button { border-radius: 10px 0 0 10px;}
.review_details_step .listing_map #map_location .gm-style-mtc-bbw .gm-style-mtc:last-of-type>button { border-radius: 0 10px 10px 0;}
.review_details_step .listing_map #map_location button.gm-control-active.gm-fullscreen-control, .listing_map #map_location .gmnoprint button[aria-label="Zoom in"], .listing_map #map_location .gmnoprint button[aria-label="Zoom out"], .listing_map #map_location .gmnoprint button.gm-control-active { border-radius: 10px !important;}

.review_details_step .listing_map #map_location .gm-svpc {border-radius: 10px !important; display: inline-block;}
.review_details_step .listing_map #map_location .gm-style-cc {display: none;}
/* .listing_map #map_location a:has(img[alt="Google"]) {display: none !important;} */
.review_details_step .gm-bundled-control .gmnoprint:has([class*="gm-control"]) > div {border-radius: 10px !important;}


/* Image Uploader */

.fs-12 { font-size: 12px;}
.listing_stepper .black_btn { background-color: #000; color: #fff; border-radius: 10px;}
.listing_stepper .image_uploader label.btn-add { font-size: 30px; cursor: pointer; opacity: 0;}
.listing_stepper .image_uploader .button { border-radius: 10px; min-width: 125px; }
.listing_stepper .image_uploader .image-upload_wrapper { border: 1px dashed; border-radius: 7px; padding: 4em 1em; cursor: pointer;}

.listing_stepper .image_uploader .image-preview:has(.error-message) img { filter: brightness(0.35); }
.listing_stepper .image_uploader .modal-content .modal-body #imagePreview { max-height: 28em; overflow: scroll; }

.review_details_step #mobile .listing_custom_meta ul {flex-wrap: wrap;}
.review_details_step #mobile .listing_custom_meta ul li {flex: 0 0 calc(50% - 8px); box-sizing: border-box;}

.button1 { background-color: var(--primary-color); border-color: transparent;}

.listing_stepper .stepper .drag_drop_photo_single .doc_name { position: absolute; font-size: 12px; bottom: -30px; left: 0; right: 0; margin: 0 auto;}
.listing_stepper .stepper .checkin_title label { font-size: 18px;}


.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single .bulk_del { left: 16px; right: unset; }
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single .bulk_del input { padding: 5px; }

/* .listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single { border: 4px solid transparent;} */
.listing_stepper .add_photos_step .drag_drop_photos_wrapper .drag_drop_photo_single:has(.bulk_del input:checked) { border: 4px var(--primary-color);}

.modal-backdrop.fade.show:empty { display: none;}


/* Experiences Stepper style starts from here */

/* Basics of your experiences step */

.basics_experience_step.accommodations_rules_step  .inner_section_main_col {max-width: 100%; margin: unset;}
.basics_experience_step.accommodations_rules_step .space_detail_single .allow_pets_field_wrapper {width: 100%;}
.basics_experience_step.accommodations_rules_step .space_detail_single .allow_pets_field_wrapper {width: 100%; display: flex; justify-content: space-between; align-items: center;}
.basics_experience_step.accommodations_rules_step .space_detail_single .allow_pets_input_wrapper {margin-top: 0;}
.listing_stepper .accommodations_rules_step.basics_experience_step .allow_pets_field_wrapper .allow_pets_input_wrapper .allow_pets_input label {width: 216px;}

/* Basics of your experiences step */

/* Schedule & Duration step */

.listing_stepper .watercraft_schedule_duration_step .inner_section_main_col {max-width: 100%; margin: unset;}
.watercraft_schedule_duration_step .custom_multiple .space_detail_quantity_wrapper select {height: 42px;}
.watercraft_schedule_duration_step .custom_multiple .all_days {padding-top: 20px;}

/* Schedule & Duration step */

/* Itinerary Stops step style starts from here */

.listing_stepper .watercraft_itenary_step .features_add_btn_wrapper {justify-content: flex-end;}
.listing_stepper .watercraft_itenary_step .itinerary_multiple_days_dropdown select {border-radius: 12px; border: .5px solid #9b9b9b; background: #FFF; height: 42px; padding-inline: 10px; color: #000;}

.listing_stepper .watercraft_itenary_step .itinerary_day_wrapper {border: .5px dashed #9b9b9b; border-radius: 15px; padding: 15px;}
.listing_stepper .watercraft_itenary_step .itinerary_day_wrapper:not(:last-child) {margin-bottom: 30px;}
.listing_stepper .watercraft_itenary_step .itinerary_day_wrapper .itinerary_day_title h5 {text-align: left; font-size: 20px; font-weight: 700;}

.listing_stepper .experience_language_step .inner_section_single_category label .category_icon_wrapper img {filter: unset;}

/* Itinerary Stops step style ends here */

.basics_experience_step .allow_pets_field_wrapper .space_detail_title, .basics_experience_step .space_detail_single .allow_pets_title {padding-right: 35px;}

.listing_stepper .experience_schedule_duration_step .space_details_wrapper .space_detail_quantity_wrapper{position: relative;}
.listing_stepper .experience_schedule_duration_step .space_details_wrapper .space_detail_quantity_wrapper:before { content: "\f078"; position: absolute; right: 10px; top: 0; bottom: 0; margin: auto 0; font-size: 15px; font-family: 'FontAwesome'; font-weight: 100; width: 25px; height: fit-content; transition: .4s ease; text-align: center; color: #6d6d6d; }
.listing_stepper .experience_schedule_duration_step .space_details_wrapper .space_detail_quantity_wrapper.open:before {content: "\f078"; transform: rotate(180deg); transition: .4s ease;}

.listing_stepper .experience_itenary_step .itinerary_multiple_days_dropdown .itinerary_days_wrapper{position: relative;}
.listing_stepper .experience_itenary_step .itinerary_multiple_days_dropdown .itinerary_days_wrapper:before { content: "\f078"; position: absolute; right: 10px; top: 0; bottom: 0; margin: auto 0; font-size: 15px; font-family: 'FontAwesome'; font-weight: 100; width: 25px; height: fit-content; transition: .4s ease; text-align: center; color: #6d6d6d; }
.listing_stepper .experience_itenary_step .itinerary_multiple_days_dropdown .itinerary_days_wrapper.open:before {content: "\f078"; transform: rotate(180deg); transition: .4s ease;}

/* Experiences Stepper style ends here */

.listing_itinerary .tabs .nav-tabs:not(:has(li:nth-child(2))) { display: none;}


/* Seasonal Pricing formatted date style starts from here */

.listing_stepper #msform .seasonal_pricing_step .time_field_wrapper input {color: white;}
.listing_stepper #msform .seasonal_pricing_step .time_field_wrapper {position: relative;}
.listing_stepper #msform .seasonal_pricing_step .time_field_wrapper label.foramted_display_time {position: absolute; top: 9px; height: fit-content; left: 15px; font-size: 17px; font-weight: 500; font-family: "Poppins"; color: black; right: unset; margin: unset;}

/* Seasonal Pricing formatted date style ends here */

.experience_itenary_step .inner_section_main_col:has(.itinerary_multiple_days_dropdown[style*="display: none;"]) .features_add_btn_wrapper {justify-content: end !important;}
.experience_itenary_step .inner_section_main_col:has(.itinerary_multiple_days_dropdown[style*="display: none;"]) .features_add_btn_wrapper label {display: none;}

.set_price_children_step .disclaimer { display: none;}
.set_price_children_step:has(#price_children.valid_error) .disclaimer { display: block;}

.important_notes_step .features_add_btn_wrapper .add_note {text-transform: lowercase;}

.pac-container:after {display: none;}