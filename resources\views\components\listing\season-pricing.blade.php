@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "season-price");
@endphp
<div class="row">
    <div class="col-md-12">
        <div class="inner_section_main_col">
            <div class="main_step_title">
                <h2>{{ $step_data->title ?? "" }}</h2>
            </div>
            @isset($step_data->sub_title)
                <div class="step_description">
                    <p>{{ $step_data->sub_title ?? "" }}</p>
                </div>
            @endisset
            <div class="checkin_checkout_price_main_wrapper scrollable-section">
                <div class="checkin_checkout_fields_wrapper">
                    <div class="add_price_btn">
                        <a class="add_feature_btn cust_add" href="javascript:void(0)">{{ translate('stepper.add') }}</a>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-md-5 col-sm-4 col_left">
                            <div class="inner_section_left_col">
                                <div class="checkin_title">
                                    <label for="">{{ translate('stepper.season_start') }}</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-7 col-sm-8 col_right">
                            <div class="inner_section_right_col">
                                <div class="time_field_wrapper">
                                    <input type="text" name="season_start"
                                        value="{{ $listing->detail->season_start ?? '' }}"
                                        class="datepicker group_discount no_validate" id="session_start">
                                    <label for="session_start" class="form-label"><i
                                            class="fas fa-calendar-alt"></i></label>
                                            <label for="session_start" class="foramted_display_time" id="session_start_display"></label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-md-5 col-sm-4 col_left">
                            <div class="inner_section_left_col">
                                <div class="checkin_title">
                                    <label for="">{{ translate('stepper.season_end') }}</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-7 col-sm-8 col_right">
                            <div class="inner_section_right_col">
                                <div class="time_field_wrapper">
                                    <input type="text" name="season_end"
                                        class="datepicker group_discount no_validate"
                                        value="{{ $listing->detail->season_end ?? '' }}" id="session_end">
                                    <label for="session_end" class="form-label"><i
                                            class="fas fa-calendar-alt"></i></label>
                                            <label for="session_end" class="foramted_display_time" id="session_end_display"></label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-md-5 col-sm-4 col_left">
                            <div class="inner_section_left_col">
                                <div class="checkin_title">
                                    <label for="" class="form-label">{{ translate('stepper.price') }}</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-7 col-sm-8 col_right">
                            <div class="inner_section_right_col txt_field">
                                <div
                                    class="input_icon_wrapper d-flex align-items-center justify-content-between gap-1 b-radius border-grey pe-2">
                                    <input type="number"
                                        class="group_discount percent seasonal_price_val no_validate border-0 py-0 m-0 fs-12"
                                        name="price_change" id=""
                                        value="{{ $listing->detail->price_change ?? '' }}" placeholder="00"
                                        min="-100" max="" pattern="^[1-9]\d*$"
                                        {{-- onKeyPress="if(this.value.length==4) return false;" --}}
                                        >
                                    <i class="fas fa-percent text-black"></i>
                                    <div class="radios_wrapper">
                                        <div class="custom_radio">
                                            <label title="Include Price"
                                                for="chk_seasonal_pricing_include">{{ translate('stepper.increase') }}</label>
                                            <input class="price_type_radio" type="radio"
                                                id="chk_seasonal_pricing_include" name="season_type" value="increase"
                                                {{ ($listing->detail->season_type ?? '') == 'increase' ? 'checked' : 'checked' }}>
                                        </div>
                                        <div class="custom_radio">
                                            <label title="Exclude Price"
                                                for="chk_seasonal_pricing_exclude">{{ translate('stepper.decrease') }}</label>
                                            <input class="price_type_radio" type="radio"
                                                id="chk_seasonal_pricing_exclude" name="season_type" value="decrease"
                                                {{ ($listing->detail->season_type ?? '') == 'decrease' ? 'checked' : '' }}>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="price_change_field_wrapper">
                    <div class="added_seasonal_prices">
                        @php
                            $listing_price = 0;
                        @endphp
                        @if ($category->id == 1)
                            @foreach ($listing->seasons ?? [] as $season)
                                {{-- tour season --}}
                                <div class="added_seasonal_price">
                                    <div class="price_delete_wrapper">
                                        <div class="price_change">
                                            <label for="">{{ translate('stepper.price') }} <span class="increase_decrease">
                                                <input type="hidden" name="seasons[{{ $loop->index }}][type]" value="{{ $season->type }}">
                                                {{ $season->type }}
                                            </span>: 
                                            <span class="percent_figure">
                                                <input type="hidden" name="seasons[{{ $loop->index }}][percentage]" value="{{ $season->percentage }}">
                                                {{ $season->percentage }}%
                                            </span> 
                                            </label>
                                        </div>
                                        <div class="cross_icon_wrapper">
                                            <a class="remove_seasonal_price_btn" href="javascript:void(0)">
                                                <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="new_price text-start">
                                        @php 
                                            $adult_price_discount =  (float) get_percentage($season->percentage, $listing->detail->adult_price )
                                        @endphp
                                        <label for="">
                                            @if($listing->detail->private_booking == "yes" && $listing->detail->child_allow == "no")
                                                {{ translate('stepper.new_price_guest') }}: 
                                            @elseif ($listing->detail->private_booking == "no" && $listing->detail->child_allow == "no")
                                                {{ translate('stepper.new_price_guest') }}: 
                                            @else
                                                {{ translate('stepper.new_price_adult') }}: 
                                            @endif
                                        </label> 
                                        <del class="fs-14">$ {{ number_format($listing->detail->adult_price) }} {{ translate('stepper.cop') }}</del>
                                        <span class="px-2 semi-bold fs-14">${{ number_format($listing->detail->adult_price - $adult_price_discount) }} {{ translate('stepper.cop') }}</span>
                                    </div>
                                    @if ($listing->detail->child_allow == "yes")
                                        <div class="new_price text-start">
                                            @php 
                                                $child_price_discount =  (float) get_percentage($season->percentage, $listing->detail->child_price)
                                            @endphp
                                            <label for="">{{ translate('stepper.new_price_child') }}: </label> 
                                            <del class="fs-14">${{ number_format($listing->detail->child_price) }} {{ translate('stepper.cop') }}</del>
                                            <span class="px-2 semi-bold fs-14">${{ number_format($listing->detail->child_price - $child_price_discount) }} {{ translate('stepper.cop') }}</span>
                                        </div>
                                    @endif
                                    @if ($listing->detail->private_booking == "yes")
                                        <div class="new_price text-start">
                                            @php
                                                $private_booking_price_discount =  (float) get_percentage($season->percentage, $listing->detail->private_booking_price)
                                            @endphp
                                            <label for="">{{ translate('stepper.new_price_private') }}: </label> 
                                            <del class="fs-14">${{ number_format($listing->detail->private_booking_price) }} {{ translate('stepper.cop') }}</del>
                                            <span class="px-2 semi-bold fs-14">${{ number_format($listing->detail->private_booking_price - $private_booking_price_discount) }} {{ translate('stepper.cop') }}</span>
                                        </div>
                                    @endif
                                    <div class="season_starts_ends_wrapper">
                                        <div class="season_starts">
                                            <input type="hidden" name="seasons[{{ $loop->index }}][start_date]" value="{{ $season->start_date }}">
                                            <label for="">{{ translate('stepper.season_starts') }}: {{ \Carbon\Carbon::parse($season->start_date)->format('d/M/Y') }}</label>
                                        </div>
                                        <div class="season_ends">
                                            <input type="hidden" name="seasons[{{ $loop->index }}][end_date]" value="{{ $season->end_date }}">
                                            <label for="">{{ translate('stepper.season_ends') }}: {{ \Carbon\Carbon::parse($season->end_date)->format('d/M/Y') }}</label>
                                        </div>
                                    </div>
                                </div>
                                {{-- tour season end --}}
                            @endforeach
                        @else
                            @foreach ($listing->seasons ?? [] as $season)
                                <div class="added_seasonal_price">
                                    <div class="price_delete_wrapper">
                                        <div class="price_change">
                                            <label for="">Price <span class="increase_decrease">
                                                    <input type="hidden" name="seasons[{{ $loop->index }}][type]"
                                                        value="{{ $season->type }}">
                                                    {{ $season->type }}
                                                </span>:
                                                <span class="percent_figure">
                                                    <input type="hidden"
                                                        name="seasons[{{ $loop->index }}][percentage]"
                                                        value="{{ $season->percentage }}">
                                                    {{ $season->percentage }}%
                                                </span>
                                            </label>
                                        </div>
                                        <div class="cross_icon_wrapper">
                                            <a class="remove_seasonal_price_btn" href="javascript:void(0)">
                                                <i class="fa fa-trash" aria-hidden="true"
                                                    style="font-size: 16px; color: red;"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="new_price text-start">
                                        <label for="">{{ translate('stepper.new_price') }}: </label>
                                        @php
                                            $listing_price = $listing->base_price ?? 0;
                                        @endphp
                                        <del class="fs-14">${{ number_format($listing_price) }}</del>
                                        <span class="px-2 semi-bold fs-14">
                                            @if (isset($season->percentage, $listing_price))
                                                @php
                                                    $season_discount = (float) get_percentage(
                                                        $season->percentage,
                                                        $listing_price,
                                                    );
                                                @endphp
                                                @if ($season->type == 'Increase')
                                                    ${{ number_format($listing_price + $season_discount, 2) }}
                                                @else
                                                    ${{ number_format($listing_price - $season_discount, 2) }}
                                                @endif
                                            @endif
                                            {{ translate('stepper.cop') }}
                                        </span>
                                    </div>

                                    <div class="season_starts_ends_wrapper">
                                        <div class="season_starts">
                                            <input type="hidden" name="seasons[{{ $loop->index }}][start_date]"
                                                value="{{ $season->start_date }}">
                                            <label for="">{{ translate('stepper.season_starts') }}: {{ $season->start_date }}</label>
                                        </div>
                                        <div class="season_ends">
                                            <input type="hidden" name="seasons[{{ $loop->index }}][end_date]"
                                                value="{{ $season->end_date }}">
                                            <label for="">{{ translate('stepper.season_ends') }}: {{ $season->end_date }}</label>
                                        </div>
                                    </div>
                                </div>
                            @endforeach

                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('js')
    <script type="text/javascript">
        let disabledRanges = [];

        @foreach ($listing->seasons ?? [] as $season)
            disabledRanges.push({
                start: "{{ $season->start_date }}",
                end: "{{ $season->end_date }}"
            });
        @endforeach

        document.addEventListener("DOMContentLoaded", function() {
            updateDatepickers();
        });

        function isDateDisabled(date) {
            let formattedDate = $.datepicker.formatDate('mm/dd/yy', date);
            return disabledRanges.some(range => formattedDate >= range.start && formattedDate <= range.end);
        }

        function isRangeOverlapping(start, end) {
            return disabledRanges.some(range => (start <= range.end && end >= range.start));
        }

        function updateDatepickers() {
            $("#session_start, #session_end").datepicker("option", "beforeShowDay", function(date) {
                return [!isDateDisabled(date)]; // Disable already blocked dates
            });
        }

        $(document).ready(function() {
            updateDatepickers();
        });

        $("#session_start").datepicker({
            minDate: 0,
            dateFormat: "mm/dd/yy",
            beforeShowDay: function(date) {
                return [!isDateDisabled(date)];
            },
            onSelect: function(selectedDate) {
                var startDate = $(this).datepicker("getDate");
                $("#session_end").datepicker("option", "minDate", startDate);

                var formattedDate = $.datepicker.formatDate("dd/M/yy", startDate);
                $("#session_start_display").text(formattedDate);

                validatePricingfields();
            },
            onClose: function() {
                if (!$(this).val()) {
                    validatePricingfields();
                }
            }
        });

        $("#session_end").datepicker({
            dateFormat: "mm/dd/yy",
            minDate: 0,
            beforeShowDay: function(date) {
                return [!isDateDisabled(date)];
            },
            onSelect: function() {

                var endDate = $(this).datepicker("getDate");

                var formattedDateEnd = $.datepicker.formatDate("dd/M/yy", endDate);
                $("#session_end_display").text(formattedDateEnd);

                validatePricingfields();
            },
            onClose: function() {
                if (!$(this).val()) {
                    validatePricingfields();
                }
            }
        });

        $("#session_start, #session_end").on("keypress keydown", function(e) {
            if (e.keyCode === 8 || e.keyCode === 46) {
                $(this).val('');

                var targetDisplay = $(this).attr("id") === "session_start" ? "#session_start_display" : "#session_end_display";
                $(targetDisplay).text("");

            } else {
                e.preventDefault();
            }
        });



        function validatePricingfields() {
            const inputs = $(
                '.seasonal_pricing_step .checkin_checkout_fields_wrapper input[type="text"], .seasonal_pricing_step .checkin_checkout_fields_wrapper input[type="number"]'
                );

            let anyFilled = false; // Instead of 'allEmpty', track if at least one input is filled

            inputs.each(function() {
                console.log(anyFilled);
                if ($(this).val().trim().length > 0) {
                    anyFilled = true;
                    console.log(anyFilled);
                    return false;
                }
            });

            $('.seasonal_pricing_step .next').prop("disabled", anyFilled);
        }

        $(document).on('input change',
            '.seasonal_pricing_step .checkin_checkout_fields_wrapper input[type="text"], .seasonal_pricing_step .checkin_checkout_fields_wrapper input[type="number"]',
            function() {
                validatePricingfields();
            });


        $(document).on('change', '.seasonal_pricing_step .radios_wrapper input[type="radio"]', function() {
            var selectedValue = $(this).val();
            var inputField = $(this).closest('.input_icon_wrapper').find('.seasonal_price_val');
            validatePricingfields();

            if (selectedValue == "increase") {
                inputField.attr('max', '');
            } else {
                inputField.attr('max', '90');
                if (parseInt(inputField.val()) > 90) {
                    inputField.val(90);
                }
            }
        });

        $(document).on('input', '.seasonal_price_val', function() {
            var max = parseInt($(this).attr('max'));
            var value = parseInt($(this).val());

            if (value > max) {
                $(this).val(max); // Restrict to the max value
            }
        });

        function formatDate(dateStr) {
            if (!dateStr) return '';

            var dateObj = new Date(dateStr);
            var day = dateObj.getDate().toString().padStart(2, '0'); // 25
            var month = dateObj.toLocaleString('en-US', { month: 'short' }); // Mar
            var year = dateObj.getFullYear(); // 2025

            return `${day}/${month}/${year}`;
        }


        $(document).on('click', '.listing_stepper .seasonal_pricing_step .checkin_checkout_fields_wrapper .add_price_btn a',
            function() {

                var priceChange = $('.listing_stepper .seasonal_pricing_step .seasonal_price_val').val();
                // var seasonalCategoryID = {{ $listing->category_id ?? '0' }};
                var seasonalCategoryID = $('input[type="hidden"][name="category_id"]').val();
                var priceFieldName = seasonalCategoryID == 1 ? "adult_price" : "price";
                console.log(priceFieldName);
                var oldPrice = parseFloat($('.hidden_price[name="' + priceFieldName + '"]').val());

                var seasonalChildSelectedValue = $('input[name="child_allow"]:checked').val() ?? 'no';
                var seasonalPrivateBookingSelectedValue = $('input[name="private_booking"]:checked').val() ?? 'no';

                if (seasonalChildSelectedValue == "yes" && seasonalPrivateBookingSelectedValue == "yes") {

                    var oldPriceChild = parseFloat($('.hidden_price[name="child_price"]').val());
                    var oldPricePrivateBooking = parseFloat($('.hidden_price[name="private_booking_price"]').val());

                    var percentageChild = parseFloat((priceChange / 100) * oldPriceChild).toFixed(3);
                    var percentagePrivateBooking = parseFloat((priceChange / 100) * oldPricePrivateBooking).toFixed(3);

                } else if (seasonalChildSelectedValue == "yes" && seasonalPrivateBookingSelectedValue == "no") {

                    var oldPriceChild = parseFloat($('.hidden_price[name="child_price"]').val());
                    var oldPricePrivateBooking = null;

                    var percentageChild = parseFloat((priceChange / 100) * oldPriceChild).toFixed(3);
                    var percentagePrivateBooking = null;

                } else if (seasonalChildSelectedValue == "no" && seasonalPrivateBookingSelectedValue == "yes") {

                    var oldPricePrivateBooking = parseFloat($('.hidden_price[name="private_booking_price"]').val());
                    var oldPriceChild = null;

                    var percentageChild = null;
                    var percentagePrivateBooking = parseFloat((priceChange / 100) * oldPricePrivateBooking).toFixed(3);

                } else if (seasonalChildSelectedValue == "no" && seasonalPrivateBookingSelectedValue == "no") {

                    var oldPricePrivateBooking = null;
                    var oldPriceChild = null;

                    var percentageChild = null;
                    var percentagePrivateBooking = null;

                }

                var newPrice;
                var newPriceChild;
                var newPricePrivateBooking;

                var startDate = $('#session_start').val();
                var endDate = $('#session_end').val();

                var formattedStartDate = formatDate(startDate);
                var formattedEndDate = formatDate(endDate);


                var increaseDecrease = $('.listing_stepper .seasonal_pricing_step .price_type_radio:checked').val();
                var percentage = parseFloat((priceChange / 100) * oldPrice).toFixed(3);

                if (increaseDecrease == "increase") {
                    var priceStatus = "Increase";
                    newPrice = parseFloat(oldPrice) + parseFloat(percentage);

                    if (oldPriceChild != null) {
                        newPriceChild = parseFloat(oldPriceChild) + parseFloat(percentageChild);
                    }

                    if (oldPricePrivateBooking != null) {
                        newPricePrivateBooking = parseFloat(oldPricePrivateBooking) + parseFloat(
                            percentagePrivateBooking);
                    }

                } else {
                    var priceStatus = "Decrease";
                    newPrice = parseFloat(oldPrice - percentage).toFixed(3);

                    if (oldPriceChild != null) {
                        newPriceChild = parseFloat(oldPriceChild - percentageChild).toFixed(3);
                    }

                    if (oldPricePrivateBooking != null) {
                        newPricePrivateBooking = parseFloat(oldPricePrivateBooking - percentagePrivateBooking).toFixed(
                            3);
                    }

                }

                oldPrice = parseFloat(oldPrice).toLocaleString();
                newPrice = parseFloat(newPrice).toLocaleString();

                if (oldPriceChild != null) {
                    oldPriceChild = parseFloat(oldPriceChild).toLocaleString();
                    newPriceChild = parseFloat(newPriceChild).toLocaleString();
                }

                if (oldPricePrivateBooking != null) {
                    oldPricePrivateBooking = parseFloat(oldPricePrivateBooking).toLocaleString();
                    newPricePrivateBooking = parseFloat(newPricePrivateBooking).toLocaleString();
                }

                if (startDate !== "" && endDate !== "" && priceChange !== "") {
                    // **Validation: Prevent selection if range overlaps disabled dates**
                    if (isRangeOverlapping(startDate, endDate)) {
                        Swal.fire({
                            icon: "error",
                            title: @json(translate('stepper.invalid_date_range')),
                            text: @json(translate('stepper.disabled_date_warning')),
                            confirmButtonColor: "#d33"
                        });
                        return;
                    }

                    // Add selected range to disable list
                    disabledRanges.push({
                        start: startDate,
                        end: endDate
                    });

                    if (oldPriceChild == null && oldPricePrivateBooking == null) {

                        $('.listing_stepper .seasonal_pricing_step .added_seasonal_prices').append(`
                        <div class="added_seasonal_price">
                            <div class="price_delete_wrapper">
                                <div class="price_change">
                                    <label for="">Price <span class="increase_decrease">
                                        <input type="hidden" name="seasons[0][type]" value="${priceStatus}" />
                                        ${priceStatus}
                                    </span>: 
                                    <span class="percent_figure">
                                        <input type="hidden" name="seasons[0][percentage]" value="${priceChange}" />
                                        ${priceChange}%
                                    </span> 
                                    </label>
                                </div>
                                <div class="cross_icon_wrapper">
                                    <a class="remove_seasonal_price_btn" href="javascript:void(0)">
                                        <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="new_price text-start">
                                <label for="">${seasonalCategoryID == 1 ? @json(translate('stepper.new_price_guest')) : @json(translate('stepper.new_price'))}: </label>
                                <del class="fs-14">$${oldPrice} {{ translate('stepper.cop') }}</del>
                                <span class="px-2 semi-bold fs-14">$${newPrice} {{ translate('stepper.cop') }}</span>
                            </div>
                            <div class="season_starts_ends_wrapper">
                                <div class="season_starts">
                                    <input type="hidden" name="seasons[0][start_date]" value="${startDate}" />
                                    <label for="">{{ translate('stepper.season_starts') }}: ${formattedStartDate}</label>
                                </div>
                                <div class="season_ends">
                                    <input type="hidden" name="seasons[0][end_date]" value="${endDate}" />
                                    <label for="">{{ translate('stepper.season_ends') }}: ${formattedEndDate}</label>
                                </div>
                            </div>
                        </div>
                    `);

                    } else if (oldPriceChild != null && oldPricePrivateBooking == null) {

                        $('.listing_stepper .seasonal_pricing_step .added_seasonal_prices').append(`
                        <div class="added_seasonal_price">
                            <div class="price_delete_wrapper">
                                <div class="price_change">
                                    <label for="">Price <span class="increase_decrease">
                                        <input type="hidden" name="seasons[0][type]" value="${priceStatus}" />
                                        ${priceStatus}
                                    </span>: 
                                    <span class="percent_figure">
                                        <input type="hidden" name="seasons[0][percentage]" value="${priceChange}" />
                                        ${priceChange}%
                                    </span> 
                                    </label>
                                </div>
                                <div class="cross_icon_wrapper">
                                    <a class="remove_seasonal_price_btn" href="javascript:void(0)">
                                        <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="new_price text-start">
                                <label for="">{{ translate('stepper.new_price_adult') }}: </label> 
                                <del class="fs-14">$${oldPrice} {{ translate('stepper.cop') }}</del>
                                <span class="px-2 semi-bold fs-14">$${newPrice} {{ translate('stepper.cop') }}</span>
                            </div>
                            <div class="new_price text-start">
                                <label for="">{{ translate('stepper.new_price_child') }}: </label> 
                                <del class="fs-14">$${oldPriceChild} {{ translate('stepper.cop') }}</del>
                                <span class="px-2 semi-bold fs-14">$${newPriceChild} {{ translate('stepper.cop') }}</span>
                            </div>
                            <div class="season_starts_ends_wrapper">
                                <div class="season_starts">
                                    <input type="hidden" name="seasons[0][start_date]" value="${startDate}" />
                                    <label for="">{{ translate('stepper.season_starts') }}: ${formattedStartDate}</label>
                                </div>
                                <div class="season_ends">
                                    <input type="hidden" name="seasons[0][end_date]" value="${endDate}" />
                                    <label for="">{{ translate('stepper.season_ends') }}: ${formattedEndDate}</label>
                                </div>
                            </div>
                        </div>
                    `);

                    } else if (oldPriceChild == null && oldPricePrivateBooking != null) {

                        $('.listing_stepper .seasonal_pricing_step .added_seasonal_prices').append(`
                        <div class="added_seasonal_price">
                            <div class="price_delete_wrapper">
                                <div class="price_change">
                                    <label for="">Price <span class="increase_decrease">
                                        <input type="hidden" name="seasons[0][type]" value="${priceStatus}" />
                                        ${priceStatus}
                                    </span>: 
                                    <span class="percent_figure">
                                        <input type="hidden" name="seasons[0][percentage]" value="${priceChange}" />
                                        ${priceChange}%
                                    </span> 
                                    </label>
                                </div>
                                <div class="cross_icon_wrapper">
                                    <a class="remove_seasonal_price_btn" href="javascript:void(0)">
                                        <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="new_price text-start">
                                <label for="">{{ translate('stepper.new_price_guest') }}: </label> 
                                <del class="fs-14">$${oldPrice} {{ translate('stepper.cop') }}</del>
                                <span class="px-2 semi-bold fs-14">$${newPrice} {{ translate('stepper.cop') }}</span>
                            </div>
                            <div class="new_price text-start">
                                <label for="">{{ translate('stepper.new_price_private') }}: </label> 
                                <del class="fs-14">$${oldPricePrivateBooking} {{ translate('stepper.cop') }}</del>
                                <span class="px-2 semi-bold fs-14">$${newPricePrivateBooking} {{ translate('stepper.cop') }}</span>
                            </div>
                            <div class="season_starts_ends_wrapper">
                                <div class="season_starts">
                                    <input type="hidden" name="seasons[0][start_date]" value="${startDate}" />
                                    <label for="">{{ translate('stepper.season_starts') }}: ${formattedStartDate}</label>
                                </div>
                                <div class="season_ends">
                                    <input type="hidden" name="seasons[0][end_date]" value="${endDate}" />
                                    <label for="">{{ translate('stepper.season_ends') }}: ${formattedEndDate}</label>
                                </div>
                            </div>
                        </div>
                    `);

                    } else if (oldPriceChild != null && oldPricePrivateBooking != null) {

                        $('.listing_stepper .seasonal_pricing_step .added_seasonal_prices').append(`
                        <div class="added_seasonal_price">
                            <div class="price_delete_wrapper">
                                <div class="price_change">
                                    <label for="">Price <span class="increase_decrease">
                                        <input type="hidden" name="seasons[0][type]" value="${priceStatus}" />
                                        ${priceStatus}
                                    </span>: 
                                    <span class="percent_figure">
                                        <input type="hidden" name="seasons[0][percentage]" value="${priceChange}" />
                                        ${priceChange}%
                                    </span> 
                                    </label>
                                </div>
                                <div class="cross_icon_wrapper">
                                    <a class="remove_seasonal_price_btn" href="javascript:void(0)">
                                        <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="new_price text-start">
                                <label for="">{{ translate('stepper.new_price_adult') }}: </label> 
                                <del class="fs-14">$${oldPrice} {{ translate('stepper.cop') }}</del>
                                <span class="px-2 semi-bold fs-14">$${newPrice} {{ translate('stepper.cop') }}</span>
                            </div>
                            <div class="new_price text-start">
                                <label for="">{{ translate('stepper.new_price_child') }}: </label> 
                                <del class="fs-14">$${oldPriceChild} {{ translate('stepper.cop') }}</del>
                                <span class="px-2 semi-bold fs-14">$${newPriceChild} {{ translate('stepper.cop') }}</span>
                            </div>
                            <div class="new_price text-start">
                                <label for="">{{ translate('stepper.new_price_private') }}: </label> 
                                <del class="fs-14">$${oldPricePrivateBooking} {{ translate('stepper.cop') }}</del>
                                <span class="px-2 semi-bold fs-14">$${newPricePrivateBooking} {{ translate('stepper.cop') }}</span>
                            </div>
                            <div class="season_starts_ends_wrapper">
                                <div class="season_starts">
                                    <input type="hidden" name="seasons[0][start_date]" value="${startDate}" />
                                    <label for="">{{ translate('stepper.season_starts') }}: ${formattedStartDate}</label>
                                </div>
                                <div class="season_ends">
                                    <input type="hidden" name="seasons[0][end_date]" value="${endDate}" />
                                    <label for="">{{ translate('stepper.season_ends') }}: ${formattedEndDate}</label>
                                </div>
                            </div>
                        </div>
                    `);

                    }

                    $('#session_start, #session_end').val('');
                    $('.foramted_display_time').html('');
                    $('.listing_stepper .seasonal_pricing_step .seasonal_price_val').val('');
                    reindexSeasonalPrices();
                    validatePricingfields();

                    // Disable the selected date range
                    updateDatepickers();
                }
            });

        $(document).on('click', '.listing_stepper .seasonal_pricing_step .added_seasonal_price .remove_seasonal_price_btn',
            function() {
                var parentDiv = $(this).closest('.added_seasonal_price');
                var startDate = parentDiv.find("input[name*='start_date']").val();
                var endDate = parentDiv.find("input[name*='end_date']").val();

                // Remove from disabledRanges array
                disabledRanges = disabledRanges.filter(range => range.start !== startDate || range.end !== endDate);

                parentDiv.remove();
                reindexSeasonalPrices();
                updateDatepickers(); // Re-enable the dates
            });

        function reindexSeasonalPrices() {
            $('.listing_stepper .seasonal_pricing_step .added_seasonal_price').each(function(index) {
                $(this).find('input[name^="seasons"]').each(function() {
                    const name = $(this).attr('name').replace(/seasons\[\d+\]/, `seasons[${index}]`);
                    $(this).attr('name', name);
                });
            });
        }
    </script>
@endpush
