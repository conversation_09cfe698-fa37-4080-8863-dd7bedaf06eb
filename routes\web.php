<?php

use App\Http\Controllers\{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Booking<PERSON><PERSON>roll<PERSON>,
    <PERSON>ron<PERSON><PERSON>roll<PERSON>,
    J<PERSON><PERSON><PERSON><PERSON><PERSON>,
    ProfileController,
    WebsiteController,
    WishlistController,
    StripeIdentityController,
    LanguagesController,
    ReviewController,
    CartController,
    ContactController,
    DashboardController,
    TipaltiAuthController,
    WebhookController
};
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Route::get('/', [WebsiteController::class, "index"])->name('/');
Route::get('update-conversion-rates-list', [WebsiteController::class, 'updateConversionRatesList']);

Route::get('url/{url_code}', [WebsiteController::class, "redirectToListing"])->name('redirectToListing');
Route::get('get-listing', [WebsiteController::class, "get_listing"])->name('get_listing')->middleware(["ajax", "cacheControl"]);
Route::get("listing-search-form", [WebsiteController::class, "listing_search_form"])->name("listing_search_form");
Route::get("listing-search", [WebsiteController::class, "listing_search"])->name("listing_search");
Route::get("detail/{listing_id}/{slug}", [WebsiteController::class, "listing_detail"])->name("detail");
Route::get("locale/{lang}", [WebsiteController::class, "locale"])->name("locale")->where("lang", "en|es");
Route::get('currency/{to}', [WebsiteController::class, "currency"])->name('currency');
Route::get('contact-us', [WebsiteController::class, "contactUs"])->name('contact_us');
Route::get('about', [WebsiteController::class, "about"])->name('about');
Route::get('privacy_policy', [WebsiteController::class, "privacy"])->name('privacy_policy');
Route::get('supplier-aggreement', [WebsiteController::class, "supplier_aggreement"])->name('supplier_aggreement');
Route::get('terms', [WebsiteController::class, "terms"])->name('terms');
Route::get('faq', [WebsiteController::class, "faq"])->name('faq');
Route::get('faq/{slug}', [WebsiteController::class, "faq_detail"])->name('faq_detail');
Route::get('dummy-details', [WebsiteController::class, "dummyDetails"])->name('dummy-details');
Route::get("help-center", [WebsiteController::class, "helpCenter"])->name('help_center');
Route::get("help-center/{slug}", [WebsiteController::class, "help_center_detail"])->name('help_center_detail');
Route::get('/user-info', [WebsiteController::class, 'getUserInfo']);

// detail page
Route::get("booking-detail/{ids}/{booking_number}", [BookingController::class, "bookingDetail"])->name('my-booking-detail');
Route::get("booking-pdf/{ids}/{booking_number}", [BookingController::class, "bookingPDF"])->name('my-booking-pdf');
Route::get("cancellation-policy-timeline/{policy_type}/{start}", [WebsiteController::class, "cancellationPolicyTimeline"])->name('cancellation_policy_timeline');
Route::get("calculate-detail-daily", [WebsiteController::class, "calculate_detail_daily"])->name('calculate_detail_daily')->middleware("ajax");
Route::get("calculate-detail-hourly", [WebsiteController::class, "calculate_detail_hourly"])->name('calculate_detail_hourly')->middleware("ajax");
Route::get("calculate-detail-tour", [WebsiteController::class, "calculate_detail_tour"])->name('calculate_detail_tour')->middleware("ajax");
// detail page end

// Route::get('webaccount_setting',[WebsiteController::class,"webaccountSetting"])->name('webaccount_setting');
Route::get('inboxChat/{provider_id}', [WebsiteController::class, "inboxChat"])->name('inboxChat')->middleware("auth");
Route::get("complete-job-cron", [CronController::class, "index"]);
Route::get("complete-wallet-job-cron", [CronController::class, "walletAmount"]);
Route::get("cancelled-wallet-job-cron", [CronController::class, "cancellationAmount"]);
Route::get("scheduled-wallet-job-cron", [CronController::class, "scheduledAmount"]);
//Tipalti Webhook Controller
Route::post('payment-deferred', [WebhookController::class, 'paymentDeferred']);
Route::post('payment-cancelled', [WebhookController::class, 'paymentCancelled']);
Route::post("payee-amount-submitted", [WebhookController::class, "payeeSubmitted"]);
Route::post("payee-detail-changed", [WebhookController::class, "payeeDetailChanged"]);
// auth
Route::post("signup", [AuthController::class, "sign_up"])->name('sign_up');
Route::post("forget-password", [AuthController::class, "forget_password"])->name('forget_password');
Route::post("resend-otp", [AuthController::class, "resend_otp"])->name("resend_otp")->middleware("throttle:1,0.1");
Route::post("check-otp", [AuthController::class, "check_otp"])->name("check_otp")->middleware("throttle:1,0.1");
Route::post("sign-up-email-verification", [AuthController::class, "signUpEmailVerification"])->name("signUpEmailVerification");
Route::post("wallet-verification", [AuthController::class, "WalletVerification"])->name("WalletVerification");
Route::post("change-email", [AuthController::class, "change_email"])->name("change_email");
Route::post("check-email-otp", [AuthController::class, "check_email_otp"])->name("check_email_otp");

// Route::post("resend-otp", [AuthController::class, "resend_otp"])->name("resend_otp");
// Route::post("check-otp", [AuthController::class, "check_otp"])->name("check_otp");
Route::post("reset-password", [AuthController::class, "reset_password"])->name("reset_password");

Route::post('stripe-webhook', [StripeIdentityController::class, 'handleWebhook']);
Route::post('/webhook/payment-intent', [StripeIdentityController::class, 'paymentStatus']);

Route::post("contact-form", [ContactController::class, "create"])->name("contact_form");

Route::get("messenger", [WebsiteController::class, "messenger"])->name("messenger");

Route::group(['middleware' => ['auth', 'roles', 'last_active'], 'roles' => ['admin', "sub_admin", 'user', 'service', 'customer']], function () {
    // booking screen
    Route::get('bookings', [WebsiteController::class, "bookings"])->name('bookings');
    Route::get('past-booking', [WebsiteController::class, "get_past_booking"])->name('get_past_booking');
    Route::get('current-booking', [WebsiteController::class, "get_current_booking"])->name('get_current_booking');

    // reviews
    Route::get("review-get/{listing_id}", [ReviewController::class, "review_get"])->name("review_get")->middleware("ajax");
    Route::post("review-post", [ReviewController::class, "review_post"])->name("review_post");
    Route::get("get-review-form", [ReviewController::class, "get_review_form"])->name("get_review_form")->middleware("ajax");
    Route::get("edit-review-form/{id}", [ReviewController::class, "edit_review_form"])->name("edit_review_form")->middleware("ajax");
    Route::get("view-review-details", [ReviewController::class, "view_review_details"])->name("view_review_details")->middleware("ajax");
    Route::get("edit-review-details/{id}", [ReviewController::class, "edit_review_details"])->name("edit_review_details")->middleware("ajax");


    // role switching
    Route::get("list-your-asset", [WebsiteController::class, "list_asset"])->name("list_asset");
    Route::get("browse-listing", [WebsiteController::class, "browse_listing"])->name("browse_listing");

    // stripe vertify
    Route::get('/account-setting', [StripeIdentityController::class, 'initiateVerification'])->name('webaccount_setting')->withoutMiddleware("kyc_verification");
    Route::get('/account-setting/stripe', [StripeIdentityController::class, 'stripeCustomPage'])->name('stripe_custom_page')->withoutMiddleware("kyc_verification");
    // Route::get('webaccount_setting',[WebsiteController::class,"webaccountSetting"])->name('webaccount_setting');
    Route::get("delete-card/{card_id}", [StripeIdentityController::class, "delete_card"])->name("cards.delete");
    Route::get("default-card/{card_id}", [StripeIdentityController::class, "default_card"])->name("cards.default");
    // Wishlist 
    Route::get("wishlist", [WishlistController::class, "wishlist_load"]);
    Route::post("add-wishlist", [WishlistController::class, "add_wishlist"])->name("add_wishlist");
    Route::get("delete-wishlist/{id}", [WishlistController::class, "wishlist_delete"]);

    // Cart 
    // Route::get('cart', [CartController::class, "cart_load"])->name('cart');
    Route::post("cart", [CartController::class, "add_cart"])->name("add_cart");
    // Route::delete("delete-cart/{cart_id}", [CartController::class, "delete_cart"])->name("delete_cart");
    // Route::get('checkout', [CartController::class, "checkout"])->name('checkout');
    // Route::post('checkout', [CartController::class, "checkout_post"])->name('checkout.post');

    // Reserve
    Route::post("reserve", [BookingController::class, "reserve_data"])->name("reserve_data");

    Route::get('/tipalti/auth', [TipaltiAuthController::class, 'redirectToAuthorization'])->name('tipalti.auth');
    Route::get('/tipalti/callback', [TipaltiAuthController::class, 'handleCallback'])->name('tipalti.callback');
    Route::get('/tipalti/refresh-token', [TipaltiAuthController::class, 'refreshAccessToken'])->name('tipalti.refresh');

    Route::post('/tipalti/create-payee', [TipaltiAuthController::class, 'createPayee'])->name('tipalti.createPayee');
    Route::get('/tipalti/iframe/{payeeId}', [TipaltiAuthController::class, 'showIframe'])->name('tipalti.showIframe');


    // Booking 
    Route::get('confirm-booking', [BookingController::class, "confirm_booking"])->name('confirm_booking');
    Route::post("confirm-booking/{listing_ids}", [BookingController::class, "confirm_booking_add"])->name("confirm_booking_add");
    Route::get("tour-update-booking/{listing_ids}", [BookingController::class, "tour_update_booking"])->name("tour_update_booking");
    
    Route::post("report-form", [WebsiteController::class, "report_form"])->name("report_form");
    Route::post("report-review", [WebsiteController::class, "report_review"])->name("report_review");
    Route::post('/paypal/create-order', [BookingController::class, 'processAdvPayPalPayment']);
    Route::get('paypal-success', [BookingController::class, 'paypalSuccess'])->name('paypal.success');
    Route::get('paypal-cancel', [BookingController::class, 'paypalCancel'])->name('paypal.cancel');
    Route::get("cancel-booking/{booking_id}", [BookingController::class, "cancelBooking"])->name('cancel_booking');
    Route::get("booking-generate-pdf/{booking_id}", [BookingController::class, "generate_booking_pdf"])->name('generate_booking_pdf');

    // JWT
    Route::get("get-bearer-token", [JWTController::class, "getBearerToken"])->name("getBearerToken")->middleware("ajax");

    // profile
    Route::post("update-password", [ProfileController::class, "update_password"])->name("userprofile.pass_update");
    Route::post("profile-update", [ProfileController::class, "profile_update"])->name("userprofile.update");
    Route::post("profile-img-update", [ProfileController::class, "profile_img_update"])->name("userprofile.profile_img");

    // session management
    Route::post("session-logout", [ProfileController::class, "logout_session"])->name("session.logout");

    // Testing Email Template route
    Route::get("testing_email_temp", [WebsiteController::class, "testingEmailTemp"])->name('testing_email_temp');
});



Route::get('auth/{provider}/', 'Auth\SocialLoginController@redirectToProvider');
Route::get('{provider}/callback', 'Auth\SocialLoginController@handleProviderCallback');
Route::get('logout', 'Auth\LoginController@logout');

Auth::routes();
Route::get('/clear-all', [WebsiteController::class, "cache_clear"]);
Route::get("google-map", [WebsiteController::class, "proxy_google"]);
Route::post('/translate-text', [WebsiteController::class, 'translate']);
Route::get("delete-draft-booking", [CronController::class, "delete_draft_listing"]);

Route::get("testing-database", function () {
    // return false;
    $users = App\Models\ContactInfo::get();
    foreach ($users as $user) {
        $user->ids = \Illuminate\Support\Str::uuid();
        $user->save();
    }
    return "uuid stored";
});
