@extends('listing.listing.layout.master')

@push('css')
    <style>
        body {
            background-color: #f8f9fa;
            height: 100vh;
        }

        .box label:hover {
            background: red
        }
    </style>
@endpush
@section('content')
    <div class="row custom_h align-items-center justify-content-center">
        <div class="col-lg-8 col-md-10 col-sm-12">
            {{-- select category  --}}
            <div class="topbar category_sec mt-5">
                <div class="white-box">
                    <h2 class="pb-2 text-center semi-bold"> {{ translate('stepper.describe_service') }} </h2>
                    <div class="ctg_box_wrapper d-flex justify-content-between py-2 flex-wrap">
                        @foreach ($categories as $category)
                            @if ($category->id != 5)
                                <div class="ctg_box">
                                    <a href="{{ route('listing_add', $category->slug) }}">
                                        <label class="single_ctg box-shadow" for="box-4">
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="radio_img pb-1">
                                                    <img src="{{ asset('website') . '/' . $category->image }}"
                                                        alt="">
                                                </div>
                                                {{ $category->display_name }}
                                            </div>
                                        </label>
                                    </a>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>
            {{-- select category end --}}
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 p-0">
            <div class="save-header d-flex">
                <a href="{{ url('/') }}">
                    <img src="{{ asset('/') . App\CommonSetting::first()->dashboard_logo }}"alt="{{ translate('stepper.logo') }}" />
                </a>
                <button type="button" onclick="document.location.href='{{ route('listing.index') }}'" class="save-btn">{{ translate('stepper.back') }}
                </button>
            </div>
        </div>
    </div>
@endsection


{{-- @endsection --}}
@push('js')
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
@endpush
