@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "pico-y-placa");
@endphp
<fieldset class="select_categories_step min_stay_requirement_step accommodations_rules_step pico_y_placa_step">
    <div class="inner_section_fieldset h_100">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                        @isset($step_data->sub_title)
                            <p>{{ $step_data->sub_title }}</p>
                        @endisset
                    </div>
                    <div class="allow_pets_field_wrapper">
                        <div class="allow_pets_input_wrapper">
                            @php
                                $placaValue = $listing?->detail->placa_restriction ?? null;
                            @endphp
                            @foreach (['yes', 'no'] as $ans)
                                <div class="allow_pets_input">
                                    <label for="placa_{{ $ans }}">{{ ucfirst($ans) }}</label>
                                    <input type="radio" class="radio_btn" id="placa_{{ $ans }}"
                                        @if ($placaValue === $ans) checked 
                                    @elseif (!$placaValue && $ans === 'no') checked @endif
                                        name="placa_restriction" value="{{ $ans }}">
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row restrictions_main_row" style="display: {{ $placaValue == 'yes' ? 'block' : 'none' }} ;">
            <div class="col-md-12">
                <div class="inner_section_categories_main_col scrollable-section">
                    <div class="row">
                        @php
                            $placaRestrictionDays = [
                                'mon_icon.png' => 'Monday',
                                'tues_icon.png' => 'Tuesday',
                                'wed_icon.png' => 'Wednesday',
                                'thurs_icon.png' => 'Thursday',
                                'fri_icon.png' => 'Friday',
                                'sat_icon.png' => 'Saturday',
                                'sun_icon.png' => 'Sunday',
                            ];
                            $selectedRestrictedDays = $listing?->restricted_days->pluck('day')->toArray() ?? [];

                        @endphp
                        @foreach ($placaRestrictionDays as $image => $placaRestrictionDay)
                            <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 single_category_col">
                                <div class="inner_section_single_category">
                                    <input type="checkbox" name="placa_restriction_days[]"
                                        @if (in_array($placaRestrictionDay, $selectedRestrictedDays) || (empty($selectedRestrictedDays) && $loop->first)) checked @endif
                                        value="{{ $placaRestrictionDay }}"
                                        id="placaRestrictionDays_{{ $loop->index }}">
                                    <label for="placaRestrictionDays_{{ $loop->index }}">
                                        <div class="category_icon_wrapper">
                                            <img src="{{ asset('website') }}/images/{{ $image }}" alt="">
                                        </div>
                                        <div class="category_title">
                                            <h5>{{ $placaRestrictionDay }}</h5>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>
@push('js')
    <script>
        $('.pico_y_placa_step .inner_section_categories_main_col input[type="checkbox"]').on('change', function() {
            const checkboxes = $('.pico_y_placa_step .inner_section_categories_main_col input[type="checkbox"]');
            if (!checkboxes.is(':checked')) {
                $(this).prop('checked', true);
            }
        });
    </script>
@endpush
