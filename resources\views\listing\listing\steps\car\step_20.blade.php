@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "hourly-avalibility");
@endphp
<fieldset class="select_categories_step min_stay_requirement_step hourly_availability_step">
    <div class="inner_section_fieldset h_100">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_col">
                    {{-- <div class="main_step_title hourly_data"> --}}
                        <div class="main_step_title">
                        {{-- <h2>Choose Your Vehicle Hourly Availability</h2> --}}
                        <h2>{{ $step_data->title ?? "" }}</h2>
                        @isset($step_data->sub_title)
                            <p>{{ $step_data->sub_title  }}</p>
                        @endisset
                    </div>

                    {{-- <div class="main_step_title daily_data" style="display: none;">
                        <h2>Set the Hours Your Vehicle Can Be Rented Daily</h2>
                    </div> --}}

                    {{-- <div class="step_description hourly_data">
                        <p>Select the hours your vehicle will be available to rent during the days of the week.</p>
                    </div>
                    <div class="step_description daily_data" style="display: none;">
                        <p>Select the hours your vehicle will be available to rent during the days of the week.</p>
                    </div> --}}
                </div>
            </div>
        </div>
        {{-- <div class="row hourly_data"> --}}
            <div class="row">
            <div class="col-md-12">
                <div class="inner_section_categories_main_col scrollable-section">
                    <div class="row">
                        @php
                            $availableHours = [
                                '12_am.png' => '00:00 - 01:00',
                                '1_am.png' => '01:00 - 02:00',
                                '2_am.png' => '02:00 - 03:00',
                                '3_am.png' => '03:00 - 04:00',
                                '4_am.png' => '04:00 - 05:00',
                                '5_am.png' => '05:00 - 06:00',
                                '6_am.png' => '06:00 - 07:00',
                                '7_am.png' => '07:00 - 08:00',
                                '8_am.png' => '08:00 - 09:00',
                                '9_am.png' => '09:00 - 10:00',
                                '10_am.png' => '10:00 - 11:00',
                                '11_am.png' => '11:00 - 12:00',
                                '12_pm.png' => '12:00 - 13:00',
                                '1_pm.png' => '13:00 - 14:00',
                                '2_pm.png' => '14:00 - 15:00',
                                '3_pm.png' => '15:00 - 16:00',
                                '4_pm.png' => '16:00 - 17:00',
                                '5_pm.png' => '17:00 - 18:00',
                                '6_pm.png' => '18:00 - 19:00',
                                '7_pm.png' => '19:00 - 20:00',
                                '8_pm.png' => '20:00 - 21:00',
                                '9_pm.png' => '21:00 - 22:00',
                                '10_pm.png' => '22:00 - 23:00',
                                '11_pm.png' => '23:00 - 00:00',
                            ];
                            $selectedHours = $listing?->hourly_availabilities->pluck('full_time')->toArray() ?? [];
                        @endphp
                        @foreach ($availableHours as $image => $availableHour)
                            <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 single_category_col">
                                <div class="inner_section_single_category">
                                    <input class="no_validate" type="checkbox" name="hourly_availabilities[]"
                                        value="{{ $availableHour }}" @if (in_array($availableHour, $selectedHours) || $loop->first) checked @endif
                                        id="hourlyAvailability_{{ $loop->index }}">
                                    <label for="hourlyAvailability_{{ $loop->index }}">
                                        <div class="category_icon_wrapper">
                                            <img src="{{ asset('website') }}/images/{{ $image }}" alt="">
                                        </div>
                                        <div class="category_title">
                                            <h5>{{ $availableHour }}</h5>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        {{-- <div class="checkin_checkout_fields_wrapper daily_data" style="display: none;">
            <div class="row">
                <div class="col-md-6 col_left">
                    <div class="inner_section_left_col">
                        <div class="checkin_title text-start">
                            <label for="">Start Time</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col_right">
                    <div class="inner_section_right_col">
                        <div class="time_field_wrapper">
                            <input type="time" name="check_in_time" class="form-control start_time no_validate"
                                value="{{ $listing->detail->check_in_time ?? '' }}" placeholder="Select start time of your {{  strtolower($category->display_name) }}">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 col_left">
                    <div class="inner_section_left_col">
                        <div class="checkin_title text-start">
                            <label for="">End Time</label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 col_right">
                    <div class="inner_section_right_col">
                        <div class="time_field_wrapper">
                            <input type="time" name="check_out_time" class="form-control end_time no_validate"
                                value="{{ $listing->detail->check_out_time ?? '' }}" placeholder="Select end time of your {{ strtolower($category->display_name) }}">
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}

    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>

@push('js')
    <script>
        $('.hourly_availability_step .inner_section_categories_main_col input[type="checkbox"]').on('change', function() {
            const checkboxes = $(
                '.hourly_availability_step .inner_section_categories_main_col input[type="checkbox"]');
            if (!checkboxes.is(':checked')) {
                $(this).prop('checked', true);
            }
        });


        $(document).ready(function() {


            function hourlyAvailabilityCheck(element) {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            hourlyAvailabilityValidation();
                        }
                    });
                });
                const config = {
                    attributes: true
                };
                observer.observe(element, config);
            }

            $('.hourly_availability_step').each(function() {
                hourlyAvailabilityCheck(this);
            });



            function hourlyAvailabilityValidation() {
                var checkedOptionsCount = 0;
                $('.hourly_availability_step .single_category_col').each(function() {
                    if ($(this).find('input[type="checkbox"]').is(':checked')) {
                        checkedOptionsCount++;
                    }
                });
                console.log(checkedOptionsCount);
                if (checkedOptionsCount == 0) {
                    $('.hourly_availability_step .next').prop('disabled', true);
                } else {
                    $('.hourly_availability_step .next').prop('disabled', false);
                }
            }
            hourlyAvailabilityValidation();
            $(document).on('change',
                '.hourly_availability_step .hourly_data .single_category_col input[type="checkbox"]',
                function() {
                    hourlyAvailabilityValidation();
                });


                // $(document).on('change', '.listing_stepper .hourly_availability_step .inner_section_categories_main_col input[type="checkbox"]', function() {
                //     checkHoursValidation()
                // });

                // function checkHoursValidation() {
                //     var allChecked = true;
                //     $('.listing_stepper .hourly_availability_step .inner_section_categories_main_col .single_category_col').each(function() {
                //         if ($(this).find('input[type="checkbox"]:checked').length === 0) {
                //             allChecked = false;
                //         }
                //     });
                //     $('.listing_stepper .hourly_availability_step .next').prop('disabled', !allChecked);
                // }

                // checkHoursValidation();


        });
    </script>
@endpush
