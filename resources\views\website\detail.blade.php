@extends('website.layout.master')
@push('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.css" />

    <style>
        .sec-2-detail .inner_detail .list_desc.show {
            -webkit-line-clamp: unset;
        }

        .loader {
            border: 16px solid #f3f3f3;
            border-radius: 50%;
            border-top: 16px solid #171717;
            width: 120px;
            height: 120px;
            -webkit-animation: spin 1s linear infinite;
            /* Safari */
            animation: spin 1s linear infinite;
            display: block;
            margin: 150px auto;
        }

        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
@endpush
@section('content')
    <section class="sec1_detail py-md-3 py-1 overflow-hidden listing_detail_page">
        <div class="container p-0">
            <div class="row listing_meta align-items-center">
                <div class="col-lg-10 col-sm-9 py-3" data-aos="fade">
                    <h2 data-aos="fade-down">{{ $listing->name ?? '' }}</h2>
                    <div class="rating d-flex align-items-center">
                        <i class="fas fa-star pe-sm-2 pe-1 "></i>
                        <p class="m-0 pe-3 ">
                            {{ $listing->rating == 0 ? translate('listing_details.new_listing') : number_format($listing->rating, 1)}}
                        </p>
                        @if ($listing->rating > 0)
                            <p class="px-3 m-0 v_divide">{{ count($listing->reviews ?? []) }} {{ translate('listing_details.reviews') }}
                            </p>
                        @endif
                        <p class="px-sm-3 px-1 m-0 v_divide">
                            {{ $listing->address?->city ?? '' }},
                            {{ $listing->address?->state ?? '' }},
                            {{ $listing->address?->country ?? '' }}
                        </p>
                    </div>
                </div>
                <div class="col-lg-2 col-sm-3 pb-3 text-sm-end " data-aos="fade">
                    <a href="#!" class="report-listing" data-bs-toggle="modal" data-bs-target="#report_modal">
                        <img src="{{ asset('website/images/flag.svg') }}" alt="">
                        <span>{{ translate('listing_details.report_listing') }} </span></a>
                </div>
                <div class="col-md-12 d-flex justify-content-between gap-md-0 gap-3">
                    <div class="d-flex" data-aos="fade-right">
                        <div class="user_img me-md-3 me-2">
                            <img class="img-fluid" src="{{ asset('website') . '/' . $listing->user->avatar }}"
                                alt="profile image">
                        </div>
                        <div class="user_info">
                            {{-- <h6 class="">Hosted by {{ $listing->user->name }}</h6> --}}
                            <h6 class="">{{ translate('listing_details.hosted_by')  }} {{ $listing->user->first_name ?? '' }}</h6>
                            <p> <i class="fas fa-star"></i>
                                {{ $listing->user->rating == 0 ? translate('listing_details.new_host'): $listing->user->rating }}
                            </p>
                        </div>
                    </div>
                    <div class="btn_info d-flex detail_contact_host align-items-center" data-aos="fade-left">
                        {{-- @auth
                            @if ($booking_check) --}}
                        <a href="{{ route('inboxChat', $listing->user_id) }}"
                            class="button me-md-3 detail_contact_host_btn me-1">{{ translate('listing_details.contact_host')}}</a>
                        {{-- @endif
                        @endauth --}}
                        {{-- <i class="far fa-heart ps-3"></i> --}}
                        <input type="checkbox" class="heart d-none" value="{{ $listing->ids }}"
                            id="wishlist{{ $listing->ids }}" @if ($listing->wishlist) checked @endif>
                        <label for="wishlist{{ $listing->ids }}">
                            <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M12.62 21.3944C12.28 21.5144 11.72 21.5144 11.38 21.3944C8.48 20.4044 2 16.2744 2 9.27436C2 6.18436 4.49 3.68436 7.56 3.68436C9.38 3.68436 10.99 4.56436 12 5.92436C12.5138 5.23023 13.183 4.66608 13.954 4.2771C14.725 3.88812 15.5764 3.68512 16.44 3.68436C19.51 3.68436 22 6.18436 22 9.27436C22 16.2744 15.52 20.4044 12.62 21.3944Z"
                                    stroke="#4A4A4A" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </label>
                    </div>
                </div>
            </div>
            <div class="row py-3 listing_info">
                <div class="col-xl-8 col-lg-12 pb-4 listing_gallery">
                    <div style="--swiper-navigation-color: #fff; --swiper-pagination-color: #fff" class="swiper mySwiper2">
                        <div class="swiper-wrapper pb-3">
                            @forelse ($listing->gallery_images as $gallery_image)
                                @if ($gallery_image->type == 'image' || $gallery_image->type == 'video')
                                    <div class="swiper-slide">
                                        @if ($gallery_image->type == 'image')
                                            <div class="slide_img">
                                                <img class="img-fluid"
                                                    src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                    onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                            </div>
                                        @elseif ($gallery_image->type == 'video')
                                            <div class="slide_img">
                                                <video width="100%" height="100%">
                                                    <source src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                        type="video/mp4">
                                                  {{translate('listing_details.your_browser_not_support_the_html_video') }}
                                                </video>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                            @empty
                                <div class="slide_img">
                                    <img class="img-fluid" src="{{ asset('website') }}"
                                        onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                </div>
                            @endforelse
                        </div>
                        <div class="swiper-button-next"><i class="fas fa-chevron-right"></i></div>
                        <div class="swiper-button-prev"><i class="fas fa-chevron-left"></i></div>
                    </div>
                    <div thumbsSlider="" class="swiper mySwiper">
                        <div class="swiper-wrapper">
                            @foreach ($listing->gallery_images as $gallery_image)
                                @if ($gallery_image->type == 'image')
                                    <div class="swiper-slide">
                                        <div class="slides_img">
                                            <img class="img-fluid" src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                        </div>
                                    </div>
                                @elseif ($gallery_image->type == 'video')
                                    <div class="swiper-slide">
                                        <div class="slides_img">
                                            <video width="100%" height="100%">
                                                <source src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                    type="video/mp4">
                                              {{ translate('listing_details.your_browser_not_support_the_html_video')}}
                                            </video>
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 col-lg-12 ">
                    <div class="row listing_map">
                        <div class="col-md-12">
                            <div class="map pb-4" data-aos="fade-left">
                                @if (count($listing->reviews) > 0)
                                    <div id="map_location" style="height:388px;border-radius: 20px"></div>
                                @else
                                    <div id="map_location" style="height:620px;border-radius: 20px"></div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="row listing_review">
                        @if ($category->id == 5)
                            <div class="col-md-12">
                                <div class="d-flex justify-content-between">
                                    <h6 class="fs-18">{{ translate('listing_details.experience')}}</h6>
                                    {{-- <a href="#!" class="fs-14 text-decoration-none">View All</a> --}}
                                    <button type="button" class="modal_btn" data-bs-toggle="modal"
                                        data-bs-target="#experience">
                                       {{ translate('listing_details.view_all')   }}
                                    </button>
                                </div>
                                @if ($listing->experiences)
                                    @foreach ($listing->experiences as $experience)
                                        @if ($loop->first)
                                            <div class="info px-sm-4 px-2 pt-4 pb-4 mb-4 medical_info">
                                                <table class="table company_info">
                                                    <tbody>
                                                        <tr>
                                                            <th class="light_bold fs-16 b-none">
                                                               {{translate('listing_details.company_name')  }}:</th>
                                                            <td class="normal b-none">
                                                                {{ $experience->company_name ?? '' }}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th class="light_bold fs-16 b-none">
                                                                {{translate('listing_details.desigination')  }}:</th>
                                                            <td class="normal b-none">{{ $experience->designation_title }}
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th class="light_bold fs-16 b-none">
                                                                {{translate('listing_details.date_from')   }}:</th>
                                                            <td class="normal b-none">{{ $experience->date_from }}</td>
                                                        </tr>
                                                        <tr>
                                                            <th class="light_bold fs-16 b-none">
                                                                {{translate('listing_details.date_to') }}:</th>
                                                            <td class="normal b-none">{{ $experience->date_to }}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <div class="detail_info py-3">
                                                    <h6 class="fs-16">{{translate('listing_details.description')   }}</h6>
                                                    <p> {{ $experience->designation_description }} </p>
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                @endif
                            </div>
                        @else
                            {{-- --------------------- Review Modal --------------------- --}}
                            @includeWhen(count($listing->reviews) > 0,
                                'website.template.listing-detail.review-modal',
                                compact('listing'))
                            {{-- --------------------- Review Modal End --------------------- --}}
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec-2-detail">
        <div class="container p-0">
            <div class="row g-0">
                {{-- description --}}
                <div class="col-md-12  inner_detail listing_description divider listing_data">
                    <div class="list_desc">
                        {!! $listing->description ?? '' !!}
                    </div>
                    <a href="#!" class="button read-more">{{ translate('listing_details.read_more') }}</a>
                </div>
                {{-- description end --}}
                {{-- --------------------- Tour detail 1 --------------------- --}}
                @includeWhen(
                    $listing->category->id == 1,
                    'website.template.listing-detail.tour',
                    compact('listing'))
                {{-- --------------------- Tour detail 1 end --------------------- --}}
                {{-- --------------------- boat detail 2 --------------------- --}}
                @includeWhen(
                    $listing->category->id == 2,
                    'website.template.listing-detail.boat',
                    compact('listing'))
                {{-- --------------------- boat detail 2 end --------------------- --}}
                {{-- --------------------- car detail 3 --------------------- --}}
                @includeWhen(
                    $listing->category->id == 3,
                    'website.template.listing-detail.car',
                    compact('listing'))
                {{-- --------------------- car detail 3 end --------------------- --}}
                {{-- --------------------- house detail --------------------- --}}
                @includeWhen(
                    $listing->category->id == 4,
                    'website.template.listing-detail.house',
                    compact('listing'))
                {{-- --------------------- house detail end --------------------- --}}
                {{-- Not for Medical Rules and Cancelation --}}
                @if ($category->id != 5)
                    {{-- Rule --}}
                    @if (isset($listing->detail->pet) || isset($listing->rules))
                        <div class="col-lg-12 divider listing_data listing_rule">
                            <div class="amenities-box">
                                <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.rules') }}</h3>
                                {{-- @if (isset($listing->rules)) --}}
                                <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                    {{-- for pets --}}
                                    @forelse ($listing->rules as $rule)
                                        @if (isset($rule->title))
                                            @if ($rule->allow == 'yes')
                                                <div class="box allowed d-flex gap-2 align-items-center" data-aos="fade">
                                                    <img src="{{ asset('website/images/square-check.svg') }}"
                                                        height="20px" width="205px" alt="Allowed">
                                                    <span>{{ $rule->title }}</span>
                                                </div>
                                            @endif
                                        @endif
                                    @endforeach
                                </div>
                                {{-- @endif --}}
                                {{-- @if (isset($listing->detail->rule)) --}}
                                <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                    {{-- for pets --}}
                                    {{-- @if ($listing->detail->pet == 'no')
                                        <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                                            <img src="{{ asset('website/images/ticksquare.svg') }}" height="20px"
                                                width="20px" alt="Not Allowed">
                                            <span>Pets</span>
                                        </div>
                                    @endif --}}
                                    @forelse ($listing->rules as $rule)
                                        @if (isset($rule->title))
                                            @if ($rule->allow == 'no')
                                                <div class="box not-allowed d-flex gap-2 align-items-center"
                                                    data-aos="fade">
                                                    <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                        height="20px" width="20px" alt="Not Allowed">
                                                    <span>{{ $rule->title }}</span>
                                                </div>
                                            @endif
                                        @endif
                                    @endforeach
                                </div>
                                {{-- @endif --}}
                            </div>
                        </div>
                    @endif
                    {{-- Rule end --}}
                    {{-- Notes --}}
                    @if (isset($listing->notes) && !$listing->notes->isEmpty())
                        <div class="col-lg-12 divider listing_data listing_notes">
                            <div class="amenities-box">
                                <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.you_need_to_know') }}
                                </h3>
                                <ol class="parent-box pt-2">
                                    @forelse ($listing->notes as $note)
                                        <li class="pb-3 fs-14" data-aos="fade">
                                            <span>{{ $note->name }}</span>
                                        </li>
                                    @endforeach
                                </ol>
                            </div>
                        </div>
                    @endif
                    {{-- Notes end --}}
                    {{-- Cancelation  --}}

                    <div class="col-md-12 main_col_listing_cancellation" style="display: none;">
                        <div class="row detail-rw g-0 divider listing_data pt-5 listing_cancelation">
                            <div class="col-lg-12">
                                <div class="details-box">
                                    <div class="box d-flex gap-2 align-items-start">
                                        <div class="icon">
                                            <img src="{{ asset('website/images/ban.svg') }}" alt="">
                                        </div>
                                        <div class="content w-100">
                                            <h6>{{ translate('listing_details.cancelation_policy') }}</h6>
                                        </div>
                                    </div>
                                    <div id="cancellation_data"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {{-- End Cancelation  --}}
                @endif
            </div>
            {{-- End Rules and Cancelation --}}
            {{-- URL --}}
            @if (in_array($category->id, [5]))
                <div class="row py-3 listing_url g-0">
                    <div class="col-md-12">
                        <div class="info py-2">
                            <h5 class="ps-0 pb-2" data-aos="fade">{{ translate('listing_details.url')  }}</h5>
                            <a href="#!" class="fs-16 blue">{{ $listing->url ?? translate('listing_details.not_provided') }} </a>
                        </div>
                    </div>
                </div>
            @endif
            {{-- End URL --}}
        </div>
    </section>
    <section class="sec2_detail overflow-hidden">
        <div class="container p-0">
            {{-- Booking --}}
            @if ($category->id == 1)
                @include('website.template.booking-tour')
            @elseif(in_array($category->id, [2, 3, 4]))
                @if ($listing->detail->basis_type == 'Hourly')
                    @include('website.template.booking-hourly')
                @else
                    @include('website.template.booking-daily')
                @endif
            @endif
            {{-- End Booking --}}
        </div>
    </section>

    <!-- Modal -->
    <div class="modal report fade" id="report_modal" tabindex="-1" aria-labelledby="reportHeading" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title mx-auto" id="reportHeading">{{ translate('listing_details.report')}} {{ $listing->name ?? '' }} </h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('report_form') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        <input type="hidden" name="booking_id" id="booking_id">
                        <div class="mb-3">
                            <input type="text" name="subject" class="form-control" placeholder="{{ translate('listing_details.subject') }}">
                        </div>
                        <div class="mb-3">
                            <textarea class="form-control" name="description" rows="7" id="report_desc" placeholder="{{ translate('listing_details.description')   }}"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        {{-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button> --}}
                        <button type="submit" class="bg_black btn button1 white">{{ translate('listing_details.submit')  }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@push('js')
<script src="https://cdn.jsdelivr.net/npm/@fancyapps/fancybox@3.5.7/dist/jquery.fancybox.min.js"></script>
    <script>
        $(document).ready(function() {
            // wishlist 
            $(document).on("click", ".heart", function() {
                let listing_id = $(this).val();
                $.ajax({
                    url: `{{ route('add_wishlist') }}`,
                    method: "POST",
                    data: {
                        "_token": "{{ csrf_token() }}",
                        listing_id
                    },
                    success: function(response) {
                        $("#wishlist_count").html(response.data);
                        console.log(response);
                    }
                })
            })
        });
        $(document).ready(function() {
            $(document).on("click", ".read-more", function(event) {
                event.preventDefault(); // Prevent the default action of the link
                var $this = $(this);
                var $content = $('.sec-2-detail .inner_detail .list_desc');

                // Toggle the 'show' class on $content
                if ($content.hasClass('show')) {
                    $content.removeClass("show");
                    $this.text(`{{ translate('listing_details.show_more') }}`);
                    
                } else {
                    $content.addClass("show");
                    $this.text(`{{ translate('listing_details.show_less') }}`);
                }
            });
        });
    </script>
    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.0.1/angular.min.js"></script>
    {{-- <script src="https://maps.googleapis.com/maps/api/js?key={{ google_map_key() }}&amp;callback=initMap" async=""
        defer=""></script> --}}
    <script type="text/javascript">
        function initMap() {
            const myLatLng = {
                lat: {{ $listing->lat ?? 0 }},
                lng: {{ $listing->lng ?? 0 }}
            };
            const map = new google.maps.Map(document.getElementById("map_location"), {
                zoom: 15,
                center: myLatLng,
                mapTypeControl: true,
                fullscreenControl: true,
                streetViewControl: true,
            });
            // const radiusCircle = new google.maps.Circle({
            //     map: map,
            //     center: myLatLng,
            //     radius: 1000,
            //     fillColor: '#FF0000',
            //     fillOpacity: 0.2, 
            //     strokeColor: '#FF0000', 
            //     strokeOpacity: 0.5, 
            //     strokeWeight: 1,
            // });
            //map.fitBounds(radiusCircle.getBounds());
            // const markerIcon = {
            //     url: "{{ asset('website') . '/' . $category->image }}",
            //     scaledSize: new google.maps.Size(60, 60),
            //     anchor: new google.maps.Point(30, 30),
            // };
            const marker = new google.maps.Marker({
                position: myLatLng,
                map: map,
                //icon: markerIcon,
                title: "{{ $listing->name ?? '' }}",
            });
            // const infoWindow = new google.maps.InfoWindow({
            //     content: '<div style="font-size: 14px; font-weight: bold;">Exact location provided after booking</div>',
            //     disableAutoPan: true,
            // });
            // marker.addListener("click", () => {
            //     infoWindow.open(map, marker);
            // });
            // marker.addListener("mouseover", () => {
            //     infoWindow.open(map, marker);
            // });
            // marker.addListener("mouseout", () => {
            //     infoWindow.close();
            // });
        }
        window.initMap = initMap;
    </script>
    <script src="{{ url('google-map') }}?callback=initMap"></script>
    <script>
        $(window).on('load', function() {
            var content = $('.sec-2-detail .inner_detail .list_desc');
            var hasEllipsis = false;
            content.each(function() {
                var $this = $(this);
                if ($this[0].scrollHeight > $this.innerHeight()) {
                    hasEllipsis = true;
                    return false;
                }
            });
            if (hasEllipsis) {
                console.log('Text is truncated, showing Read More button.');
                $('.read-more').show();
            } else {
                console.log('Text fits within the clamped lines, hiding Read More button.');
                $('.read-more').hide();
            }
        });
        $(document).ready(function() {
            $(window).on('load', function() {
                $('#booking-form .calendar .pignose-calendar-unit-disabled + .pignose-calendar-unit > a')
                    .trigger('click')
            });
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
@endpush
