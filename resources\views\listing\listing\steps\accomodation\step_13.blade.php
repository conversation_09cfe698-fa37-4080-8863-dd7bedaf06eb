<fieldset class="add_videos_step" id="">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>Showcase Your Space with a YouTube Video</h2>
                    </div>
                    {{-- <div class="step_description">
                        <p>You'll need 5 photos to get started. You can add more to make changes later.</p>
                    </div> --}}
                    <div class="fields_wrapper">
                        <div class="txt_field position-relative">
                            <input type="url"
                                placeholder="Paste your YouTube link here (e.g., https://youtu.be/xxxxxxx)"
                                class="form-control step_input no_validate" name="youtubeLink" id="youtubeLink"
                                value="" />
                            <button type="button" id="addLink" class="addLink btn_yellow">Add</button>
                        </div>
                        <small id="errorMsg" class="mt-2 text-danger d-none youtubeError">
                            Please enter a valid YouTube link.</small>
                    </div>

                    <div class="video_wrapper">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1 " value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>

@push('js')
    <script>
        $(document).ready(function() {

            // $('#youtubeLink').on('keyup', function() {
            //     $('#addLink').attr('disabled' false);
            // });
            $('#addLink').on('click', function() {
                const link = $('#youtubeLink').val().trim();
                const videoId = extractYouTubeId(link);

                if (videoId) {
                    $('#errorMsg').addClass('d-none');
                    $('.video_wrapper').append(
                        `<div class="video_single">
                            <div class="dropdown delete"> 
                            <button class="btn btn-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-h" aria-hidden="true"></i> </button> <ul class="dropdown-menu"> <li><a class="dropdown-item delete_btn" href="#">Delete</a></li> </ul> </div>
                        <iframe width="100%" height="100%" class="youtubeIframe"
                            src="https://www.youtube.com/embed/${videoId}?controls=0&autoplay=0&autohide=1&modestbranding=1&showinfo=0&rel=0&&mute=1&iv_load_policy=3" 

                            allow=encrypted-media>
                        </iframe> 
                        <div class="video-fixed video-title"> </div><div class="video-fixed video-end"> </div>
                    </div>`
                    );

                } else if (link) {
                    $('#errorMsg').removeClass('d-none');
                    $('#videoPreview').html('');
                } else {
                    $('#errorMsg').addClass('d-none');
                    $('#videoPreview').html('');
                }
                $('#youtubeLink').val('');
                // $('#addLink').attr('disabled' true);

            });

            function extractYouTubeId(url) {
                const regExp =
                    /^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:watch\?v=|embed\/|v\/|shorts\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})(?:[?&].*)?$/;
                const match = url.match(regExp);
                return match ? match[1] : null;
            }

            $('.video_wrapper').delegate('.delete_btn','click', function() {
                console.log('hey');
                videoParent = $(this).closest('.video_single');
                videoParent.remove();

            });
        });
    </script>
@endpush
