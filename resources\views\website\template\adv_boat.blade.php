<section class="Residence-modal">
    <div class="modal fade" id="boat_filter_category" tabindex="-1" aria-labelledby="filterCategoryLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="filterCategoryLabel">{{ translate('advance_filters_modal.filters') }}</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="bg_white">
                        <div class="price-list">
                            <div class="inner-title">
                                <h6 class="title">{{ translate('advance_filters_modal.price_range') }}</h6>
                            </div>
                            <ul class="icheck-list">
                                <li>
                                    <label for="boat_hourly">{{ translate('advance_filters_modal.hourly') }}</label>
                                    <input type="radio" class="check" id="boat_hourly" name="basis_type"
                                        data-radio="iradio_flat-yellow" value="Hourly" checked>
                                </li>
                                <li>
                                    <label for="boat_daily">{{ translate('advance_filters_modal.daily') }}</label>
                                    <input type="radio" class="check" id="boat_daily" name="basis_type"
                                        data-radio="iradio_flat-yellow" value="Daily">
                                </li>
                            </ul>
                        </div>
                        <input type="text" class="js-range-slider" name="price_range" value=""
                            data-skin="round" data-type="double" data-min="1" data-max="5000000" data-grid="false" />
                    </div>
                    <div class="bg_white resident-list">
                        <h6 class="title">{{ translate('advance_filters_modal.watercraft_type') }}</h6>
                        <select class="js-example-basic-multiple" name="listing_types[]" multiple="multiple"
                            data-placeholder="{{ translate('advance_filters_modal.select_type') }}">
                            {{-- <option value="" disabled>Type Search Field</option> --}}
                            {{-- @foreach ($listing_types as $key => $listing_type)
                                <option value="{{ $listing_type->name ?? '' }}">{{ $listing_type->name ?? '' }}
                                </option>
                            @endforeach --}}
                            @foreach ($categories->where('id', 2)->first()->listing_types as $type)
                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                        @endforeach
                        </select>
                    </div>
                    <div class="bg_white">
                        <div class="rooms_parent">
                            <h6 class="title">{{ translate('advance_filters_modal.passengers') }}</h6>
                            <div class="guest_wrapper d-flex gap-2 align-items-center justify-content-between">
                                <button class="minus_btn p-0 " type="button"
                                    onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                    <i class="fa fa-minus" aria-hidden="true"></i>
                                </button>
                                <input type="number" name="capacity" class="form-control border-0 text-center m-0 p-0"
                                    id="capacity" min="0" max="" value="1" readonly />
                                <button class="plus_btn p-0" type="button"
                                    onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>

                    </div>
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.watercraft_length') }}</h6>
                        <div class="custom-radio">
                            <input type="radio" class="boat_length_radio_btn" id="boat_length_any" name="boat_length" value="any">
                            <label for="boat_length_any" class="ms-0">{{ translate('advance_filters_modal.any') }}</label>
                        
                            <input type="radio" class="boat_length_radio_btn" id="meter_five" name="boat_length" value="5">
                            <label for="meter_five">{{ translate('advance_filters_modal.five_plus_fit') }}</label>
                        
                            <input type="radio" class="boat_length_radio_btn" id="meter_six" name="boat_length" value="6">
                            <label for="meter_six">{{ translate('advance_filters_modal.six_plus_fit') }}</label>
                        
                            <input type="radio" class="boat_length_radio_btn" id="meter_seven" name="boat_length" value="7">
                            <label for="meter_seven">{{ translate('advance_filters_modal.seven_plus_fit') }}</label>
                        </div>                        
                    </div>
                    <div class="bg_white resident-list">
                        <h6 class="title">{{ translate('advance_filters_modal.activities') }}</h6>
                        <select class="js-example-basic-multiple" name="amenities[]" multiple="multiple"
                            data-placeholder="{{ translate('advance_filters_modal.select_activity') }}">
                            {{-- <option value="" disabled>Type Search Field</option> --}}
                            {{-- @foreach ($amenities as $amenity)
                                <option value="{{ $amenity->id ?? '' }}">{{ $amenity->name ?? '' }}</option>
                            @endforeach --}}
                            @foreach ($categories->where('id', 2)->first()->amenities as $amenity)
                            <option value="{{ $amenity->id }}">{{ $amenity->name }}</option>
                        @endforeach
                        </select>
                    </div>
                    {{-- <div class="bg_white">
                        <h6 class="title">boat manufactures</h6>
                        <select class="js-example-basic-multiple" name="states[]" multiple="multiple">
                            <option value="amenity_one">manufactures 1</option>
                            <option value="amenity_two">manufactures 2</option>
                            <option value="amenity_three">manufactures 3</option>
                            <option value="amenity_four">manufactures 4</option>
                            <option value="amenity_five">manufactures 5</option>
                            <option value="amenity_six">manufactures 6</option>
                            <option value="amenity_seven">manufactures 7</option>
                        </select>
                    </div>  --}}
                    <div class="bg_white">
                        <h6 class="title">{{ translate('advance_filters_modal.ratings') }}</h6>
                        <div class="custom-radio">
                            <input type="radio" class="boat_rating_radio_btn" id="boat_one-plus" name="ratings"
                                value="1">
                            <label for="boat_one-plus" class="ms-0">1+</label>
                            <input type="radio" class="boat_rating_radio_btn" id="boat_two-plus" name="ratings"
                                value="2">
                            <label for="boat_two-plus">2+</label>
                            <input type="radio" class="boat_rating_radio_btn" id="boat_three-plus" name="ratings"
                                value="3">
                            <label for="boat_three-plus">3+</label>
                            <input type="radio" class="boat_rating_radio_btn" id="boat_four-plus" name="ratings"
                                value="4">
                            <label for="boat_four-plus">4+</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn yellow-btn adv_apply">{{ translate('advance_filters_modal.apply') }}</button>
                    <button type="button" class="btn white-btn adv_reset">{{ translate('advance_filters_modal.reset') }}</button>
                </div>
            </div>
        </div>
    </div>
</section>
