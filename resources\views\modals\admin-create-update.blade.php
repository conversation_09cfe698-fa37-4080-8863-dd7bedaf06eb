@if (auth()->user()->hasRole('user'))
    <section class="addAdmin reject">
        <div class="modal fade" id="addAdmin" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel1">
            <div class="modal-dialog" id="amenModal" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="admin-modal-title">{{ translate('user_management.add_admin') }}</h1>
                        @if ($errors->any())
                            <ul class="alert alert-danger" id="form-error">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        @endif
                        <div class="form_field_padding">
                            <div class="mb-3 mod_cust_text">
                                <form id="admin-form" class="form-inline" method="POST">
                                    @csrf
                                    <input type="hidden" name="_method" value="PATCH"
                                        {{ old('_method') == 'PATCH' ? '' : 'disabled' }}>
                                    <div class="row form_field_padding ">
                                        <div class="col">
                                            <select class="form-select form-control select_multi" id="admin_role"
                                                aria-label="Role select" name="role">
                                                <option selected disabled>{{ translate('user_management.select_role') }}</option>
                                                <option value="2" {{ old('role') == 2 ? 'selected' : '' }}>
                                                    {{ trans('super_admin') }}</option>
                                                <option value="5" {{ old('role') == 5 ? 'selected' : '' }}>
                                                    {{ trans('sub_admin') }}
                                                </option>
                                            </select>
                                        </div>
                                        <input type="hidden" id="admin_id" value="">
                                        <input type="text" name="first_name" value="{{ old('first_name') }}"
                                            id="admin_first_name"
                                            class="{{ $errors->has('first_name') ? 'border-danger' : '' }} form-control"
                                            placeholder="{{ translate('user_management.first_name') }}" autocomplete="false">
                                        <input type="text" name="last_name" value="{{ old('last_name') }}"
                                            class="{{ $errors->has('last_name') ? 'border-danger' : '' }} form-control"
                                            id="admin_last_name" placeholder="{{ translate('user_management.last_name') }}(s)">
                                        <input type="hidden" class="country_code" id="admin_country_code"
                                            name="country_code" value="+1">
                                        <input type="tel" name="phone" id="admin_phone"
                                            value="{{ old('phone') }}" id="telephone"
                                            class="{{ $errors->has('phone') ? 'border-danger' : '' }} pl form-control"
                                            placeholder="{{ translate('user_management.phone') }}*" maxlength="15">
                                        <input type="email" id="admin_email" name="email"
                                            value="{{ old('email') }}"
                                            class="{{ $errors->has('email') ? 'border-danger' : '' }} form-control"
                                            id="email" placeholder="{{ translate('user_management.email') }}" autocomplete="none">
                                        <input type="password" name="password" value="{{ old('email') }}"
                                            class="{{ $errors->has('email') ? 'border-danger' : '' }} form-control"
                                            id="password" placeholder="{{ translate('user_management.password') }}" autocomplete="false">
                                        <input type="password" name="confirm_password" value="{{ old('email') }}"
                                            class="{{ $errors->has('email') ? 'border-danger' : '' }} form-control"
                                            id="confirmPassword" placeholder="{{ translate('user_management.confirm_password') }}">
                                    </div>
                                    <div class=" modal_btn text-center">
                                        <a href="javascript:void(0)" data-dismiss="modal" class="cancel_btn yellow"
                                            id="" style="margin-right: 5px;">{{ trans('cancel') }}</a>
                                        <button type="submit" class="btn create_btn"
                                            id="admin-modal-btn">{{ translate('user_management.create') }}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endif
