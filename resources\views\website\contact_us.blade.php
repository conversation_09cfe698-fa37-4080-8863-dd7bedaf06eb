@extends('website.layout.master')
@push('css')
    <style>
        .contact .iti {
            width: 100%;
        }
    </style>
@endpush
@section('content')
    <section class="contact">
        <div class="container">
            <div class="row boxes">
                <div class="col-lg-12">
                    <h2 data-aos="fade-up">{{ translate('contact.contact_us') }}</h2>
                </div>
                <div class="col-lg-4" data-aos="fade" data-aos-delay="500">
                    <div class="icon_box">
                        <p><i class="bi bi-telephone"></i></p>
                        <h5>{{ translate('contact.phone_number') }}</h5>
                        <a href="tel:{{ $common_setting->contact_phone }}">{{ $common_setting->contact_phone }}</a>
                    </div>
                </div>
                <div class="col-lg-4" data-aos="fade" data-aos-delay="500">
                    <div class="icon_box">
                        <p><i class="bi bi-envelope"></i></p>
                        <h5>{{ translate('contact.email_address') }}</h5>
                        <a href="mailto:{{ $common_setting->contact_email }}">{{ $common_setting->contact_email }}</a>
                    </div>
                </div>
                <div class="col-lg-4" data-aos="fade" data-aos-delay="500">
                    <div class="icon_box">
                        <p><i class="bi bi-geo-alt"></i></p>
                        <h5>{{ translate('contact.location') }}</h5>
                        <a href="">{{ $common_setting->contact_address }}</a>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="form-box" data-aos="fade" data-aos-delay="700">
                        <h6>{{ translate('contact.get_in_touch') }}</h6>
                        <p>{{ translate('contact.get_started') }}</p>
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <form action="{{ route('contact_form') }}" method="POST" data-aos="fade" data-aos-delay="500"
                            class="row">
                            @csrf
                            <div class="col-md-6">
                                <label>{{ translate('contact.first_name') }}</label>
                                <input type="text" name="first_name" value="{{ old('first_name') }}" id="first"
                                    class="{{ $errors->has('first_name') ? 'border-danger' : '' }} form-control w-100"
                                    placeholder="{{ translate('contact.first_name') }}">
                            </div>
                            <div class="col-md-6">
                                <label>{{ translate('contact.last_name') }}</label>
                                <input type="text" name="last_name" value="{{ old('last_name') }}"
                                    class="{{ $errors->has('last_name') ? 'border-danger' : '' }} form-control w-100"
                                    id="last" placeholder="{{ translate('contact.last_name') }}">
                            </div>
                            <div class="col-md-6">
                                <label>{{ translate('contact.phone') }}</label><br>
                                <input type="hidden" class="country_code" name="country_code" value="+1">
                                <input type="tel" name="phone" value="{{ old('phone') }}" id="telephone"
                                    class="{{ $errors->has('phone') ? 'border-danger' : '' }} pl w-100 form-control"
                                    placeholder="{{ translate('contact.phone') }}" maxlength="15">
                            </div>
                            <div class="col-md-6">
                                <label>{{ translate('contact.email') }}</label>
                                <input type="email" name="email" value="{{ old('email') }}"
                                    class="{{ $errors->has('email') ? 'border-danger' : '' }} form-control w-100"
                                    id="email" placeholder="{{ translate('contact.email') }}">
                            </div>
                            <div class="col-md-12">
                                <label>{{ translate('contact.subject') }}</label>
                                <input type="text" name="subject" value="{{ old('subject') }}"
                                    class="{{ $errors->has('subject') ? 'border-danger' : '' }} form-control w-100"
                                    id="subject" placeholder="{{ translate('contact.subject') }}">
                            </div>

                            <div class="col-md-12">
                                <label>{{ translate('contact.message') }}</label>
                                <textarea name="comment" class="{{ $errors->has('comment') ? 'border-danger' : '' }} w-100" id=""
                                    cols="30" rows="10" placeholder="{{ translate('contact.message') }}">{{ old('comment') }}</textarea>
                            </div>

                            <button type="submit" class="button bg-warning text-dark border-0">{{ translate('contact.submit') }}</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
