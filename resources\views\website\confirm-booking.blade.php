@extends('website.layout.master')

@push('css')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/css/select2.min.css" rel="stylesheet" />
@endpush

@section('content')
    <section class="book_details booking_detail">
        <div class="container">
            <form role="form" action="{{ route('confirm_booking_add', ['listing_ids' => $listing->ids]) }}"
                class="require-validation" data-cc-on-file="false" data-stripe-publishable-key="{{ stripe_key() }}"
                method="POST">
                @csrf
                <div class="row">
                    <div class="col-lg-12">
                        <h2>{{ translate('confirm_booking.confirm_your_booking') }}</h2>
                    </div>

                    {{-- ---------------------- booking card ---------------------- --}}
                    @include('website.layout.booking.card')
                    {{-- ---------------------- booking card end ---------------------- --}}
                </div>

                <div class="row trip-rw p-md-4 py-3 px-2 b_shadow b_radius bg_white sec-2-detail">
                    <div class="col-lg-12 p-2">
                        <h4 class="light-bold">{{ translate('confirm_booking.your_listing_details') }}</h4>
                        <p>{!! $listing->description ?? '' !!}</p>
                        <div class="row shadow-none pt-2 m-0 mb-3">
                            @includeWhen(
                                $listing->category->id == 1,
                                'website.template.listing-detail.tour',
                                compact('listing'))
                            {{-- --------------------- Tour detail 1 end --------------------- --}}
                            {{-- --------------------- boat detail 2 --------------------- --}}
                            @includeWhen(
                                $listing->category->id == 2,
                                'website.template.listing-detail.boat',
                                compact('listing'))
                            {{-- --------------------- boat detail 2 end --------------------- --}}
                            {{-- --------------------- car detail 3 --------------------- --}}
                            @includeWhen(
                                $listing->category->id == 3,
                                'website.template.listing-detail.car',
                                compact('listing'))
                            {{-- --------------------- car detail 3 end --------------------- --}}
                            {{-- --------------------- house detail --------------------- --}}
                            @includeWhen(
                                $listing->category->id == 4,
                                'website.template.listing-detail.house',
                                compact('listing'))
                            {{-- --------------------- house detail end --------------------- --}}
                            {{-- Ground Rules  --}}
                            <div class="col-lg-12 listing_data listing_rule ">
                                <div class="amenities-box">
                                    <h3 class="fs-22 listing_data_heading">
                                        {{ translate('confirm_booking.rules') }}
                                    </h3>
                                    <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                        {{-- for pets --}}
                                        @if ($listing->detail->pet == 'yes')
                                            <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                                                <img src="{{ asset('website/images/square-check.svg') }}" height="20px"
                                                    width="20px" alt="Allowed">
                                                <span>{{ translate('confirm_booking.pets') }}</span>
                                            </div>
                                        @endif
                                        @forelse ($listing->rules as $rule)
                                            @if (isset($rule->title))
                                                @if ($rule->allow == 'yes')
                                                    <div class="box allowed d-flex gap-2 align-items-center"
                                                        data-aos="fade">
                                                        <img src="{{ asset('website/images/square-check.svg') }}"
                                                            height="20px" width="20px" alt="Allowed">
                                                        <span>{{ $rule->title }}</span>
                                                    </div>
                                                @endif
                                            @endif

                                        @endforeach
                                    </div>
                                    <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                        {{-- for pets --}}
                                        @if ($listing->detail->pet == 'no')
                                            <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                                                <img src="{{ asset('website/images/ticksquare.svg') }}" height="20px"
                                                    width="20px" alt="Not Allowed">
                                                <span>{{ translate('confirm_booking.pets') }}</span>
                                            </div>
                                        @endif
                                        @forelse ($listing->rules as $rule)
                                            @if (isset($rule->title))
                                                @if ($rule->allow == 'no')
                                                    <div class="box not-allowed d-flex gap-2 align-items-center"
                                                        data-aos="fade">
                                                        <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                            height="20px" width="20px" alt="Not Allowed">
                                                        <span>{{ $rule->title }}</span>
                                                    </div>
                                                @endif
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            {{-- Ground Rules End  --}}
                            @if ($listing->category_id == 3)
                                <div class="col-lg-12 listing_data listing_pico">
                                    @if (count($listing->restricted_days ?? []) > 0)
                                        {{ translate('confirm_booking.pico_y_placa_restrictions') }}
                                        @foreach ($listing->restricted_days as $index => $restricted_day)
                                            @if ($index < count($listing->restricted_days) - 2)
                                                {{ $restricted_day->day }}s,
                                            @elseif ($index == count($listing->restricted_days) - 2)
                                                {{ $restricted_day->day }}s {{ translate('confirm_booking.and') }}
                                            @else
                                                {{ $restricted_day->day }}s.
                                            @endif
                                        @endforeach
                                    @else
                                        {{-- <p>This vehicle does not have any Pico y Placa restrictions.</p> --}}
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="row trip-rw sec-2-detail">
                    {{-- <div class="col-lg-12">
                        <div class="billing-box">
                            <h4>Billing Address</h4>
                            <input type="text" name="street_address" id="" placeholder="Street Address"
                                class="prep">
                            <input type="number" name="apt_num" id="" placeholder="Apt Or Suit Number">
                            <input type="text" name="city" id="" placeholder="City" class="prep">
                            <input type="text" name="state" id="" placeholder="State">
                            <input type="text" name="country" id="" placeholder="Country" class="prep">
                            <input type="number" name="zip_code" id="" placeholder="Zip Code">
                        </div>
                    </div> --}}

                    {{-- Cancelation  --}}
                    <div class="col-md-12">
                        <div class="row detail-rw g-0 divider listing_data pt-0 listing_cancelation">
                            <div class="col-lg-12">
                                <div class="details-box">
                                    <div class="box d-flex gap-2 align-items-start">
                                        <div class="icon">
                                            <img src="{{ asset('website/images/ban.svg') }}" alt="">
                                        </div>
                                        <div class="content w-100">
                                            <h6>{{ translate('confirm_booking.cancelation_policy') }}</h6>
                                        </div>
                                    </div>
                                    <div id="cancellation_data"></div>

                                </div>
                            </div>
                        </div>
                    </div>
                    {{-- End Cancelation  --}}





                    {{-- <div class="col-lg-6">
                        <div class="ploicy_box">
                            <h4>Cancellation policy</h4>
                            <p>Free cancellation before 3:00 PM on Aug 5. Cancel before check-in on Aug 10 for a partial
                                refund. <a href="">Learn more</a></p>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="ploicy_box box22">
                            <h4>Ground rules</h4>
                            <p>We ask every guest to remember a few simple things about what makes a great guest.</p>
                            <a href="">Follow the house rules</a> <a href="">Treat your Host’s home like
                                your
                                own</a>
                        </div>
                    </div> --}}

                </div>
                {{-- Payment Method --}}
                <div class="row trip-rw p-md-4 py-3 px-2 b_shadow b_radius  bg_white">
                    <div class="col-lg-12 p-2">
                        <div class="pay_box">
                            <h4 class="light-bold">{{ translate('confirm_booking.payment_method') }}</h4>
                            <div class="row payment-parent">
                                <div class="col-md-3">
                                    <div class="payment-card">
                                        <input type="radio" checked class="d-none" name="payment-method" value="card"
                                            id="card">
                                        <label for="card"
                                            class="card d-flex flex-row justify-content-center align-items-center gap-3 position-relative"><span
                                                class="text">{{ translate('confirm_booking.card') }}</span>
                                            <span class="icon  position-absolute">
                                                <i class="fas fa-check-circle text-white"></i>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="payment-card">
                                        <input type="radio" class="d-none" name="payment-method" value="paypal"
                                            id="paypal">
                                        <label for="paypal"
                                            class="paypal d-flex flex-row justify-content-center align-items-center gap-3 position-relative">
                                            <span class="text">
                                                <img src="{{ asset('website/images/paypal-color.svg') }}"
                                                    alt="Pay with PayPal" height="25px">
                                            </span>
                                            <span class="icon position-absolute">
                                                <i class="fas fa-check-circle text-white"></i>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row register-card custom-cards align-items-end">
                                <div class="col-md-12 pb-3">
                                    <div class="card-img-parent d-flex gap-3 align-items-center">
                                        <div class="cards-img">
                                            <img src="{{ asset('website/images/masterCard.png') }}" alt="Master"
                                                class="img-fluid">
                                        </div>
                                        <div class="cards-img">
                                            <img src="{{ asset('website/images/visaCard.png') }}" alt="Visa"
                                                class="img-fluid">
                                        </div>
                                        <div class="cards-img">
                                            <img src="{{ asset('website/images/americanCard.png') }}"
                                                alt="American Express" class="img-fluid">
                                        </div>
                                        <div class="cards-img">
                                            <img src="{{ asset('website/images/dicoverCard.jpg') }}" alt="Dicover"
                                                class="img-fluid">
                                        </div>
                                        <div class="cards-img">
                                            <img src="{{ asset('website/images/union_pay.png') }}" alt="Union Pay"
                                                class="img-fluid">
                                        </div>

                                    </div>
                                </div>
                                <div class="col-md-8">
                                    {{-- <label class="control-label fs-14 regular">Select Card</label> --}}
                                    <div class="parent_select">
                                        <select class="card_select" name="card_id" id="card-dropdown">
                                            <option selected disabled>{{ translate('confirm_booking.saved_cards') }}
                                            </option>
                                            @forelse ($cards["records"] ?? [] as $card)
                                                <option value="{{ $card['fields']['skyflow_id'] ?? '' }}"
                                                    data-type="{{ $card['fields']['card_type'] ?? '' }}"
                                                    data-image="@if ($card['fields']['card_type'] == 'Mastercard') {{ asset('website/images/master_card_new.png') }}
                                                                @elseif ($card['fields']['card_type'] == 'JCB') {{ asset('website/images/JCB_new.png') }}
                                                                @elseif ($card['fields']['card_type'] == 'Discover') {{ asset('website/images/discover_new.png') }}
                                                                @elseif ($card['fields']['card_type'] == 'Visa') {{ asset('website/images/visa_new.png') }}
                                                                @elseif ($card['fields']['card_type'] == 'American Express') {{ asset('website/images/american_express_new.png') }}
                                                                @elseif ($card['fields']['card_type'] == 'UnionPay') {{ asset('website/images/union_pay_new.png') }}
                                                                @elseif ($card['fields']['card_type'] == 'Diners Club') {{ asset('website/images/diners_club_new.png') }} @endif"
                                                    data-last4="{{ substr($card['fields']['card_number'] ?? '', -4) }}"
                                                    data-exp="{{ $card['fields']['expiry_date'] ?? '' }}"
                                                    @if (!empty($card['fields']['is_default']) && $card['fields']['is_default'] == true) data-default="true" @endif>
                                                    {{ $card['fields']['card_number'] ?? '' }}
                                                </option>
                                            @empty
                                                <option value="">{{ translate('confirm_booking.no_card_added') }}
                                                </option>
                                            @endforelse
                                        </select>
                                    </div>
                                </div>
                                <div class="col position-relative">
                                    <label
                                        class="control-label fs-14 light-bold">{{ translate('confirm_booking.cvc') }}</label>
                                    <input autocomplete='off' onKeyPress="if(this.value.length==3) return false;"
                                        class='form-control card-cvc'
                                        placeholder='{{ translate('confirm_booking.cvc') }}' name="cvc"
                                        size='4' type='number'>
                                    @error('cvc')
                                        <div class="text-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-12 pt-3">
                                    <button type="button" class="btn setting_btn add-card-btn w-100 button mb-1"
                                        id="add-card-btn">{{ translate('confirm_booking.add_new_card') }}</button>
                                </div>
                            </div>
                            <div class="row add-card pt-2 custom-cards ">
                                <div class="col-md-12">
                                    {{-- card --}}
                                    <div id="collectCardNumber" class="empty-div"></div>
                                    {{-- <div id="collectCvv" class="empty-div"></div> --}}
                                    <div id="collectExpiryDate" class="empty-div"></div>
                                    <div id="collectCardholderName" class="empty-div"></div>
                                    <div>
                                        <input class="btn setting_btn button1" id="collectPCIData" type="submit"
                                            value="{{ translate('confirm_booking.add_card') }}">
                                        <span class="spinner-border spinner-border-sm d-none" role="status"
                                            aria-hidden="true"></span>
                                    </div>
                                    <div>
                                        <pre id="collectResponse"></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- Payment Method End --}}

                <div class="row">
                    <div class="col-lg-12">
                        <div class="confirm">
                            @if (auth()->user()->identity_verified == 'verified')
                                <p>{{ translate('confirm_booking.confirm_and_pay_agreement') }} <a href="#!"
                                        data-bs-toggle="modal"
                                        data-bs-target="#booking_detail">{{ translate('confirm_booking.booking_terms') }}</a>.
                                </p>
                                <div class="text-center">
                                    <button type="submit" class="button button1 confirm-btn text-center">
                                        {{ translate('confirm_booking.confirm_and_pay') }}</button>
                                </div>
                            @else
                                <p>{{ translate('confirm_booking.please_verify_your_identity') }} <a
                                        href="{{ url('account-setting') . '#verfication-kyc' }}"
                                        target="_blank">{{ translate('confirm_booking.click_here') }}</a>
                            @endif
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>


    <!-- Modal -->
    <div class="modal report fade booking_detail" id="booking_detail" tabindex="-1" aria-labelledby="reportHeading"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title mx-auto" id="reportHeading">{{ translate('confirm_booking.booking_detail') }}
                    </h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>{{ translate('confirm_booking.modal_text') }}</p>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/pg-calendar@1.4.31/dist/js/pignose.calendar.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>


    <script>
        $(document).on("wheel", 'input[name="cvc"]', function(event) {
            event.preventDefault();
            $(this).blur();
        });

        $(document).on("keydown", 'input[name="cvc"]', function(event) {
            if (event.key === "ArrowUp" || event.key === "ArrowDown") {
                event.preventDefault();
            }
        });

        $('.calendar').pignoseCalendar({
            pickWeeks: false,
            multiple: true
        });
    </script>
    <script src="https://js.skyflow.com/v1/index.js"></script>

    <script>
        $(document).ready(function() {

            var policyType = `{{ $listing->detail->cancellation_policy }}`;

            function cancellation_policy(startDate) {
                $.ajax({
                    type: 'GET',
                    // url: `{{ url('cancellation-policy-timeline') }}/` + id + `/` + category,
                    url: `{{ url('cancellation-policy-timeline') }}/` + policyType +
                        `/` +
                        startDate,
                    data: {
                        policyType: policyType,
                        startDate: startDate
                    },
                    success: function(data) {
                        if (data) {
                            $('.main_col_listing_cancellation').show();
                            $("#cancellation_data").html(data);
                            console.log(policyType);
                        } else {
                            console.log('Error: No view received.');
                        }
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        Swal.fire(
                            `Error`,
                            @json(translate('confirm_booking.please_select_a_valid_view')),
                            'error'
                        )
                    }
                });
            }

            cancellation_policy(`{{ $reserve['check_in'] }}`);


            $(document).on("click", "#collectPCIData", function() {
                var $button = $(this);
                $button.prop("disabled", true);
                $('.spinner-border').removeClass('d-none'); // Show the spinner
                setTimeout(function() {
                    $button.prop("disabled", false);
                    $('.spinner-border').addClass('d-none');
                }, 3000);
            });
            $(document).on("change", "[name=payment-method]", function() {
                $('.confirm-btn').prop('disabled', false);
                var paymentMethod = $(this).val();
                if (paymentMethod == 'paypal') {
                    $('.add-card').removeClass('active');
                    $('#add-card-btn').removeClass('d-none');
                    $('.custom-cards input:not([type="submit"], .setting_btn)').val('');
                    $('.custom-cards select > option:first-child').prop('selected', true);
                } else {
                    $('.confirm-btn').prop('disabled', true);
                }
            });
            $(document).on("click", "#add-card-btn", function() {
                var $button = $(this);
                $button.addClass("d-none");
                $('.add-card').addClass('active');
                $('.custom-cards input:not([type="submit"], .setting_btn)').val('');
                $('.custom-cards select > option:first-child').prop('selected', true);
            });
            $(document).on("click", ".edit-trip", function() {
                var $button = $(this);
                var isEdit = $button.text().trim() === @json(translate('confirm_booking.edit'))  ;
                $(this).addClass("save-trip");

                if (isEdit) {
                    $button.text(@json(translate('confirm_booking.save')));
                    $(this).closest('.trip-box').find('.guest_wrapper input').prop('readonly', false);
                } else {
                    $button.text(@json(translate('confirm_booking.edit')));
                    $(this).closest('.trip-box').find('.guest_wrapper input').prop('readonly', true);
                }
                $(this).closest('.trip-box').find('.guest_wrapper .btn').toggleClass("d_none");
            });
        });

        try {
            const skyflow = Skyflow.init({
                vaultID: "{{ skyflow_vault_id() }}",
                vaultURL: "{{ skyflow_vault_url() }}",
                getBearerToken: () => {
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            url: "{{ route('getBearerToken') }}",
                            method: "GET",
                            data: {
                                type: "front_end"
                            },
                            success: function(response) {
                                resolve(response.accessToken);
                            },
                            error: function(jqXHR, textStatus, errorThrown) {
                                reject(errorThrown);
                            }
                        });
                    });
                },
                options: {
                    logLevel: Skyflow.LogLevel.ERROR,
                    // actual value of element can only be accessed inside the handler 
                    // when the env is set to DEV.
                    // make sure the env is set to PROD when using skyflow-js in production
                    env: Skyflow.Env.DEV,
                }
            });

            // create collect Container
            const collectContainer = skyflow.container(Skyflow.ContainerType.COLLECT);

            //custom styles for collect elements
            const collectStylesOptions = {
                inputStyles: {
                    base: {
                        border: "1px solid #eae8ee",
                        padding: "10px 16px",
                        color: "#1d1d1d",
                        marginTop: "4px",
                        fontFamily: "Poppins, sans-serif",
                        paddingInline: "18px",
                        height: "50px",
                        borderRadius: "15px",
                        marginBottom: "5px",
                        fontSize: "16px",
                    },
                    complete: {
                        color: "#4caf50",
                    },
                    empty: {},
                    focus: {},
                    invalid: {
                        color: "#f44336",
                        fontFamily: "Poppins, sans-serif",
                    },
                },
                labelStyles: {
                    base: {
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "15px",
                        fontWeight: "500",
                        marginBottom: "10px"
                    },
                },
                errorTextStyles: {
                    base: {
                        color: "#f44336",
                        fontSize: "13.5px",
                        fontFamily: "Poppins, sans-serif",
                        marginBottom: "10px",
                        marginLeft: "8px",
                    },
                },
            };

            // create collect elements
            const cardNumberElement = collectContainer.create({
                table: "credit_cards",
                column: "card_number",
                ...collectStylesOptions,
                placeholder: @json(translate('confirm_booking.card_number')),
                label: @json(translate('confirm_booking.card_number'))  ,
                type: Skyflow.ElementType.CARD_NUMBER,
            }, {
                required: true
            });

            // const cvvElement = collectContainer.create({
            //     table: "credit_cards",
            //     column: "cvv",
            //     ...collectStylesOptions,
            //     label: "Cvv",
            //     placeholder: "cvv",
            //     type: Skyflow.ElementType.CVV,
            // });

            const expiryDateElement = collectContainer.create({
                table: "credit_cards",
                column: "expiry_date",
                ...collectStylesOptions,
                label: @json(translate('confirm_booking.expiry_date'))  ,
                placeholder: @json(translate('confirm_booking.expiry_format'))  ,
                type: Skyflow.ElementType.EXPIRATION_DATE,
            }, {
                required: true
            });

            const cardHolderNameElement = collectContainer.create({
                table: "credit_cards",
                column: "cardholder_name",
                ...collectStylesOptions,
                label: @json(translate('confirm_booking.card_holder_name'))  ,
                placeholder: @json(translate('confirm_booking.cardholder_name'))  ,
                type: Skyflow.ElementType.CARDHOLDER_NAME,
            }, {
                required: true
            });

            // mount the elements
            cardNumberElement.mount("#collectCardNumber");
            // cvvElement.mount("#collectCvv");
            expiryDateElement.mount("#collectExpiryDate");
            cardHolderNameElement.mount("#collectCardholderName");

            // add listeners to Collect Elements

            // add READY EVENT Listener 
            cardNumberElement.on(Skyflow.EventName.READY, (readyState) => {
                console.log("Ready Event Triggered", readyState);
            });

            // add CHANGE EVENT Listener
            // cvvElement.on(Skyflow.EventName.CHANGE, (changeState) => {
            //     console.table(changeState);
            //     console.log("CHANGE Event Triggered", changeState);
            // });

            // add FOCUS EVENT Listener
            expiryDateElement.on(Skyflow.EventName.FOCUS, (focusState) => {
                console.log("FOCUS Event Triggered", focusState);
            });

            // add BLUR EVENT Listener
            cardHolderNameElement.on(Skyflow.EventName.BLUR, (blurState) => {
                console.log("BLUR Event Triggered", blurState);
            });

            // collect all elements data
            const collectButton = document.getElementById("collectPCIData");
            if (collectButton) {
                collectButton.addEventListener("click", () => {
                    // loader
                    $("#collectPCIData").html(`<div class="spinner-border text-secondary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>`);
                    const collectResponse = collectContainer.collect({
                        additionalFields: {
                            records: [{
                                table: "credit_cards",
                                fields: {
                                    user_id: {{ auth()->id() }}
                                }
                            }]
                        }
                    });
                    // axios start
                    collectResponse
                        .then((response) => {
                            Swal.fire({
                                title: "Success!",
                                text: @json(translate('confirm_booking.card_added_successfully')),
                                icon: "success"
                            }).then(() => {
                                location.reload();
                            });
                        })
                        .catch((err) => {
                            console.log(err);
                        }).then(() => {
                            $("#collectPCIData").html(@json(translate('confirm_booking.add_card')));
                        });
                });
            }
        } catch (err) {
            console.log(err);
        }
        setTimeout(() => {
            $('#collectPCIData').prop("disabled", false);
        }, 5000);


        $(document).ready(function() {
            var elementHeight = $('.booking_detail .confirm_booking_card .col_right').height();
            $('.booking_detail .confirm_booking_card .col_left .swiper-slide img').css('height', elementHeight);
        });


        function formatCard(card) {
            if (!card.id || !card.element) return card.text;

            if (card.id === "Saved Cards" || $(card.element).is(':disabled')) {
                return $('<span>').text(card.text);
            }

            var image = $(card.element).data('image');
            var type = $(card.element).data('type');
            var last4 = $(card.element).data('last4');
            var exp = $(card.element).data('exp');
            var isDefault = $(card.element).data('default');
            var defaultLabel = `{{ translate('confirm_booking.default') }}`;

            return $(`
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div style="display: flex; align-items: center;">
                <img src="${image}" width="40" height="25" style="margin-right: 10px; object-fit: contain;" />
                <div>
                    <div><strong>${type}</strong> •••• ${last4}</div>
                    <div style="font-size: 12px; color: gray;">Expiration: ${exp}</div>
                </div>
            </div>
            ${isDefault ? `<span style="background: gold; padding: 2px 6px; border-radius: 8px; font-size: 12px;">${defaultLabel}</span>` : ''}
        </div>
    `);
        }


        $('.card_select').select2({
            templateResult: formatCard,
            // templateSelection: formatCard,
            closeOnSelect: true,
            placeholder: "Saved Cards",
            allowClear: false,
            tags: false,
            multiple: false,
        });



        function checkCardFields() {
            var selectedValues = $('#card-dropdown').val();
            var cvcValue = $('.card-cvc').val();
            if (selectedValues == null || cvcValue == '') {
                $('.confirm-btn').prop('disabled', true);
            } else {
                $('.confirm-btn').prop('disabled', false);
            }
        }

        $(document).on('change input', '#card-dropdown, .card-cvc', function() {
            checkCardFields();
        });

        $(document).ready(function() {
            checkCardFields();
        });
    </script>
@endpush
