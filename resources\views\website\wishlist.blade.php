@extends('website.layout.master')
@section('content')
    <section class="sec1_cart sec-2-detail wishlist wishlist_main_sec py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="heading_search_wrapper">
                        <div class="heading_wrapper">
                            <h3 class="semi-bold cart">{{ translate('wishlist.wishlist') }}</h3>
                        </div>
                        <div class="searchBar_wrapper">
                            <input type="search" name="search" id="" class="form-control search_field" placeholder="{{ translate('wishlist.search') }}">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                @forelse ($wishlists as $wishlist)
                    @if ($wishlist->listing)
                        <div class="col-lg-6 col-md-12 py-2 wishlist_item_col">
                            <div class="main_cart d-flex justify-content-between p-2 ">
                                <div class="property-image">
                                    <a
                                        href="{{ route('detail', ['listing_id' => $wishlist->listing->ids, 'slug' => $wishlist->listing->slug]) }}">
                                        <img class="img-fluid"
                                            src="{{ asset('website') . '/' . ($wishlist->listing->thumbnail_image->url ?? '') }}"
                                            loading="lazy"
                                            onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
                                    </a>
                                    <button class="delete border-0 bg-transparent delete-wishlist"
                                        data-wishlist-id="{{ $wishlist->id }}">
                                        <i class="fa fa-trash bg_yellow"></i>
                                    </button>
                                </div>
                                <a href="{{ route('detail', ['listing_id' => $wishlist->listing->ids, 'slug' => $wishlist->listing->slug]) }}"
                                    class="cart_text py-2 ps-1 pe-3 listing_data" style="text-decoration: none">
                                    <div class="cart-asset flex-wrap">
                                        <div class="d-flex justify-content-between flex-wrap">
                                            <h4 class="fs-26 light_bold list_product_heading " data-provider="{{ $wishlist->user->first_name ?? '' }} {{ $wishlist->user->last_name ?? '' }}">
                                                {{ $wishlist->listing->name ?? '-' }}</h4>
                                                <p class="mb-1 host_name">{{ $wishlist->user->first_name ?? '' }}
                                                    {{ $wishlist->user->last_name ?? '' }}</p>
                                            @php
                                                $listReview = empty($wishlist->listing->rating)
                                                    ? 0
                                                    : $wishlist->listing->rating;
                                            @endphp
                                            <p class="rating @if ($listReview < 5) new_listing @endif">
                                                <i class="fa fa-star"></i>
                                                @if ($listReview < 5)
                                                    {{ translate('wishlist.new_listing') }}
                                                @else
                                                    {{ $listReview }}
                                                @endif
                                            </p>
                                        </div>
                                        <div class="listing_desc">{!! $wishlist->listing->description ?? '-' !!}</div>
                                    </div>
                                    <ul class="categories d-flex parent-box p-0 gap-2" data-aos="fade">

                                        {{-- ------------- tour ------------- --}}
                                        @if ($wishlist->listing->category_id == 1)
                                            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                                                <img src="{{ asset('website/images/user.svg') }}" alt=""
                                                    height="15px" width="15px">
                                                {{ $wishlist->listing->detail->booking_capacity ?? 0 }} {{ translate('wishlist.booking_capacity') }}
                                            </li>
                                            @if (($wishlist->listing->detail->tour_day_type ?? '') == 'same-day')
                                                <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                                                    <img src="{{ asset('website/images/hourglass.png') }}" alt=""
                                                        height="15px" width="15px">
                                                    {{ translate('wishlist.same_day_tour') }}
                                                </li>
                                            @else
                                                <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                                                    <img src="{{ asset('website/images/hourglass.png') }}" alt=""
                                                        height="15px" width="15px">
                                                    {{ count($wishlist->listing->itineraries ?? []) }}
                                                    {{ translate('wishlist.days_tour') }}
                                                </li>
                                            @endif
                                            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                                                <img src="{{ asset('website/images/newborn-baby-icon.png') }}" alt="" height="15px" width="15px">
                                                @if ($wishlist->listing->detail->child_allow == 'yes')
                                                    {{ translate('wishlist.children_allowed') }}
                                                @else
                                                    {{ translate('wishlist.children_allowed') }}
                                                @endif
                                    
                                            </li>
                                            {{-- ------------- tour end ------------- --}}

                                            {{-- ------------- Boat ------------- --}}
                                        @elseif ($wishlist->listing->category_id == 2)
                                            <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                                {{ $wishlist->listing->detail->capacity ?? '0' }} {{ translate('wishlist.passengers') }}
                                            </li>
                                            <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                                {{ $wishlist->listing->detail->boat_length ?? '0' }} {{ translate('wishlist.ft') }}
                                            </li>
                                            @isset($wishlist->listing->activity)
                                                @foreach ($wishlist->listing->activity as $activity)
                                                    <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                                        {{ $activity->name }}
                                                    </li>
                                                @endforeach
                                            @endisset
                                            {{-- ------------- Boat end ------------- --}}

                                            {{-- ------------- Car ------------- --}}
                                        @elseif ($wishlist->listing->category_id == 3)
                                            <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                                <img src="{{ asset('website/images/car-front.svg') }}" alt=""
                                                    height="20px" width="20px">
                                                <span> {{ $wishlist->listing->detail->seats }} {{ translate('wishlist.seats') }}</span>
                                            </li>
                                            <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                                <img src="{{ asset('website/images/car-transmission.png') }}"
                                                    alt="" height="20px" width="20px">
                                                <span>{{ $wishlist->listing->detail->transmission ?? '-' }}</span>
                                            </li>
                                            <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                                <img src="{{ asset('website/images/car-engine.png') }}" alt=""
                                                    height="20px" width="25px">
                                                <span>{{ $wishlist->listing->detail->engine_type ?? '-' }}</span>
                                            </li>
                                            {{-- ------------- Car end ------------- --}}
                                            {{-- ------------- House ------------- --}}
                                        @elseif ($wishlist->listing->category_id == 4)
                                            <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                                <img src="{{ asset('website/images/bedroom.svg') }}" alt=""
                                                    height="20px" width="20px">
                                                {{ $wishlist->listing->detail->guests ?? '0' }} Guests
                                            </li>
                                            <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                                <img src="{{ asset('website/images/bedroom.svg') }}" alt=""
                                                    height="20px" width="20px">
                                                {{ $wishlist->listing->detail->bedrooms ?? '0' }} Bedrooms
                                            </li>
                                            <li class="catg box d-flex gap-2 align-items-center fs-14" data-aos="fade">
                                                <img src="{{ asset('website/images/bedroom.svg') }}" alt=""
                                                    height="20px" width="20px">
                                                {{ $wishlist->listing->detail->bathrooms ?? '0' }} Bathrooms
                                            </li>
                                            {{-- ------------- House End ------------- --}}
                                            {{-- medical --}}
                                        @elseif ($wishlist->listing->category_id == 5)
                                        @endif
                                    </ul>

                                </a>
                            </div>
                        </div>
                    @endif
                @empty
                    <h3 class="text-muted text-center">{{ translate('wishlist.wishlist_is_empty') }}</h3>
                @endforelse
            </div>
        </div>
    </section>
@endsection
@push('js')
    @if (View::hasSection('wishlist_message'))
        <script>
            Swal.fire({
                title: "{{ translate('wishlist.deleted') }}",
                text: "{{ translate('wishlist.your_file_has_been_deleted') }}",
                icon: "success"
            });
        </script>
    @endif
    <script>
        $(document).ready(function() {
            $(".delete-wishlist").on("click", function() {
                let wishlist_id = $(this).data("wishlist-id");
                if (wishlist_id) {
                    Swal.fire({
                        title: "Are you sure you want to remove this from the wishlist?",
                        // text: "You won't be able to revert this!",
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#FFCE32",
                        cancelButtonColor: "#E1505E",
                        confirmButtonText: "Yes, remove it",
                        cancelButtonText: "No"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = `{{ url('delete-wishlist') }}/${wishlist_id}`;
                        }
                    });
                }
            });



            $(".search_field").on("keyup", function () {
                var searchText = $(this).val().toLowerCase();
                
                $(".wishlist_main_sec .wishlist_item_col").each(function () {
                    var title = $(this).find(".list_product_heading").html().toLowerCase();
                    var provider = $(this).find(".list_product_heading").data('provider').toLowerCase();
                    if (title.includes(searchText) || provider.includes(searchText)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });


        });
    </script>
@endpush
