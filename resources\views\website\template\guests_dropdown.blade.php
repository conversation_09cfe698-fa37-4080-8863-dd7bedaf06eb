<div class="dropdown-menu members_dropdown">
    <div class="members_wrapper exp">
        <div class="d-flex justify-content-between align-items-center gap-5 members_parnet">
            <h6 class="fs-16 m-0 member_heading">{{ translate('home.adults') }}</h6>
            <div class="guest_wrapper d-flex gap-2 align-items-center">
                <button class=" minus_btn p-0 " type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepDown()">
                    <i class="fa fa-minus" aria-hidden="true"></i>
                </button>
                <input type="number" name="adults"
                    class="form-control border-0 text-center m-0" id="adults"
                    min="1" max="" value="1" readonly />
                <button class=" plus_btn p-0" type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepUp()">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
            </div>
        </div>
        <div class="d-flex justify-content-between align-items-center gap-5 members_parnet">
            <h6 class="fs-16 m-0 member_heading">{{ translate('home.children') }}</h6>
            <div class="guest_wrapper d-flex gap-2 align-items-center">
                <button class="minus_btn p-0 " type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepDown()">
                    <i class="fa fa-minus" aria-hidden="true"></i>
                </button>
                <input type="number" name="children"
                    class="form-control border-0 text-center m-0"
                    id="children" min="0" max=""
                    value="0" readonly />
                <button class="plus_btn p-0" type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepUp()">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="members_wrapper watercraft">
        <div class="d-flex justify-content-between align-items-center gap-5 members_parnet">
            <h6 class="fs-16 m-0 member_heading">{{ translate('home.passengers') }}</h6>
            <div class="guest_wrapper d-flex gap-2 align-items-center">
                <button class="minus_btn p-0 " type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepDown()">
                    <i class="fa fa-minus" aria-hidden="true"></i>
                </button>
                <input type="number" name="passenger"
                    class="form-control border-0 text-center m-0"
                    id="passenger" min="1" max=""
                    value="1" readonly />
                <button class="plus_btn p-0" type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepUp()">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="members_wrapper car">
        <div class="d-flex justify-content-between align-items-center gap-1 members_parnet">
            {{-- <h6 class="fs-16 m-0 member_heading">{{ translate('stepper.seats') }} </h6>
            <div class="guest_wrapper d-flex gap-2 align-items-center">
                <button class="minus_btn p-0 " type="button"
                    onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                    <i class="fa fa-minus" aria-hidden="true"></i>
                </button>
                <input type="number" name="seat"
                    class="form-control border-0 text-center m-0"
                    id="seat" min="1" max="110"
                    value="1" readonly />
                <button class="plus_btn p-0" type="button"
                    onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
            </div> --}}
            <h6 class="fs-16 m-0 member_heading w-50">{{ translate('home.pickup_time') }}</h6>
            <div class="time_field_wrapper">
                <input type="time" name="pickup_time" class="form-control pick_up_time"
                    value="" placeholder="{{ translate('home.pickup_time') }}">
            </div>

        </div>
        <div class="d-flex justify-content-between align-items-center gap-1 members_parnet">
            <h6 class="fs-16 m-0 member_heading w-50">{{ translate('home.dropoff_time') }}</h6>
            <div class="time_field_wrapper">
                <input type="time" name="dropoff_time" class="form-control drop_off_time"
                    value="" placeholder="{{ translate('home.dropoff_time_placeholder') }}">
            </div>
        </div>
    </div>
    <div class="members_wrapper acc">
        <div class="d-flex justify-content-between align-items-center gap-5 members_parnet">
            <h6 class="fs-16 m-0 member_heading">{{ translate('home.guests') }}</h6>
            <div class="guest_wrapper d-flex gap-2 align-items-center">
                <button class="minus_btn p-0 " type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepDown()">
                    <i class="fa fa-minus" aria-hidden="true"></i>
                </button>
                <input type="number" name="guest"
                    class="form-control border-0 text-center m-0"
                    id="guest" min="1" max=""
                    value="1" readonly />
                <button class="plus_btn p-0" type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepUp()">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
            </div>
        </div>
        <div class="d-flex justify-content-between align-items-center gap-5 members_parnet">
            <h6 class="fs-16 m-0 member_heading">{{ translate('home.children') }}</h6>
            <div class="guest_wrapper d-flex gap-2 align-items-center">
                <button class="minus_btn p-0 " type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepDown()">
                    <i class="fa fa-minus" aria-hidden="true"></i>
                </button>
                <input type="number" name="acc_children"
                    class="form-control border-0 text-center m-0"
                    id="acc_children" min="0" max=""
                    value="0" readonly />
                <button class="plus_btn p-0" type="button"
                    onclick="this.parentNode.querySelector('input[type=number]').stepUp()">
                    <i class="fa fa-plus" aria-hidden="true"></i>
                </button>
            </div>
        </div>
    </div>
</div>