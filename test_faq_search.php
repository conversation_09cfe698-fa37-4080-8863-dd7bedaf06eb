<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "FAQ Search Test\n";
echo "===============\n\n";

// Check FAQ count
$faqCount = App\Models\Faq::count();
echo "Total FAQs: $faqCount\n\n";

if ($faqCount > 0) {
    echo "Sample FAQ titles:\n";
    foreach(App\Models\Faq::take(5)->get() as $faq) {
        echo "- " . $faq->title . "\n";
        if ($faq->description) {
            echo "  Description: " . substr(strip_tags($faq->description), 0, 100) . "...\n";
        }
    }
    
    echo "\n\nTesting search functionality:\n";
    echo "============================\n";
    
    // Test search for "payment"
    $searchTerm = "payment";
    echo "\nSearching for '$searchTerm':\n";
    $results = App\Models\Faq::where(function ($query) use ($searchTerm) {
        $query->where("title", "LIKE", "%$searchTerm%")
              ->orWhere("description", "LIKE", "%$searchTerm%");
    })->get();
    echo "Results found: " . $results->count() . "\n";
    foreach($results as $result) {
        echo "- " . $result->title . "\n";
    }
    
    // Test search for "payments" (plural)
    $searchTerm = "payments";
    echo "\nSearching for '$searchTerm':\n";
    $results = App\Models\Faq::where(function ($query) use ($searchTerm) {
        $query->where("title", "LIKE", "%$searchTerm%")
              ->orWhere("description", "LIKE", "%$searchTerm%");
    })->get();
    echo "Results found: " . $results->count() . "\n";
    foreach($results as $result) {
        echo "- " . $result->title . "\n";
    }
    
} else {
    echo "No FAQs found in database. Please add some FAQs first.\n";
}
