<table class="table custom_table" id="myTable" data-order='[[ 4, "desc" ]]'>
    <thead>
        <tr>
            {{-- <th scope="col">#</th> --}}
            <th scope="col">{{ translate('dashboard_bookings.booking_id') }}</th>
            <th scope="col">{{ translate('dashboard_bookings.listing') }}</th>
            <th scope="col">{{ translate('dashboard_bookings.customer') }}</th>

            {{-- <th scope="col">Listing</th> --}}
            <th scope="col">{{ translate('dashboard_bookings.category') }}</th>
            <th scope="col">{{ translate('dashboard_bookings.date_from') }}</th>
            <th scope="col">{{ translate('dashboard_bookings.until') }}</th>
            @if (!auth()->user()->hasRole('service'))
                <th scope="col">{{ translate('dashboard_bookings.amount') }}</th>
                <th scope="col">{{ translate('dashboard_bookings.payment_method') }}</th>
            @else
                <th scope="col">{{ translate('dashboard_bookings.total_paid_by_customer') }}</th>
                <th scope="col">{{ translate('dashboard_bookings.total_payout') }}</th>
                <th scope="col">{{ translate('dashboard_bookings.booked') }}</th>
            @endif
            {{-- <th scope="col">{{ translate('dashboard_bookings.ratings') }}</th> --}}
            <th class="d-none" scope="col">{{ translate('dashboard_bookings.created_at') }}</th>
            <th scope="col">{{ translate('dashboard_bookings.status') }}</th>
            @if (!auth()->user()->hasRole('service'))
                <th scope="col">{{ translate('dashboard_bookings.action') }}</th>
            @endif
        </tr>
    </thead>
    <tbody>
        @forelse ($booking as $item)
            <tr>
                {{-- <td>{{ $loop->iteration ?? $item->id }}</td> --}}
                <td>
                    <p class="">{{ $item->booking_number ?? '' }}</p>
                </td>

                {{-- <td>{{ $item->listing->name ?? '-' }}</td> --}}
                <td>
                    {{-- <p class="limit">{{ $item->listing->address->address ?? '-' }}</p> --}}
                    <p class="limit">{{ $item->listing->name ?? translate('dashboard_bookings.deleted_listing') }}</p>
                </td>
                <td>
                    <p class="">{{ $item->customer->first_name ?? '' }}
                        {{ $item->customer->last_name ?? '' }}</p>
                </td>
                <td>
                    {{ $item->listing->category->display_name ?? '-' }}
                </td>
                <td>
                    {{-- @if ($item->check_out)
                        {{ $item->check_in . ' - ' . ($item->check_out ?? '') }}
                        ({{ $item->total_days ?? '-' }} days)
                    @else
                        {{ $item->check_in   }}
                    @endif --}}
                    {{ date(config('constant.date_format'), strtotime($item->check_in)) }}
                </td>
                <td>
                    {{ date(config('constant.date_format'), strtotime($item->check_out == 0 ? $item->check_in : $item->check_out)) }}
                </td>
                <td>
                    {{ translate('dashboard_bookings.currency_code') }}
                    {{ isset($item->total_amount) ? number_format($item->total_amount, 0) : '-' }}
                </td>
                @if (auth()->user()->hasRole('service'))
                    {{-- Total payout --}}
                    <td>
                        {{ translate('dashboard_bookings.currency_code') }}
                        {{-- {{ isset($item->total_amount) ? number_format($item->total_amount, 0) : '-' }} --}}
                        @php
                            $deductedAmount = ($item->total_amount ?? 0) * (($item->listing->category->tax ?? 0) / 100);
                            $totalPayout = $item->total_amount - $deductedAmount;
                        @endphp
                        {{ isset($totalPayout) ? number_format($totalPayout) : '-' }}
                    </td>
                    <td>{{ date(config('constant.date_format'), strtotime($item->created_at)) }}
                    </td>
                @else
                    <td>{{ ucfirst($item->payment_method) ?? '' }}</td>
                @endif
                {{-- <td> {{ ($item->listing->rating ?? '') == '' ? '0.0' : $item->listing->rating }} --}}
                <td class="d-none">{{ $item->created_at }}</td>
                <td>
                    <span
                        style="@if (strtolower($item->statusName->name) == 'pending') color: #ffc107; @elseif(strtolower($item->statusName->name) == 'accept' ||
                                strtolower($item->statusName->name) == 'completed' ||
                                strtolower($item->statusName->name) == 'paid') color: green; @elseif(strtolower($item->statusName->name) == 'rejected' || strtolower($item->statusName->name) == 'cancelled') color: red; @else color: #0384CC; @endif">
                        @php
                            $checkInDate = \Carbon\Carbon::parse(
                                $item->check_in,
                            )->toDateString();
                            $checkOutDate = \Carbon\Carbon::parse(
                                $item->check_out,
                            )->toDateString();
                            $today = now()->toDateString();
                            $currentTime = now();
                            $isOnGoing = false;
                            $isCompleted = false;

                            // Check if the booking is for today or in the past
                            if ($checkInDate <= $today && $checkOutDate >= $today) {
                                // For Daily bookings, this is enough to be "On Going"
                                if ($item->listing_basis != "Hourly") {
                                    $isOnGoing = true;
                                } else {
                                    // For Hourly bookings, check if current time is within any booked slot
                                    $hourlySlots = $item->hourly_slots;
                                    if ($hourlySlots && $hourlySlots->count() > 0) {
                                        $allSlotsPassed = true;

                                        foreach ($hourlySlots as $slot) {
                                            // Parse the slot time (format is typically "21:00 - 22:00")
                                            $slotParts = explode(' - ', $slot->slot);
                                            if (count($slotParts) == 2) {
                                                $slotStartTime = \Carbon\Carbon::parse($slot->date . ' ' . $slotParts[0]);
                                                $slotEndTime = \Carbon\Carbon::parse($slot->date . ' ' . $slotParts[1]);

                                                // Handle midnight crossover (e.g., "23:00 - 00:00")
                                                // If end time is earlier than start time, it means it crosses midnight
                                                if ($slotEndTime <= $slotStartTime) {
                                                    $slotEndTime->addDay(); // Move end time to next day
                                                }

                                                // Check if current time is within this slot
                                                if ($currentTime->between($slotStartTime, $slotEndTime)) {
                                                    $isOnGoing = true;
                                                    $allSlotsPassed = false;
                                                    break;
                                                }

                                                // If any slot's end time is in the future, not all slots have passed
                                                if ($slotEndTime > $currentTime) {
                                                    $allSlotsPassed = false;
                                                }
                                            }
                                        }

                                        // If all slots have passed and booking is still pending, it should be completed
                                        if ($allSlotsPassed && $item->status == 0) {
                                            $isCompleted = true;
                                        }
                                    }
                                }
                            }
                        @endphp
                        @if ($isOnGoing)
                            {{ translate('dashboard_bookings.on_going') }}
                        @elseif ($isCompleted)
                            {{ translate('dashboard_bookings.completed') }}
                        @else
                            {{ $item->statusName->name == 'Pending' ? translate('dashboard_bookings.upcoming') : $item->statusName->name ?? '-' }}
                        @endif
                    </span>
                </td>
                @if (!auth()->user()->hasRole('service'))
                    <td class="form_btn ">
                        <div class="dropdown">
                            <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                            </button>
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                @can('view-' . str_slug('Booking'))
                                    {{-- <a href="" data-toggle="modal" data-target="#booking"
                                    class="dropdown-item"
                                    title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Booking') }}">
                                    View
                                </a> --}}
                                    {{-- <a href="{{ url('/booking/booking/' . $item->id) }}"
                                    class="dropdown-item"
                                    title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Booking') }}">
                                    View
                                </a> --}}
                                @endcan
                                @can('edit-' . str_slug('Booking'))
                                    <a href="{{ url('/booking/booking/' . $item->id . '/edit') }}"
                                        class="dropdown-item"
                                        title="{{ translate('dashboard_bookings.edit_title') }}">
                                        <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                        {{ translate('dashboard_bookings.action') }}
                                    </a>
                                @endcan
                                @if ($item->status == 0)
                                    {{-- <button class="dropdown-item text-danger cancel-booking"
                                    data-booking-id="{{ $item->ids }}">
                                    {{ translate('dashboard_bookings.cancel') }}
                                </button> --}}
                                    <button type="button"
                                        class="dropdown-item text-danger cancel-booking"
                                        data-toggle="modal" data-target="#cancelation"
                                        data-booking-id="{{ $item->ids }}"
                                        data-payment-method="{{ $item->payment_method }}">
                                        {{ translate('dashboard_bookings.cancel') }}
                                    </button>
                                @else
                                    @can('delete-' . str_slug('Booking'))
                                        <form method="POST"
                                            action="{{ url('/booking/booking' . '/' . $item->id) }}"
                                            id="delete-form-{{ $item->id }}"
                                            accept-charset="UTF-8" style="display:inline">
                                            {{ method_field('DELETE') }}
                                            {{ csrf_field() }}
                                            <button type="button"
                                                class="dropdown-item btn-sm delete-btn"
                                                data-id="{{ $item->id }}"
                                                title="{{ translate('dashboard_bookings.delete_title') }}">
                                                {{-- <i class="fa fa-trash-o" aria-hidden="true"></i>  --}}
                                                {{ translate('dashboard_bookings.delete') }}
                                            </button>
                                        </form>
                                    @endcan
                                @endif
                                {{-- <a class="dropdown-item" data-toggle="modal" data-target="#">Cancel</a> --}}

                            </div>
                        </div>

                    </td>
                @endif
            </tr>
        @empty
            <tr>
                @php
                    // Calculate the correct colspan based on user role
                    $colspan = 9; // Base columns: booking_id, listing, customer, category, date_from, until, status
                    if (!auth()->user()->hasRole('service')) {
                        $colspan += 3; // amount, payment_method, action
                    } else {
                        $colspan += 2; // total_paid_by_customer, total_payout, booked (no action column)
                    }
                @endphp
                <td colspan="{{ $colspan }}" class="text-center no_booking">
                    @if ($status == 'today')
                        {{ translate('dashboard_bookings.no_bookings_for_today') }}
                    @elseif ($status == 'ongoing')
                        {{ translate('dashboard_bookings.no_ongoing_bookings') }}
                    @elseif ($status == 'upcoming')
                        {{ translate('dashboard_bookings.no_upcoming_bookings') }}
                    @elseif ($status == 'completed')
                        {{ translate('dashboard_bookings.no_completed_bookings') }}
                    @elseif ($status == 'canceled')
                        {{ translate('dashboard_bookings.no_canceled_bookings') }}
                    @else
                        {{ translate('dashboard_bookings.no_bookings') }}
                    @endif
                </td>
            </tr>
        @endforelse
    </tbody>
</table>
@if(is_object($booking) && method_exists($booking, 'firstItem'))
<div style="display: flex; justify-content: space-between; align-items: center;">
    <div class="pagination-info">
        {{ translate('dashboard_bookings.showing') }} {{ $booking->firstItem() }} {{ translate('dashboard_bookings.to') }} {{ $booking->lastItem() }} {{ translate('dashboard_bookings.of') }}
        {{ $booking->total() }} {{ translate('dashboard_bookings.entries') }}
    </div>
    <div class="pagination-wrapper"> {!! $booking->appends(['search' => Request::get('search'), 'status' => $status, 'date' => Request::get('date')])->render() !!} </div>
</div>
@endif
