<div class="preview_wrapper_main scrollable-section">
    <div class="row ">
        <div class="col-md-12">
            <ul class="nav nav-tabs nav-line-tabs nav-stretch border-0 gap-2 parent_tabs justify-content-center"
                id="system_config">
                <li class="nav-item">
                    <a class="nav-link active dark fs-14 regular" data-bs-toggle="tab" href="#laptop"><i
                            class="bi bi-laptop"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link dark fs-14 regular" data-bs-toggle="tab" href="#mobile"><i
                            class="bi bi-phone"></i></a>
                </li>
            </ul>
        </div>
    </div>

    <div
        class="row tab-content justify-content-center inner_section_main_col inner_section_categories_main_col scrollable-section">

        <div class="col-md-12 tab-pane fade laptop show active " id="laptop" role="tabpanel">

            <section class="sec1_detail py-2 overflow-hidden">
                <div class="container p-0">
                    <div class="row listing_meta align-items-center">
                        <div class="col-lg-10 col-sm-9 py-3" data-aos="fade">
                            <h2 class="preview_listingTitle" data-aos="fade-down">{{ $listing->name }}</h2>
                            <div class="rating d-flex align-items-center flex-wrap">
                                <i class="fas fa-star pe-sm-2 pe-1 "></i>
                                <p class="m-0 pe-3 ">
                                    @php
                                        $listReview = empty($listing->rating) ? 0 : $listing->rating;
                                    @endphp

                                    @if ($listReview < 5)
                                      {{translate('listing_details.new_listing')  }}
                                    @else
                                        {{ $listReview }} {{ translate('listing_details.reviews')  }}
                                    @endif
                                </p>
                                @if ($listReview > 5)
                                    <p class="px-3 m-0 v_divide">{{ count($listing->reviews ?? []) }}
                                      {{ translate('website.reviews') }}</p>
                                @endif
                                <p class="px-sm-3 px-1 m-0 v_divide preview_listing_location">
                                    {{ $listing->address->city ?? '' }},
                                    {{ $listing->address->state ?? '' }},
                                    {{ $listing->address->country ?? '' }}</p>
                            </div>
                        </div>
                        <div class="col-lg-2 col-sm-3 pb-3 text-sm-end " data-aos="fade">
                            <a href="javascript:void(0)" class="report-listing">
                                <img src="{{ asset('website/images/flag.svg') }}" alt="">
                                <span>{{ translate('listing_details.report_listing')   }} </span></a>
                        </div>
                        <div class="col-md-12 d-flex justify-content-between gap-md-0 gap-3">
                            <div class="d-flex" data-aos="fade-right">
                                <div class="user_img me-md-3 me-2">
                                    <img class="img-fluid"
                                        src="{{ asset('website') . '/' . ($listing->user->avatar ?? 'user-profile/dTF64fhJFRQcn80zssSZ1tfPmUOqhUM4T6Ov4DHA.png') }}"
                                        alt="profile image">
                                </div>
                                <div class="user_info">
                                    <h6 class="">{{ translate('listing_details.hosted_by')   }} {{ $listing->user->name ?? '' }}</h6>
                                    <p> <i class="fas fa-star"></i>
                                        @if (count($listing->user->provider_review ?? []) < 5)
                                           {{translate('listing_details.new_host')  }}
                                        @else
                                            {{ count($listing->user->provider_review ?? []) }}
                                            {{ translate('stepper.reviews')  
 }}
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div class="btn_info detail_contact_host d-flex align-items-center" data-aos="fade-left">
                                {{-- @auth
                                        @if ($booking_check) --}}
                                <a href="javascript:void(0)"
                                    class="button me-md-3 detail_contact_host_btn me-1 ">{{ __('website.contact_host') }}</a>
                                {{-- @endif
                                    @endauth --}}
                                {{-- <i class="far fa-heart ps-3"></i> --}}
                                <input type="checkbox" class="heart d-none" value="{{ $listing->ids ?? '' }}"
                                    id="wishlist{{ $listing->ids ?? '' }}"
                                    @if ($listing->wishlist ?? '') checked @endif>
                                <label for="wishlist{{ $listing->ids ?? '' }}">
                                    <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M12.62 21.3944C12.28 21.5144 11.72 21.5144 11.38 21.3944C8.48 20.4044 2 16.2744 2 9.27436C2 6.18436 4.49 3.68436 7.56 3.68436C9.38 3.68436 10.99 4.56436 12 5.92436C12.5138 5.23023 13.183 4.66608 13.954 4.2771C14.725 3.88812 15.5764 3.68512 16.44 3.68436C19.51 3.68436 22 6.18436 22 9.27436C22 16.2744 15.52 20.4044 12.62 21.3944Z"
                                            stroke="#4A4A4A" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row py-3 listing_info">
                        <div class="col-xl-8 col-lg-12 pb-4 listing_gallery">
                            <div style="--swiper-navigation-color: #fff; --swiper-pagination-color: #fff"
                                class="swiper mySwiper2">
                                <div class="swiper-wrapper pb-3">
                                    @forelse ($listing->gallery_images ?? [] as $gallery_image )
                                        @if ($gallery_image->type == 'image' || $gallery_image->type == 'video')
                                            <div class="swiper-slide">
                                                @if ($gallery_image->type == 'image')
                                                    <div class="slide_img">
                                                        <img class="img-fluid"
                                                            src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                            onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                                    </div>
                                                @elseif ($gallery_image->type == 'video')
                                                    <div class="slide_img">
                                                        <video width="100%" height="100%">
                                                            <source
                                                                src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                                type="video/mp4">
                                                            {{ translate('website.your_browser_not_support_the_html_video')   }}
                                                        </video>
                                                    </div>
                                                @endif
                                            </div>
                                        @endif
                                    @empty
                                        <div class="slide_img">
                                            <img class="img-fluid" src="{{ asset('website') }}"
                                                onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                        </div>
                                    @endforelse
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                            <div thumbsSlider="" class="swiper mySwiper">
                                <div class="swiper-wrapper">
                                    @foreach ($listing->gallery_images ?? [] as $gallery_image)
                                        @if ($gallery_image->type == 'image')
                                            <div class="swiper-slide">
                                                <div class="slides_img">
                                                    <img class="img-fluid"
                                                        src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                        onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                                </div>
                                            </div>
                                        @elseif ($gallery_image->type == 'video')
                                            <div class="swiper-slide">
                                                <div class="slides_img">
                                                    <video width="100%" height="100%">
                                                        <source
                                                            src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                            type="video/mp4">
                                                       {{ translate('website.your_browser_not_support_the_html_video')   }}
                                                    </video>
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-12 ">
                            <div class="row listing_map">
                                <div class="col-md-12">
                                    <div class="map pb-4" data-aos="fade-left">
                                        {{-- @if (count($listing->reviews) > 0) --}}
                                        {{-- <div id="map_location" style="height:390px;border-radius: 20px"></div> --}}
                                        {{-- @else --}}
                                        <div id="map_location" style="height:620px;border-radius: 20px"></div>
                                        {{--   @endif --}}
                                    </div>
                                </div>
                            </div>
                            {{-- <div class="row listing_review">
                                @if ($category->id == 5)
                                    <div class="col-md-12">
                                        <div class="d-flex justify-content-between">
                                            <h6 class="fs-18">{{ __('website.experience') }}</h6>
                                            <button type="button" class="modal_btn" data-bs-toggle="modal"
                                                data-bs-target="#experience">
                                                {{ __('website.view_all') }}
                                            </button>
                                        </div>
                                        @if ($listing->experiences)
                                            @foreach ($listing->experiences as $experience)
                                                @if ($loop->first)
                                                    <div class="info px-sm-4 px-2 pt-4 pb-4 mb-4 medical_info">
                                                        <table class="table company_info">
                                                            <tbody>
                                                                <tr>
                                                                    <th class="light_bold fs-16 b-none">
                                                                        {{ __('website.company_name') }}:</th>
                                                                    <td class="normal b-none">
                                                                        {{ $experience->company_name ?? '' }}
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <th class="light_bold fs-16 b-none">
                                                                        {{ __('website.desigination') }}:</th>
                                                                    <td class="normal b-none">
                                                                        {{ $experience->designation_title }}
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <th class="light_bold fs-16 b-none">
                                                                        {{ __('website.date_from') }}:</th>
                                                                    <td class="normal b-none">
                                                                        {{ $experience->date_from }}</td>
                                                                </tr>
                                                                <tr>
                                                                    <th class="light_bold fs-16 b-none">
                                                                        {{ __('website.date_to') }}:</th>
                                                                    <td class="normal b-none">
                                                                        {{ $experience->date_to }}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <div class="detail_info py-3">
                                                            <h6 class="fs-16">{{ __('website.description') }}
                                                            </h6>
                                                            <p> {{ $experience->designation_description }} </p>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endforeach
                                        @endif
                                    </div>
                                @else
                                    <div class="col-md-12">
                                        <div class="info px-4 pt-4 pb-4">
                                            <h5 class="py-sm-3 py-1 d-flex justify-content-between gap-1 flex-wrap">
                                                <span>{{ __('website.overall_rating') }}</span>
                                                <span class="rating">
                                                    <i class="fas fa-star"></i>
                                                    <span class="fs-26 light-bold">4.5 /</span> 5.0
                                                </span>
                                            </h5>
                                            <div class="d-flex justify-content-between">
                                                <div class="d-flex">
                                                    <div class="user_img me-md-3 me-2">
                                                        <img width="70" height="70"
                                                            class="img-fluid rounded-circle"
                                                            src="{{ asset('website') . '/' . ($listing->latest_review->user->avatar ?? 'user-profile/dTF64fhJFRQcn80zssSZ1tfPmUOqhUM4T6Ov4DHA.png') }}"
                                                            alt="user">
                                                    </div>
                                                    <div class="user_info">
                                                        <h6 class="">{{ translate('listing_details.john_doe') }} </h6>
                                                        <p> 5/Jan/2025</p>
                                                    </div>
                                                </div>
                                                <div class="user_rating">
                                                    <p> <i class="fas fa-star"></i>
                                                        {{ $listing->latest_review->rating ?? '4.2' }} </p>
                                                </div>
                                            </div>
                                            <div class="detail_info pb-3">
                                                <p>{{ $listing->latest_review->comment ?? 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry standard dummy text ever since the 1500s.' }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div> --}}
                        </div>
                    </div>
                </div>
            </section>

            <section class="sec-2-detail">
                <div class="container p-0">
                    <div class="row g-0">
                        {{-- description --}}
                        <div class="col-md-12  inner_detail listing_description divider listing_data">
                            <div class="list_desc">
                                {!! $listing->description ?? '' !!}
                            </div>
                            {{-- <a href="#!" class="button read-more">{{ __('website.read_more') }}</a> --}}
                        </div>
                        {{-- description end --}}
                        {{-- --------------------- Tour detail 1 --------------------- --}}
                        @includeWhen(
                            $listing->category->id == 1,
                            'website.template.listing-detail.tour',
                            compact('listing'))
                        {{-- --------------------- Tour detail 1 end --------------------- --}}
                        {{-- --------------------- boat detail 2 --------------------- --}}
                        @includeWhen(
                            $listing->category->id == 2,
                            'website.template.listing-detail.boat',
                            compact('listing'))
                        {{-- --------------------- boat detail 2 end --------------------- --}}
                        {{-- --------------------- car detail 3 --------------------- --}}
                        @includeWhen(
                            $listing->category->id == 3,
                            'website.template.listing-detail.car',
                            compact('listing'))
                        {{-- --------------------- car detail 3 end --------------------- --}}
                        {{-- --------------------- house detail --------------------- --}}
                        @includeWhen(
                            $listing->category->id == 4,
                            'website.template.listing-detail.house',
                            compact('listing'))
                        {{-- --------------------- house detail end --------------------- --}}
                        {{-- Not for Medical Rules and Cancelation --}}
                        @if ($category->id != 5)
                            {{-- Rule --}}
                            @if (isset($listing->detail->pet) || isset($listing->rules))
                                <div class="col-lg-12 divider listing_data listing_rule">
                                    <div class="amenities-box">
                                        <h3 class="fs-22 listing_data_heading">Rules</h3>
                                        {{-- @if (isset($listing->rules)) --}}
                                        <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                            {{-- for pets --}}
                                            {{-- @if ($listing->detail->pet == 'yes')
                                                <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                                                    <img src="{{ asset('website/images/square-check.svg') }}"
                                                        height="20px" width="20px" alt="Allowed">
                                                    <span>Pets</span>
                                                </div>
                                            @endif --}}
                                            @forelse ($listing->rules as $rule)
                                                @if (isset($rule->title))
                                                    @if ($rule->allow == 'yes')
                                                        <div class="box allowed d-flex gap-2 align-items-center"
                                                            data-aos="fade">
                                                            <img src="{{ asset('website/images/square-check.svg') }}"
                                                                height="20px" width="205px" alt="Allowed">
                                                            <span>{{ $rule->title }}</span>
                                                        </div>
                                                    @endif
                                                @endif
                                            @endforeach
                                        </div>
                                        {{-- @endif --}}
                                        {{-- @if (isset($listing->detail->rule)) --}}
                                        <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                            {{-- for pets --}}
                                            {{-- @if ($listing->detail->pet == 'no')
                                                <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                                                    <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                        height="20px" width="20px" alt="Not Allowed">
                                                    <span>Pets</span>
                                                </div>
                                            @endif --}}
                                            @forelse ($listing->rules as $rule)
                                                @if (isset($rule->title))
                                                    @if ($rule->allow == 'no')
                                                        <div class="box not-allowed d-flex gap-2 align-items-center"
                                                            data-aos="fade">
                                                            <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                                height="20px" width="20px" alt="Not Allowed">
                                                            <span>{{ $rule->title }}</span>
                                                        </div>
                                                    @endif
                                                @endif
                                            @endforeach
                                        </div>
                                        {{-- @endif --}}
                                    </div>
                                </div>
                            @endif
                            {{-- Rule end --}}
                            {{-- Notes --}}
                            @if (isset($listing->notes) && !$listing->notes->isEmpty())
                                <div class="col-lg-12 divider listing_data listing_notes">
                                    <div class="amenities-box">
                                        <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.you_need_to_know')   }}
                                        </h3>
                                        <ol class="parent-box pt-2">
                                            @forelse ($listing->notes as $note)
                                                <li class="pb-3 fs-14" data-aos="fade">
                                                    <span>{{ $note->name }}</span>
                                                </li>
                                            @endforeach
                                        </ol>
                                    </div>
                                </div>
                            @endif
                            {{-- Notes end --}}



                            {{-- Cancelation  --}}
                            <div class="col-md-12">
                                <div class="row detail-rw g-0 listing_data pt-5 listing_cancelation">
                                    <div class="col-lg-12">
                                        <div class="details-box">

                                            <div class="box d-flex gap-2 align-items-start">
                                                <div class="icon">
                                                    <img src="{{ asset('website/images/ban.svg') }}" alt="">
                                                </div>
                                                <div class="content w-100">
                                                    <h6 class="mt-0 semi-bold">{{ translate('listing_details.cancelation_policy')  }}</h6>
                                                </div>
                                            </div>

                                            <ul class="cancelation_policy_timeline_wrapper">
                                                @if ($listing->detail->cancellation_policy == 'Moderate' || $listing->detail->cancellation_policy == 'Strict')
                                                    <li class="timeline_step">
                                                        <span class="refund_date">{{ translate('listing_details.today')   }}</span>
                                                    </li>
                                                    <li class="timeline_step">
                                                        <span class="refund_date">{{ $dates['increment1'] }}</span>
                                                    </li>
                                                    <li class="timeline_step">
                                                        <span class="refund_date">{{ $dates['increment2'] }}</span>
                                                    </li>
                                                    <li class="timeline_step">
                                                        <span class="refund_date">{{ translate('listing_details.check_in')  }}</span>
                                                    </li>
                                                @else
                                                    <li class="timeline_step">
                                                        <span class="refund_date">{{ translate('listing_details.today')   }}</span>
                                                    </li>
                                                    <li class="timeline_step">
                                                        <span class="refund_date">{{ $dates['increment1'] }}</span>
                                                    </li>
                                                    <li class="timeline_step">
                                                        <span class="refund_date">{{ translate('listing_details.check_in')  }}</span>
                                                    </li>
                                                @endif
                                            </ul>

                                            <div class="timeline_detailed_wrapper">
                                                @if ($listing->detail->cancellation_policy == 'Moderate' || $listing->detail->cancellation_policy == 'Strict')
                                                    <div class="timeline_detail_single">
                                                        <div class="timeline_date">
                                                            <div class="date_tag">
                                                                <span>{{ translate('listing_details.before')   }}</span>
                                                                <h5>{{ $dates['increment1'] }}</h5>
                                                            </div>
                                                        </div>
                                                        <div class="description">
                                                            <h5>{{ translate('listing_details.full_refund') }}</h5>
                                                            <p>{{ translate('listing_details.cancel_before')   }}
                                                                {{ $dates['increment1'] }} {{ translate('listing_details.full_refund_time_note') }}</p>
                                                        </div>
                                                    </div>
                                                    <div class="timeline_detail_single">
                                                        <div class="timeline_date">
                                                            <div class="date_tag">
                                                                <span>{{ translate('listing_details.before')   }}</span>
                                                                <h5>{{ $dates['increment2'] }}</h5>
                                                            </div>
                                                        </div>
                                                        <div class="description">
                                                            <h5>{{ translate('listing_details.partial_refund')  }}</h5>
                                                            <p>{{ translate('listing_details.cancel_before_partial_refund')   }}
                                                                {{ $dates['increment2'] }} {{translate('listing_details.partial_refund_time_notice')  }}</p>
                                                        </div>
                                                    </div>
                                                    <div class="timeline_detail_single">
                                                        <div class="timeline_date">
                                                            <div class="date_tag">
                                                                <span>{{ translate('listing_details.after')   }}</span>
                                                                <h5>{{ $dates['increment2'] }}</h5>
                                                            </div>
                                                        </div>
                                                        <div class="description">
                                                            <h5>{{ translate('listing_details.no_refund') }}</h5>
                                                            <p>{{ translate('listing_details.no_refund_after_that')   }}</p>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="timeline_detail_single">
                                                        <div class="timeline_date">
                                                            <div class="date_tag">
                                                                <span>{{translate('listing_details.before')  }} </span>
                                                                <h5>{{ $dates['increment1'] }}</h5>
                                                            </div>
                                                        </div>
                                                        <div class="description">
                                                            <h5>{{ translate('listing_details.full_refund') }}</h5>
                                                            <p>{{ translate('listing_details.cancel_before') }}  
                                                                {{ $dates['increment1'] }} {{ translate('listing_details.full_refund_time_note') }}  </p>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div class="timeline_detail_single">
                                                        <div class="timeline_date">
                                                            <div class="date_tag">
                                                                <span>{{ translate('listing_details.after') }}  </span>
                                                                <h5>{{ $dates['increment1'] }}</h5>
                                                            </div>
                                                        </div>
                                                        <div class="description">
                                                            <h5>{{ translate('listing_details.no_refund') }}  </h5>
                                                            <p>{{ translate('listing_details.after') }}   {{ translate('listing_details.no_refund_after_that') }}  </p>
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{-- End Cancelation  --}}


                        @endif
                    </div>
                    {{-- End Rules and Cancelation --}}
                    {{-- {{ translate('listing_details.url') }} --}}
                    @if (in_array($category->id, [5]))
                        <div class="row py-3 listing_url g-0">
                            <div class="col-md-12">
                                <div class="info py-2">
                                    <h5 class="ps-0 pb-2" data-aos="fade">{{ translate('listing_details.url') }}</h5>
                                    <a href="#!" class="fs-16 blue">{{ $listing->url ?? 'Not provided' }} </a>
                                </div>
                            </div>
                        </div>
                    @endif
                    {{-- End {{ translate('listing_details.url') }} --}}
                </div>
            </section>

            {{-- <section class="sec2_detail overflow-hidden">
                    <div class="container p-0">
                        @if ($category->id == 1)
                            @include('website.template.booking-tour')
                        @elseif(in_array($category->id, [2, 3, 4]))
                            @if ($listing->detail->basis_type == 'Hourly')
                                @include('website.template.booking-hourly')
                            @else
                                @include('website.template.booking-daily')
                            @endif
                        @endif
                    </div>
                </section> --}}

            <div class="modal fade all-amenties" id="all-amenties" tabindex="-1" aria-modal="true" role="dialog"
                data-bs-backdrop="static">
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content pb-0">
                        <div class="modal-header border-0 ">
                            <h4 class="modal-title mx-auto">{{ translate('listing_details.everything_included')   }} </h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                        </div>
                        <div class="modal-body pb-lg-10 px-lg-10 pt-4 pb-0">
                            <div class="parent-box row g-0 align-items-start">
                                @foreach ($listing->amenity_detail ?? [] as $amenities)
                                    <div class="col-md-12 divider pb-3">
                                        <div class="box d-flex gap-3 align-items-center" data-aos="fade">
                                            <img src="{{ asset('website') . '/' . $amenities->image }}"
                                                alt="" height="20px" width="20px">
                                            <div class="amenity-data ">
                                                <span>{{ $amenities->name ?? '' }}</span>
                                                <p class="amenties_desc fs-14 m-0 text-black-50">
                                                    {{ $amenities->description ?? 'No description available' }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                                {{-- @foreach ($listing->custom_amenities ?? [] as $custom_amenities)
                                    <div class="col-md-12 divider pb-3">
                                        <div class="box d-flex gap-3 align-items-start" data-aos="fade">
                                            <img src="{{ asset('website/images/bath.svg') }}" alt=""
                                                height="20px" width="20px">
                                            <div class="amenity-data ">
                                                <span>{{ $custom_amenities->name }}</span>
                                                <p class="amenties_desc fs-14 m-0 text-black-50">Lorem ipsum is
                                                    a dummy text</p>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach --}}
                            </div>
                        </div>
                        {{-- <div class="modal-footer border-0">
                                <button class="btn button login btn-block mb-4 action-button" data-bs-dismiss="modal">Cancel</button>
                            </div> --}}
                    </div>
                </div>
            </div>

            <!-- Modal -->
            <div class="modal report fade" id="report_modal" tabindex="-1" aria-labelledby="reportHeading"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title mx-auto" id="reportHeading">{{ translate('listing_details.report')  }} </h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                        </div>
                        <form action="{{ route('report_form') }}" method="POST">
                            @csrf
                            <div class="modal-body">
                                <input type="hidden" name="booking_id" id="booking_id">
                                <div class="mb-3">
                                    <input type="text" name="subject" class="form-control" placeholder="Subject">
                                </div>
                                <div class="mb-3">
                                    <textarea class="form-control" name="report_sdescription" rows="7" id="" placeholder="Description"></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                {{-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button> --}}
                                <button type="submit" class="bg_black btn button1 white">{{ translate('listing_details.submit')  }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 tab-pane fade mobile" id="mobile" role="tabpanel">

            <div class="mockup-container scrollable-section">
                {{-- <div class="screen"> --}}

                {{-- <svg width="294" height="608" viewBox="0 0 294 608" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M244.679 607.819H49.155C23.6737 607.819 3.0179 586.56 3.0179 560.335V244.765V198.367V186.394V139.996V119.976V96.1596V47.6223C3.0179 21.3973 23.6737 0.138758 49.155 0.138758H244.679C270.161 0.138758 290.816 21.3973 290.816 47.6223V560.335C290.816 586.56 270.161 607.819 244.679 607.819Z" fill="#5E465D"/>
                    <path d="M243.521 603.18H50.3136C26.8843 603.18 7.89153 583.633 7.89153 559.52V48.4369C7.89153 24.3238 26.8843 4.77677 50.3136 4.77677H243.521C266.95 4.77677 285.943 24.3238 285.943 48.4369V559.52C285.943 583.633 266.95 603.18 243.521 603.18Z" fill="#1D1D1B"/>
                    </svg> --}}

                <img class="phone_frame" src="{{ asset('website') }}/images/iphone_frame.svg" alt="">

                <div class="html-content">

                    <section class="sec1_detail py-2 overflow-hidden">
                        <div class="container p-0">
                            <div class="row listing_meta align-items-center">
                                <div class="col-12 py-3" data-aos="fade">
                                    <h2 class="preview_listingTitle" data-aos="fade-down">{{ $listing->name }}</h2>
                                    <div class="rating d-flex align-items-center flex-wrap">
                                        <i class="fas fa-star pe-sm-2 pe-1 "></i>
                                        <p> <i class="fas fa-star"></i>
                                            @if (count($listing->user->provider_review ?? []) < 5)
                                                {{ translate('listing_details.new_host') }}  

                                            @else
                                                {{ count($listing->user->provider_review ?? []) }}
                                                {{ __('website.reviews') }}
                                            @endif
                                        </p>
                                    </div>
                                    <p class="px-sm-3 px-1 m-0 v_divide preview_listing_location">
                                        {{ $listing->address->city ?? '' }},
                                        {{ $listing->address->state ?? '' }},
                                        {{ $listing->address->country ?? '' }}</p>
                                </div>
                            </div>
                            <div class="col-12 pb-3 text-sm-end " data-aos="fade">
                                <a href="javascript:void(0)" class="report-listing">
                                    <img src="{{ asset('website/images/flag.svg') }}" alt="">
                                    <span>{{ translate('listing_details.report_listing') }}   </span>
                                </a>
                            </div>
                            <div class="col-md-12 d-flex justify-content-between gap-md-0 gap-3">
                                <div class="d-flex" data-aos="fade-right">
                                    <div class="user_img me-2">
                                        <img class="img-fluid"
                                            src="{{ asset('website') . '/' . ($listing->user->avatar ?? 'user-profile/dTF64fhJFRQcn80zssSZ1tfPmUOqhUM4T6Ov4DHA.png') }}"
                                            alt="profile image">
                                    </div>
                                    <div class="user_info">
                                        <h5 class="mt-0">{{ translate('listing_details.hosted_by') }}   {{ $listing->user->name ?? '' }}</h5>
                                        <p> <i class="fas fa-star"></i>
                                            @if (count($listing->user->provider_review ?? []) < 5)
                                          {{ translate('listing_details.new_host') }}  
                                            @else
                                                {{ count($listing->user->provider_review ?? []) }}
                                              {{ translate('listing_details.reviews') }}  
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="btn_info detail_contact_host d-flex align-items-center"
                                    data-aos="fade-left">
                                    {{-- @auth
                                                @if ($booking_check) --}}
                                    <a href="javascript:void(0)"
                                        class="button me-md-3 me-1 detail_contact_host_btn">{{ __('website.contact_host') }}</a>
                                    {{-- @endif
                                            @endauth --}}
                                    {{-- <i class="far fa-heart ps-3"></i> --}}
                                    <input type="checkbox" class="heart d-none" value="{{ $listing->ids ?? '' }}"
                                        id="wishlist{{ $listing->ids ?? '' }}"
                                        @if ($listing->wishlist ?? '') checked @endif>

                                    <label for="wishlist{{ $listing->ids ?? '' }}">
                                        <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M12.62 21.3944C12.28 21.5144 11.72 21.5144 11.38 21.3944C8.48 20.4044 2 16.2744 2 9.27436C2 6.18436 4.49 3.68436 7.56 3.68436C9.38 3.68436 10.99 4.56436 12 5.92436C12.5138 5.23023 13.183 4.66608 13.954 4.2771C14.725 3.88812 15.5764 3.68512 16.44 3.68436C19.51 3.68436 22 6.18436 22 9.27436C22 16.2744 15.52 20.4044 12.62 21.3944Z"
                                                stroke="#4A4A4A" stroke-width="1.5" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row py-3 listing_info">
                            <div class="col-12 pb-4 listing_gallery">
                                <div style="--swiper-navigation-color: #fff; --swiper-pagination-color: #fff"
                                    class="swiper mySwiper2Mobile">
                                    <div class="swiper-wrapper pb-3">
                                        @forelse ($listing->gallery_images ?? [] as $gallery_image )
                                            @if ($gallery_image->type == 'image' || $gallery_image->type == 'video')
                                                <div class="swiper-slide">
                                                    @if ($gallery_image->type == 'image')
                                                        <div class="slide_img">
                                                            <img class="img-fluid"
                                                                src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                                onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                                        </div>
                                                    @elseif ($gallery_image->type == 'video')
                                                        <div class="slide_img">
                                                            <video width="100%" height="100%">
                                                                <source
                                                                    src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                                    type="video/mp4">
                                                                {{ __('website.your_browser_not_support_the_html_video') }}
                                                            </video>
                                                        </div>
                                                    @endif
                                                </div>
                                            @endif
                                        @empty
                                            <div class="slide_img">
                                                <img class="img-fluid" src="{{ asset('website') }}"
                                                    onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                            </div>
                                        @endforelse
                                    </div>
                                    <div class="swiper-button-next"></div>
                                    <div class="swiper-button-prev"></div>
                                </div>
                                <div thumbsSlider="" class="swiper mySwiperMobile">
                                    <div class="swiper-wrapper">
                                        @foreach ($listing->gallery_images ?? [] as $gallery_image)
                                            @if ($gallery_image->type == 'image')
                                                <div class="swiper-slide">
                                                    <div class="slides_img">
                                                        <img class="img-fluid"
                                                            src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                            onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;" />
                                                    </div>
                                                </div>
                                            @elseif ($gallery_image->type == 'video')
                                                <div class="swiper-slide">
                                                    <div class="slides_img">
                                                        <video width="100%" height="100%">
                                                            <source
                                                                src="{{ asset('website') . '/' . $gallery_image->url }}"
                                                                type="video/mp4">
                                                           {{ translate('listing_details.your_browser_not_support_the_html_video') }}  
                                                        </video>
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 ">
                                <div class="row listing_map">
                                    <div class="col-md-12">
                                        <div class="map pb-4" data-aos="fade-left">
                                            {{-- @if (count($listing->reviews) > 0) --}}
                                            <div id="map_location_mobile" style="height:230px;border-radius: 20px">
                                            </div>
                                            {{-- @else
                                                    <div id="map_location" style="height:620px;border-radius: 20px"></div>
                                                    @endif --}}
                                        </div>
                                    </div>
                                </div>
                                <div class="row listing_review">
                                    @if ($category->id == 5)
                                        <div class="col-md-12">
                                            <div class="d-flex justify-content-between">
                                                <h6 class="fs-18">{{ __('website.experience') }}</h6>
                                                {{-- <a href="#!" class="fs-14 text-decoration-none">View All</a> --}}
                                                <button type="button" class="modal_btn" data-bs-toggle="modal"
                                                    data-bs-target="#experience">
                                                    {{ __('website.view_all') }}
                                                </button>
                                            </div>
                                            @if ($listing->experiences)
                                                @foreach ($listing->experiences as $experience)
                                                    @if ($loop->first)
                                                        <div class="info px-sm-4 px-2 pt-4 pb-4 mb-4 medical_info">
                                                            <table class="table company_info">
                                                                <tbody>
                                                                    <tr>
                                                                        <th class="light_bold fs-16 b-none">
                                                                          {{ translate('listing_details.company_name') }} :</th>
                                                                        <td class="normal b-none">
                                                                            {{ $experience->company_name ?? '' }}
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th class="light_bold fs-16 b-none">
                                                                            {{ translate('listing_details.desigination') }}  :</th>
                                                                        <td class="normal b-none">
                                                                            {{ $experience->designation_title }}
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th class="light_bold fs-16 b-none">
                                                                           {{ translate('listing_details.date_from') }}:</th>
                                                                        <td class="normal b-none">
                                                                            {{ $experience->date_from }}</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <th class="light_bold fs-16 b-none">
                                                                            {{ translate('listing_details.date_to') }}:</th>
                                                                        <td class="normal b-none">
                                                                            {{ $experience->date_to }}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <div class="detail_info py-3">
                                                                <h6 class="fs-16">
                                                                {{ translate('listing_details.description') }} 
                                                                </h6>
                                                                <p> {{ $experience->designation_description }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endforeach
                                            @endif
                                        </div>
                                    @else
                                        <div class="col-md-12">
                                            <div class="info px-4 pt-4 pb-4">
                                                <h5
                                                    class="py-sm-3 py-1 d-flex justify-content-between gap-1 flex-wrap">
                                                    <span>{{ translate('listing_details.overall_rating') }}</span>
                                                    <span class="rating">
                                                        <i class="fas fa-star"></i>
                                                        <span class="fs-26 light-bold">4.5 /</span> 5.0
                                                    </span>
                                                </h5>
                                                <div class="d-flex justify-content-between">
                                                    <div class="d-flex">
                                                        <div class="user_img me-md-3 me-2">
                                                            <img width="70" height="70"
                                                                class="img-fluid rounded-circle"
                                                                src="{{ asset('website') . '/' . ($listing->latest_review->user->avatar ?? 'user-profile/dTF64fhJFRQcn80zssSZ1tfPmUOqhUM4T6Ov4DHA.png') }}"
                                                                alt="user">
                                                        </div>
                                                        <div class="user_info">
                                                            <h6 class="">{{ translate('listing_details.john_doe') }} </h6>
                                                            <p> 5/Jan/2025</p>
                                                        </div>
                                                    </div>
                                                    <div class="user_rating">
                                                        <p> <i class="fas fa-star"></i>
                                                            {{ $listing->latest_review->rating ?? '4.2' }} </p>
                                                    </div>
                                                </div>
                                                <div class="detail_info py-3">
                                                    <p>{{ $listing->latest_review->comment ?? 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry standard dummy text ever since the 1500s.' }}
                                                    </p>
                                                </div>
                                                {{-- <button type="button" class="button" data-bs-toggle="modal"
                                                            data-bs-target="#review">
                                                            {{ __('website.view_all_reviews') }}
                                                        </button> --}}
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </section>
                    <section class="sec-2-detail">
                        <div class="container p-0">
                            <div class="row g-0">
                                {{-- description --}}
                                <div class="col-md-12  inner_detail listing_description divider listing_data">
                                    <div class="list_desc">
                                        {!! $listing->description ?? '' !!}
                                    </div>
                                    {{-- <a href="#!" class="button read-more">{{ __('website.read_more') }}</a> --}}
                                </div>
                                {{-- description end --}}
                                {{-- --------------------- Tour detail 1 --------------------- --}}
                                @includeWhen(
                                    $listing->category->id == 1,
                                    'website.template.listing-detail.tour',
                                    compact('listing'))
                                {{-- --------------------- Tour detail 1 end --------------------- --}}
                                {{-- --------------------- boat detail 2 --------------------- --}}
                                @includeWhen(
                                    $listing->category->id == 2,
                                    'website.template.listing-detail.boat',
                                    compact('listing'))
                                {{-- --------------------- boat detail 2 end --------------------- --}}
                                {{-- --------------------- car detail 3 --------------------- --}}
                                @includeWhen(
                                    $listing->category->id == 3,
                                    'website.template.listing-detail.car',
                                    compact('listing'))
                                {{-- --------------------- car detail 3 end --------------------- --}}
                                {{-- --------------------- house detail --------------------- --}}
                                @includeWhen(
                                    $listing->category->id == 4,
                                    'website.template.listing-detail.house',
                                    compact('listing'))
                                {{-- --------------------- house detail end --------------------- --}}
                                {{-- Not for Medical Rules and Cancelation --}}
                                @if ($category->id != 5)
                                    {{-- Rule --}}
                                    @if (isset($listing->detail->pet) || isset($listing->rules))
                                        <div class="col-lg-12 divider listing_data listing_rule">
                                            <div class="amenities-box">
                                                <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.rules') }}</h3>
                                                {{-- @if (isset($listing->rules)) --}}
                                                <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                                    {{-- for pets --}}
                                                    {{-- @if ($listing->detail->pet == 'yes')
                                                        <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                                                            <img src="{{ asset('website/images/square-check.svg') }}"
                                                                height="20px" width="20px" alt="Allowed">
                                                            <span>Pets</span>
                                                        </div>
                                                    @endif --}}
                                                    @forelse ($listing->rules as $rule)
                                                        @if (isset($rule->title))
                                                            @if ($rule->allow == 'yes')
                                                                <div class="box allowed d-flex gap-2 align-items-center"
                                                                    data-aos="fade">
                                                                    <img src="{{ asset('website/images/square-check.svg') }}"
                                                                        height="20px" width="205px" alt="Allowed">
                                                                    <span>{{ $rule->title }}</span>
                                                                </div>
                                                            @endif
                                                        @endif
                                                    @endforeach
                                                </div>
                                                {{-- @endif --}}
                                                {{-- @if (isset($listing->detail->rule)) --}}
                                                <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                                                    {{-- for pets --}}
                                                    {{-- @if ($listing->detail->pet == 'no')
                                                        <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                                                            <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                                height="20px" width="20px" alt="Not Allowed">
                                                            <span>Pets</span>
                                                        </div>
                                                    @endif --}}
                                                    @forelse ($listing->rules as $rule)
                                                        @if (isset($rule->title))
                                                            @if ($rule->allow == 'no')
                                                                <div class="box not-allowed d-flex gap-2 align-items-center"
                                                                    data-aos="fade">
                                                                    <img src="{{ asset('website/images/ticksquare.svg') }}"
                                                                        height="20px" width="20px" alt="Not Allowed">
                                                                    <span>{{ $rule->title }}</span>
                                                                </div>
                                                            @endif
                                                        @endif
                                                    @endforeach
                                                </div>
                                                {{-- @endif --}}
                                            </div>
                                        </div>
                                    @endif
                                    {{-- Rule end --}}
                                    {{-- Notes --}}
                                    @if (isset($listing->notes) && !$listing->notes->isEmpty())
                                        <div class="col-lg-12 divider listing_data listing_notes">
                                            <div class="amenities-box">
                                                <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.you_need_to_know') }} 
                                                </h3>
                                                <ol class="parent-box pt-2">
                                                    @forelse ($listing->notes as $note)
                                                        <li class="pb-3 fs-14" data-aos="fade">
                                                            <span>{{ $note->name }}</span>
                                                        </li>
                                                    @endforeach
                                                </ol>
                                            </div>
                                        </div>
                                    @endif
                                    {{-- Notes end --}}
    
                                    {{-- Cancelation  --}}
                                    <div class="col-md-12">
                                        <div class="row detail-rw g-0 listing_data pt-5 listing_cancelation">
                                            <div class="col-lg-12">
                                                <div class="details-box">
    
                                                    <div class="box d-flex gap-2 align-items-start">
                                                        <div class="icon">
                                                            <img src="{{ asset('website/images/ban.svg') }}"
                                                                alt="">
                                                        </div>
                                                        <div class="content w-100">
                                                            <h6 class="mt-0 semi-bold">{{ translate('listing_details.cancelation_policy') }} </h6>
                                                        </div>
                                                    </div>
    
                                                    <ul class="cancelation_policy_timeline_wrapper">
                                                        @if ($listing->detail->cancellation_policy == 'Moderate' || $listing->detail->cancellation_policy == 'Strict')
                                                            <li class="timeline_step">
                                                                <span class="refund_date">{{ translate('listing_details.today') }}  </span>
                                                            </li>
                                                            <li class="timeline_step">
                                                                <span
                                                                    class="refund_date">{{ $dates['increment1'] }}</span>
                                                            </li>
                                                            <li class="timeline_step">
                                                                <span
                                                                    class="refund_date">{{ $dates['increment2'] }}</span>
                                                            </li>
                                                            <li class="timeline_step">
                                                                <span class="refund_date">{{ translate('listing_details.check_in') }}  </span>
                                                            </li>
                                                        @else
                                                            <li class="timeline_step">
                                                                <span class="refund_date">{{ translate('listing_details.today') }}</span>
                                                            </li>
                                                            <li class="timeline_step">
                                                                <span
                                                                    class="refund_date">{{ $dates['increment1'] }}</span>
                                                            </li>
                                                            <li class="timeline_step">
                                                                <span class="refund_date">{{ translate('listing_details.check_in') }}  </span>
                                                            </li>
                                                        @endif
                                                    </ul>
    
                                                    <div class="timeline_detailed_wrapper">
                                                        @if ($listing->detail->cancellation_policy == 'Moderate' || $listing->detail->cancellation_policy == 'Strict')
                                                            <div class="timeline_detail_single">
                                                                <div class="timeline_date">
                                                                    <div class="date_tag">
                                                                        <span>{{translate('listing_details.before')  }} </span>
                                                                        <h5>{{ $dates['increment1'] }}</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="description">
                                                                    <h5>{{ translate('listing_details.full_refund') }}</h5>
                                                                    <p>{{ translate('listing_details.cancel_before') }}  
                                                                        {{ $dates['increment1'] }} {{ translate('listing_details.full_refund_time_note') }}  
</p>
                                                                </div>
                                                            </div>
                                                            <div class="timeline_detail_single">
                                                                <div class="timeline_date">
                                                                    <div class="date_tag">
                                                                        <span>{{translate('listing_details.before')  }} </span>
                                                                        <h5>{{ $dates['increment2'] }}</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="description">
                                                                    <h5>{{ translate('listing_details.partial_refund') }} </h5>
                                                                    <p>{{ translate('listing_details.cancel_before_partial_refund') }}  
                                                                        {{ $dates['increment2'] }} {{ translate('listing_details.full_refund_time_note') }}  
</p>
                                                                </div>
                                                            </div>
                                                            <div class="timeline_detail_single">
                                                                <div class="timeline_date">
                                                                    <div class="date_tag">
                                                                        <span>{{ translate('listing_details.after') }}  </span>
                                                                        <h5>{{ $dates['increment2'] }}</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="description">
                                                                    <h5>{{ translate('listing_details.no_refund') }}  </h5>
                                                                    <p>After {{ translate('listing_details.no_refund_after_that') }}  </p>
                                                                </div>
                                                            </div>
                                                        @else
                                                            <div class="timeline_detail_single">
                                                                <div class="timeline_date">
                                                                    <div class="date_tag">
                                                                        <span>{{translate('listing_details.before')  }} </span>
                                                                        <h5>{{ $dates['increment1'] }}</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="description">
                                                                    <h5>{{ translate('listing_details.full_refund') }}</h5>
                                                                    <p>{{ translate('listing_details.cancel_before') }}  
                                                                        {{ $dates['increment1'] }} {{ translate('listing_details.full_refund_time_note') }}</p>
                                                                    </p>
                                                                </div>
                                                            </div>
                                                            <div class="timeline_detail_single">
                                                                <div class="timeline_date">
                                                                    <div class="date_tag">
                                                                        <span>{{ translate('listing_details.after') }}  </span>
                                                                        <h5>{{ $dates['increment1'] }}</h5>
                                                                    </div>
                                                                </div>
                                                                <div class="description">
                                                                    <h5>{{ translate('listing_details.no_refund') }}  </h5>
                                                                    <p>{{ translate('listing_details.after') }}   {{ translate('listing_details.no_refund_after_that') }}  </p>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {{-- End Cancelation  --}}
    
                                @endif
                            </div>
                            {{-- End Rules and Cancelation --}}
                            {{-- {{ translate('listing_details.url') }} --}}
                            @if (in_array($category->id, [5]))
                                <div class="row py-3 listing_url g-0">
                                    <div class="col-md-12">
                                        <div class="info py-2">
                                            <h5 class="ps-0 pb-2" data-aos="fade">{{ translate('listing_details.url') }}</h5>
                                            <a href="#!" class="fs-16 blue">{{ $listing->url ?? 'Not provided' }}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif
                            {{-- End {{ translate('listing_details.url') }} --}}
                        </div>
                    </section>
                </div>


                <div class="modal fade all-amenties" id="all-amenties" tabindex="-1" aria-modal="true"
                    role="dialog" data-bs-backdrop="static">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content pb-0">
                            <div class="modal-header border-0 ">
                                <h4 class="modal-title mx-auto">{{ translate('listing_details.everything_included') }}  </h4>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                    aria-label="Close"></button>
                            </div>
                            <div class="modal-body pb-lg-10 px-lg-10 pt-4 pb-0">
                                <div class="parent-box row g-0 align-items-start">
                                    @foreach ($listing->amenity_detail ?? [] as $amenities)
                                        <div class="col-md-12 divider pb-3">
                                            <div class="box d-flex gap-3 align-items-center" data-aos="fade">
                                                <img src="{{ asset('website/images/bedroom.svg') }}" alt=""
                                                    height="20px" width="20px">
                                                <div class="amenity-data ">
                                                    <span>{{ $amenities->name ?? '' }}</span>
                                                    <p class="amenties_desc fs-14 m-0 text-black-50">{{ translate('listing_details.lorem_dummy_text') }}  </p>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                    {{-- @foreach ($listing->custom_amenities ?? [] as $custom_amenities)
                                            <div class="col-md-12 divider pb-3">
                                                <div class="box d-flex gap-3 align-items-start" data-aos="fade">
                                                    <img src="{{ asset('website/images/bath.svg') }}" alt=""
                                                        height="20px" width="20px">
                                                    <div class="amenity-data ">
                                                        <span>{{ $custom_amenities->name }}</span>
                                                        <p class="amenties_desc fs-14 m-0 text-black-50">Lorem ipsum is
                                                            a dummy text</p>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach --}}
                                </div>
                            </div>
                            {{-- <div class="modal-footer border-0">
                                        <button class="btn button login btn-block mb-4 action-button" data-bs-dismiss="modal">Cancel</button>
                                    </div> --}}
                        </div>
                    </div>
                </div>

            </div>
        </div>

    </div>
</div>
</div>


<script>
    function initMap() {
        var latitude = {{ $listing->lat }};
        var longitude = {{ $listing->lng }};
        const location = {
            lat: latitude,
            lng: longitude
        };
        const mapPreview = new google.maps.Map(document.getElementById("map_location"), {
            zoom: 16,
            center: location,
        });
        const marker = new google.maps.Marker({
            position: location,
            map: mapPreview,
            // title: "Your Location",
        });
    }

    function initMapMobile() {
        var latitude = {{ $listing->lat }};
        var longitude = {{ $listing->lng }};
        const locationMobile = {
            lat: latitude,
            lng: longitude
        };
        const mapPreviewMobile = new google.maps.Map(document.getElementById("map_location_mobile"), {
            zoom: 16,
            center: locationMobile,
        });
        const markerMobile = new google.maps.Marker({
            position: locationMobile,
            map: mapPreviewMobile,
            // title: "Your Location",
        });
    }

    var swiper = new Swiper(".mySwiper", {
        // spaceBetween: 10,
        // slidesPerView: 4,
        freeMode: true,
        watchSlidesProgress: true,
        breakpoints: {
            200: {
                slidesPerView: 2,
                spaceBetween: 10,
            },
            320: {
                slidesPerView: 3,
                spaceBetween: 10,
            },
            600: {
                slidesPerView: 4,
                spaceBetween: 10,
            },

            767: {
                slidesPerView: 6,
                spaceBetween: 10,
            },
        },
    });
    var swiper2 = new Swiper(".mySwiper2", {
        spaceBetween: 10,
        autoplay: {
            delay: 2500,
            disableOnInteraction: true,
            pauseOnMouseEnter: true,
        },
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        },
        thumbs: {
            swiper: swiper,
        },
    });

    var swiper3 = new Swiper(".mySwiper2Mobile", {
        spaceBetween: 10,
        autoplay: {
            delay: 2500,
            disableOnInteraction: true,
            pauseOnMouseEnter: true,
        },
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        },
        thumbs: {
            swiper: swiper4,
        },
    });

    var swiper4 = new Swiper(".mySwiperMobile", {
        spaceBetween: 10,
        slidesPerView: 3,
        freeMode: true,
        watchSlidesProgress: true,
        // breakpoints: {
        //     200: {
        //         slidesPerView: 2,
        //         spaceBetween: 10,
        //     },
        //     320: {
        //         slidesPerView: 3,
        //         spaceBetween: 10,
        //     },
        //     600: {
        //         slidesPerView: 4,
        //         spaceBetween: 10,
        //     },

        //     767: {
        //         slidesPerView: 6,
        //         spaceBetween: 10,
        //     },
        // },
    });

    $(document).ajaxComplete(function() {

        function setDynamicHeight() {
            var screenHeight = $(window).height();
            console.log(screenHeight);
            var scrollableSections = $('fieldset.active .scrollable-section');

            if (scrollableSections.length > 0) {
                if (scrollableSections.length >= 2) {
                    scrollableSections.each(function(index, element) {
                        var sectionTop = $(element).offset().top;
                        console.log(`Section ${index + 1} top:`, sectionTop);
                        var heightTopResult = screenHeight - sectionTop;
                        console.log(`Height Top Result for Section ${index + 1}:`, heightTopResult);
                        heightTopResult = heightTopResult - 145;
                        $(element).css('height', heightTopResult);
                        $(element).css('max-height', heightTopResult);
                        console.log(`Total screen height for Section ${index + 1}:`, heightTopResult);
                    });
                } else {
                    var sectionTop = $('fieldset.active .scrollable-section').offset().top;
                    console.log(sectionTop);
                    var heightTopResult = screenHeight - sectionTop;
                    console.log(heightTopResult);
                    heightTopResult = heightTopResult - 145;
                    $('fieldset.active .scrollable-section').css('height', heightTopResult);
                    $('fieldset.active .scrollable-section').css('max-height', heightTopResult);
                    console.log("Total screen height:", heightTopResult);
                }
            }
        }


        // setTimeout(() => {
        setDynamicHeight();
        // }, 1200);

        $(".listing_stepper .next").on("click", function() {
            setDynamicHeight();
        });

        $(".listing_stepper .previous").on("click", function() {
            setDynamicHeight();
        });

        $(window).on("resize", setDynamicHeight);
        $(window).on("scroll", setDynamicHeight);

    });

    $(document).ready(function() {

        function adjustZoom() {
            var container = $('.mockup-container');
            var parent = container.parent(); // Get the parent container

            var parentWidth = parent.width();
            var parentHeight = parent.height();
            var mockupWidth = container[0].scrollWidth; // Get full content width
            var mockupHeight = container[0].scrollHeight; // Get full content height

            var zoomWidth = parentWidth / mockupWidth;
            var zoomHeight = parentHeight / mockupHeight;

            var zoomLevel = Math.min(zoomWidth, zoomHeight); // Use the smaller zoom value

            container.css('zoom', zoomLevel - 0.015);
        }


        function mobileViewClassChange(element) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        adjustZoom();
                    }
                });
            });
            const config = {
                attributes: true
            };
            observer.observe(element, config);
        }

        $('#mobile').each(function() {
            mobileViewClassChange(this);
        });


        adjustZoom(); // Apply zoom on page load

        $(window).resize(function() {
            adjustZoom(); // Update zoom on window resize
        });
    });

    // GSAP Animation
    gsap.to(".mockup-container", {
        opacity: 1,
        scale: 1,
        duration: 1,
        ease: "power3.out",
    });

    gsap.registerPlugin(ScrollTrigger);

    gsap.from(".html-content", {
        y: 100,
        opacity: 0,
        duration: 4,
        delay: 2,
        ease: "elastic.out(1,0.3)",
        scrollTrigger: {
            trigger: ".html-content",
            start: "top 80%",
            end: "top 30%",
            toggleActions: "play none none none",
            markers: false
        }
    });
</script>

<script src="{{ url('google-map') }}?libraries=places&callback=initMap"></script>
<script src="{{ url('google-map') }}?libraries=places&callback=initMapMobile"></script>
