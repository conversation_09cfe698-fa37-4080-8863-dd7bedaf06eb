@extends('website.layout.master')

@push('css')
    

    <style>
        footer {display: none}
        html {overflow-y: hidden;}
    </style>

@endpush


@section('content')
    

    <section class="messenger_main_sec">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="inner_section_messenger_main_col">


                        {{-- <div class="row">
                            <div class="col-md-12">
                                <div class="main_heading_wrapper">
                                    <div class="page_title">
                                        <h2>Inbox</h2>
                                    </div>
                                    <div class="contact_button">
                                        <a class="btn button1 call_us_now_btn" href="javascript:void(0)">Call Us Now</a>
                                    </div>
                                </div>
                            </div>
                        </div> --}}

                        <div class="row">
                            <div class="col-md-12">
                                <div class="messenger_wrapper">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="inner_section_messenger_sidebar">
                                                <div class="sidebar_chats_toggle_buttons_wrapper">
                                                    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link active" id="pills-conversations-tab" data-bs-toggle="pill" data-bs-target="#pills-conversations" type="button" role="tab" aria-controls="pills-conversations" aria-selected="true">Conversations</button>
                                                        </li>
                                                        <li class="nav-item" role="presentation">
                                                            <button class="nav-link" id="pills-support-tab" data-bs-toggle="pill" data-bs-target="#pills-support" type="button" role="tab" aria-controls="pills-support" aria-selected="false">LuxuStars Support</button>
                                                        </li>
                                                    </ul>
                                                    <div class="tab-content" id="pills-tabContent">
                                                        <div class="tab-pane fade show active" id="pills-conversations" role="tabpanel" aria-labelledby="pills-conversations-tab">
                                                            <div class="inner_section_chat_threads">

                                                                <div class="chat_threads_search">
                                                                    <i class="fas fa-search"></i>
                                                                    <input type="search" class="form-control" aria-describedby="emailHelp" placeholder="Search">
                                                                </div>

                                                                <div class="chat_threads_wrapper scrollable-section">

                                                                    @for ($i = 0; $i < 10; $i++)
                                                                        
                                                                        <div class="single_chat_thread">
                                                                            <div class="user_profile_picture">
                                                                                <img src="{{ asset('website') }}/images/user1.png" alt="User Profile">
                                                                            </div>
                                                                            <div class="chat_details_wrapper">
                                                                                <div class="username_date_wrapper">
                                                                                    <div class="user_name">
                                                                                        <h6>User Name</h6>
                                                                                    </div>
                                                                                    <div class="chat_date">
                                                                                        <span>12:35 PM</span>
                                                                                    </div>
                                                                                </div>
                                                                                {{-- <div class="open_chat_icon">
                                                                                    <i class="fas fa-chevron-right"></i>
                                                                                </div> --}}
                                                                                <div class="chat_preview">
                                                                                    <p>Thank you very much, I am waiting for the parcel.</p>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                    @endfor

                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="tab-pane fade" id="pills-support" role="tabpanel" aria-labelledby="pills-support-tab">
                                                            <div class="inner_section_chat_threads">

                                                                <div class="chat_threads_search">
                                                                    <a href="javascript:void(0)" class="btn start_new_convo_btn">Start conversation</a>
                                                                </div>

                                                                <div class="chat_threads_wrapper scrollable-section">

                                                                    @for ($i = 0; $i < 10; $i++)
                                                                        
                                                                        <div class="single_chat_thread">
                                                                            <div class="user_profile_picture">
                                                                                <img src="{{ asset('website') }}/images/user1.png" alt="User Profile">
                                                                            </div>
                                                                            <div class="chat_details_wrapper">
                                                                                <div class="username_date_wrapper">
                                                                                    <div class="user_name">
                                                                                        <h6>User Name</h6>
                                                                                    </div>
                                                                                    <div class="chat_date">
                                                                                        <span>12:35 PM</span>
                                                                                    </div>
                                                                                </div>
                                                                                {{-- <div class="open_chat_icon">
                                                                                    <i class="fas fa-chevron-right"></i>
                                                                                </div> --}}
                                                                                <div class="chat_preview">
                                                                                    <p>Thank you very much, I am waiting for the parcel.</p>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                    @endfor

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="inner_section_chat_messages">
                                                <div class="chat_top_bar">
                                                    <div class="profile_picture_username_wrapper">
                                                        <div class="user_profile_picture">
                                                            <img src="{{ asset('website') }}/images/user1.png" alt="User Profile">
                                                        </div>
                                                        <div class="username">
                                                            <h6>User Name</h6>
                                                        </div>
                                                    </div>
                                                    <div class="chat_options">
                                                        <a class="btn call_us_now_btn" href="javascript:void(0)">Call Us Now</a>
                                                        <a href="javascript:void(0)" class="find_messages_btn"><i class="fas fa-search"></i></a>
                                                        <div class="dropdown">
                                                            <button class="btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="fas fa-ellipsis-v"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#">Translate</a></li>
                                                                <li><a class="dropdown-item" href="#">View Profile</a></li>
                                                                <li><a class="dropdown-item" href="#">End Chat</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="chat_body scrollable-section-2">

                                                    <div class="inner_body">
                                                        {{-- <div class="d-flex flex-column align-items-end justify-content-end"> --}}
                                                            <div class="chat_date_time">
                                                                <p>12:24 PM</p>
                                                            </div>

                                                            @for ($i = 0; $i < 3; $i++)
                                                                <div class="msg msg_right">
                                                                    <div class="chat_bubble">
                                                                        <p class="">Yeah, It’s really good! </p>
                                                                    </div>
                                                                    @if ($i === 2)
                                                                        <div class="user_image">
                                                                            <img src="{{ asset('website') }}/images/user1.png" alt="Last Image" />
                                                                        </div>
                                                                    @endif
                                                                </div>
                                                            @endfor

                                                            @for ($i = 0; $i < 2; $i++)
                                                                <div class="msg msg_left">
                                                                    @if ($i === 1)
                                                                        <div class="user_image">
                                                                            <img src="{{ asset('website') }}/images/user1.png" alt="Last Image" />
                                                                        </div>
                                                                    @endif
                                                                    <div class="chat_bubble">
                                                                        <p class="">I have been using it for a week now and I love it!</p>
                                                                    </div>
                                                                </div>
                                                            @endfor

                                                        {{-- </div> --}}
                                                    </div>

                                                </div>

                                                <div class="chat_footer">
                                                    <div class="typing_attchment_wrapper">
                                                        <input type="text" class="form-control" name="message" id="" placeholder="Type your message....">
                                                        <div class="dropdown">
                                                            <button class="btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="fas fa-paperclip"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#">Photos</a></li>
                                                                <li><a class="dropdown-item" href="#">Videos</a></li>
                                                                <li><a class="dropdown-item" href="#">Files</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="send_button">
                                                        <a class="send_msg_btn" href="javascript:void(0)"><i class="fas fa-paper-plane"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


@endsection


@push('js')
    

<script>

    function setDynamicHeight() {
            var screenHeight = $(window).height();
            // console.log(screenHeight);
            var scrollableSections = $('.messenger_main_sec .scrollable-section');
            var scrollableSections2 = $('.messenger_main_sec .scrollable-section-2');

            if (scrollableSections.length >= 2) {
                scrollableSections.each(function(index, element) {
                    var sectionTop = $(element).offset().top;
                    // console.log(`Section ${index + 1} top:`, sectionTop);
                    var heightTopResult = screenHeight - sectionTop;
                    // console.log(`Height Top Result for Section ${index + 1}:`, heightTopResult);
                    heightTopResult = heightTopResult - 20;
                    $(element).css('height', heightTopResult);
                    $(element).css('max-height', heightTopResult);
                    // console.log(`Total screen height for Section ${index + 1}:`, heightTopResult);
                });
            } else {
                var sectionTop = $('fieldset.active .scrollable-section').offset().top;
                // console.log(sectionTop);
                var heightTopResult = screenHeight - sectionTop;
                // console.log(heightTopResult);
                heightTopResult = heightTopResult - 20;
                $('fieldset.active .scrollable-section').css('height', heightTopResult);
                $('fieldset.active .scrollable-section').css('max-height', heightTopResult);
                // console.log("Total screen height:", heightTopResult);
            }

            if (scrollableSections2.length > 0) {
                    var sectionTop = $('.messenger_main_sec .scrollable-section-2').offset().top;
                    // console.log(sectionTop);
                    var heightTopResult = screenHeight - sectionTop;
                    // console.log(heightTopResult);
                    heightTopResult = heightTopResult - 80;
                    $('.messenger_main_sec .scrollable-section-2').css('height', heightTopResult);
                    $('.messenger_main_sec .scrollable-section-2').css('max-height', heightTopResult);
                    // console.log("Total screen height:", heightTopResult);
            }

        }

        $(document).ready(function() {
            setDynamicHeight();

            $(document).on("click", ".messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item button", function() {
                setTimeout(() => {
                    setDynamicHeight();
                }, 200);
            });

        });

    $(window).on("resize", setDynamicHeight);
    $(window).on("scroll", setDynamicHeight);
</script>


@endpush