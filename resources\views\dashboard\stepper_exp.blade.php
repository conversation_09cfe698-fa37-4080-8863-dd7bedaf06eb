<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords" content="">
    <meta name="author" content="">
    <meta name="description" content="{{ App\CommonSetting::first()->description ?? '' }}">
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <link rel="icon" type="image/png" sizes="16x16"
        href="{{ asset('') }}{{ App\CommonSetting::first()->favicon ?? '' }}">
    <title>{{ App\CommonSetting::first()->title ?? '' }}</title>
    <!-- ===== Bootstrap CSS ===== -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- ===== Plugin CSS ===== -->
    <link href="{{ asset('plugins/components/chartist-js/dist/chartist.min.css') }}" rel="stylesheet">
    <link href="{{ asset('plugins/components/chartist-plugin-tooltip-master/dist/chartist-plugin-tooltip.css') }}"
        rel="stylesheet">
    <link href="{{ asset('plugins/components/toast-master/css/jquery.toast.css') }}" rel="stylesheet">
     <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.0.3/css/font-awesome.css" rel="stylesheet">
    <!-- ===== Animation CSS ===== -->
    <link href="{{ asset('css/animate.css') }}" rel="stylesheet">
    <!-- ===== Custom CSS ===== -->
    <link href="{{ asset('css/common.css') }}" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    @stack('css')

    <!--====== Dynamic theme changing =====-->
 
    @if (session()->get('theme-layout') == 'fix-header')
        <link href="{{ asset('css/style-fix-header.css') }}" rel="stylesheet">
        <link href="{{ asset('css/colors/default.css') }}" id="theme" rel="stylesheet">
    @elseif(session()->get('theme-layout') == 'mini-sidebar')
        <link href="{{ asset('css/style-mini-sidebar.css') }}" rel="stylesheet">
        <link href="{{ asset('css/colors/default.css') }}" id="theme" rel="stylesheet">
    @else
        <link href="{{ asset('css/style-normal.css') }}" rel="stylesheet">
        <link href="{{ asset('css/dashboard.css') }}" rel="stylesheet">
        <link href="{{ asset('css/colors/default.css') }}" id="theme" rel="stylesheet">
    @endif


    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-iconpicker/1.9.0/css/bootstrap-iconpicker.min.css" /> -->
    <!-- ===== Color CSS ===== -->
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        /* @media (min-width: 768px) {.extra.collapse li a span.hide-menu {display: block !important;}
            .extra.collapse.in li a.waves-effect span.hide-menu {display: block !important;}
            .extra.collapse li.active a.active span.hide-menu {display: block !important;}
            ul.side-menu li:hover+.extra.collapse.in li.active a.active span.hide-menu {display: block !important;}} */
        body{ background-color: #f8f9fa; height:100vh;}

        /* #progressbar li {list-style-type: none;font-size: 15px;width: 25%;float: left;position: relative;font-weight: 400;} */
        /* #progressbar li:before {width: 50px;height: 50px;line-height: 45px;display: block;font-size: 20px;
            color: #ffffff;background: lightgray;border-radius: 50%;margin: 0 auto 10px auto;padding: 2px;}
        #progressbar li:after {content: '';width: 100%;height: 2px;background: lightgray;position: absolute;
            left: 0;top: 25px;z-index: -1} */
            /* Make circles that indicate the steps of the form: */
        .is-invalid {border-color: #dc3545;}
    </style>
</head>

<body class="@if (session()->get('theme-layout')) {{ session()->get('theme-layout') }} @endif">
<section class="listing_stepper">
    <form id="msform" action="" class="stepper">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12 p-0">
                    <div class="save-header d-flex">
                        <img src="{{ asset('website') }}/images/logo.png"alt="logo">
                        <button type="submit" class="save-btn">{{ translate('stepper.save_exit') }}</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid">
            <div class="row custom-rw">
                <div class="col-md-12 custom-fieldset">
                    <!-- progressbar -->
                    <!-- <ul id="progressbar">
                        <li class="active" id="account"></li>
                        <li id="personal"><strong>Personal</strong></li>
                        <li id="payment"><strong>Image</strong></li>
                        <li id="confirm"><strong>Finish</strong></li>
                    </ul> --><!-- fieldsets -->
                    <fieldset class="topbar">
                        <div class="white-box">
                            <h2 class="pb-2 text-center">{{ translate('stepper.describe_service') }}</h2>
                            <div class=" top_box1 d-flex justify-content-between py-2 flex-wrap">
                            <div class="box">
                                <input type="radio" class="radio_btn is-invalid" id="box-4" name="category_id"
                                    value="4" required>
                                <label for="box-4">
                                    <div class="find_btn d-flex flex-column align-items-center">
                                        <div class="radio_img">
                                            <img src="http://127.0.0.1:8000/website/category/BaQKuMrDSoxyyr9XuPekvRfJPgsSwqsvO4DHYuYr.png"
                                                alt="">
                                        </div>
                                        {{ translate('stepper.luxury_stays') }}
                                    </div>
                                </label>
                            </div>
                            <div class="box">
                                <input type="radio" class="radio_btn is-invalid" id="box-3" name="category_id"
                                    value="3" required>
                                <label for="box-3">
                                    <div class="find_btn d-flex flex-column align-items-center">
                                        <div class="radio_img">

                                            <img src="http://127.0.0.1:8000/website/category/oNkXs2tj3Lgw6lM5milzdMEFA1DLH7msqVDVjZWr.png"
                                                alt="">
                                        </div>
                                        {{ translate('stepper.luxury_car') }}
                                    </div>
                                </label>
                            </div>
                            <div class="box">
                                <input type="radio" class="radio_btn is-invalid" id="box-2" name="category_id"
                                    value="2" required>
                                <label for="box-2">
                                    <div class="find_btn d-flex flex-column align-items-center">
                                        <div class="radio_img">

                                            <img src="http://127.0.0.1:8000/website/category/1WxhHA307lU6gDUkOymM4WNPGJf8Z5igsMtZzqEq.png"
                                                alt="">
                                        </div>
                                        {{ translate('stepper.luxury_boat') }}
                                    </div>
                                </label>
                            </div>
                            <div class="box">
                                <input type="radio" class="radio_btn is-invalid" id="box-1" name="category_id"
                                    value="1" required>
                                <label for="box-1">
                                    <div class="find_btn d-flex flex-column align-items-center">
                                        <div class="radio_img">
                                            <img src="http://127.0.0.1:8000/website/category/0kQpkTva6sj9uJsHHFBQsAbnDFz73An9hQ6OG3K3.png"
                                                alt="">
                                        </div>
                                        {{ translate('stepper.experiences') }}
                                    </div>
                                </label>
                            </div>
                            {{-- <div class="form-outline mb-4 " >
                                <input type="radio" class="" name="idType" value="idCard" id="idCard" checked />
                                <label for="idCard">Identity Card</label>
                            </div>
                            <div class="form-outline mb-4 ">
                                    <input type="radio" class="" name="idType" value="passport" id="passport" />
                                    <label for="passport">Passport</label>
                            </div>
                            <div class="form-outline mb-4 ">
                                    <input type="radio" class="" name="idType" value="driverLicence"
                                    id="driverLicence" />
                                <label for="driverLicence">Driver's Licence</label>
                            </div> --}}
                            </div>
                        </div>
                        <!-- <div class="error text-center text-danger blink-text"></div> -->
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1 mb-4"
                            value="{{ translate('stepper.next') }}" />
                    </fieldset>
                    <fieldset> 
                        <div class="row">
                            <div class="col-md-6 fieldset-2" style="text-align:left;">
                                <h4 class="">{{ translate('stepper.step_1') }}</h4>
                                <h2 class="semi_bold">{{ translate('stepper.experience_intro') }}</h2>
                                <p class="fs-18">
                                    {{ translate('stepper.experience_detail') }}                                </p>
                                <label for="#property">{{ translate('stepper.type_experience') }}</label>
                                <select name="property" id="property">
                                    <option value="" disabled selected>{{ translate('stepper.selection_field') }}</option>
                                    <option value="pakistan">{{ translate('stepper.pakistan') }}</option>
                                    <option value="usa">{{ translate('stepper.usa') }}</option>
                                    <option value="uae">{{ translate('stepper.uae') }}</option>
                                    <option value="india">{{ translate('stepper.india') }}</option>
                                </select>
                                <div class="error text-center text-danger blink-text pb-3"></div>
                            </div>
                            <div class="col-md-6 fieldset-2">
                                <div class="pot_img">
                                    <img src="{{ asset('website') }}/images/experience.png" alt="Property Image"
                                        class="img-fluid">
                                </div>
                            </div>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset>
                        <div class="inner-box1">
                            <h2 class="semi_bold text-center">{{ translate('stepper.confirm_address') }}</h2>
                            <p class="fs-18 text-center">{{ translate('stepper.address_note') }}</p>
                            <div class="address">
                                <label for="#country">{{ translate('stepper.country_region') }}</label>
                                <select name="property" id="country" required>
                                    <option value="" disabled selected>{{ translate('stepper.country_selection') }}</option>
                                    <option value="pakistan">{{ translate('stepper.pakistan') }}</option>
                                    <option value="usa">{{ translate('stepper.usa') }}</option>
                                    <option value="uae">{{ translate('stepper.uae') }}</option>
                                    <option value="india">{{ translate('stepper.india') }}</option>
                                </select>
                                <div class="error text-center text-danger blink-text pb-3"></div>
                                <div class="form-outline mb-4 files back_id">
                                    <label for="#street">{{ translate('stepper.street_address') }}</label>
                                    <input type="text" placeholder="{{ translate('stepper.enter_street') }}" class="form-control step_input"
                                        name="street" id="street" required />
                                </div>
                                <div class="error text-center text-danger blink-text pb-3"></div>
                                <div class="form-outline mb-4 files back_id">
                                    <label for="#suit">{{ translate('stepper.apartment_label') }}</label>
                                    <input type="text" placeholder="{{ translate('stepper.enter_suite') }}" class="form-control step_input"
                                        name="suit" id="suit" required/>
                                </div>
                                <div class="error text-center text-danger blink-text pb-3"></div>
                                <div class="form-outline mb-4 files back_id">
                                    <label for="#city">{{ translate('stepper.city_town') }}</label>
                                    <input type="text" placeholder="{{ translate('stepper.enter_city') }}" class="form-control step_input"
                                        name="city" id="city" required />
                                </div>
                                <div class="error text-center text-danger blink-text pb-3"></div>
                                <div class="form-outline mb-4 files back_id">
                                    <label for="#state">{{ translate('stepper.state_territory') }}</label>
                                    <input type="text" placeholder="{{ translate('stepper.enter_state') }}" class="form-control step_input"
                                        name="state" id="state" required />
                                </div>
                                <div class="error text-center text-danger blink-text pb-3"></div>
                                <div class="form-outline mb-4 files back_id">
                                    <label for="#zip">{{ translate('stepper.zip_code') }}</label>
                                    <input type="number" placeholder="{{ translate('stepper.enter_zip') }}" class="form-control step_input"
                                        name="zip" id="zip" required />
                                </div>
                            </div>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset> 
                        <div class="inner-box1">
                            <h2 class="semi_bold text-center">{{ translate('stepper.start_point') }}</h2>
                            <p class="fs-18 text-center">{{ translate('stepper.address_note') }}</p>
                            <div class="form-outline mb-4 files back_id map">
                                {{-- <input type="text" class="form-control " placeholder="Enter Location" name="location"
                                    id="location" required /> --}}
                                    <input id="autocomplete" type="text" placeholder="{{ translate('stepper.enter_location') }}">
                                    <div id="map"></div>
                                    <button type="submit" class="map-btn">{{ translate('stepper.drag_map') }}</button>
                            </div>
                        </div>
                        <div class="d-flex pb-3" id="back_preview" style="column-gap:30px"></div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                            <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />

                    </fieldset>
                    <fieldset class="capacity"> 
                        <h2 class="semi_bold text-center">{{ translate('stepper.share_basics') }}</h2>
                        <div class="basic_parent pt-2">
                            <div class="form-outline mb-4 files back_id d-flex divider">
                                <label for="guest">{{ translate('stepper.booking_capacity') }}</label>
                                <div class="click d-flex">
                                    <button class="btn btn-link px-1 fa fa-minus " type="button"
                                    onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                    </button>
                                    <input type="number" value="1" class="form-control file" name="guest" id="guest"
                                    required />
                                    <button class="btn btn-link px-1 fa fa-plus " type="button"
                                        onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                    </button>
                                </div>
                            </div>
                            <div class="extra-fields">
                                <div class="mb-3 d-flex divider">
                                    <label for="" class="form-label">{{ translate('stepper.start_time') }}</label>
                                    <div class="box d-flex">
                                        <input type="date" class="form-control" id="" placeholder="">
                                        <input type="time" class="form-control" id="" placeholder="">
                                    </div>
                                </div>
                                <div class="mb-3 d-flex divider">
                                    <label for="" class="form-label">{{ translate('stepper.end_time') }}</label>
                                    <div class="box d-flex">
                                        <input type="date" class="form-control" id="" placeholder="">
                                        <input type="time" class="form-control" id="" placeholder="">
                                    </div>
                                </div>
                                <div class="mb-3 d-flex divider">
                                    <label for="" class="form-label">{{ translate('stepper.define_child_age') }}</label>
                                    <div class="box d-flex">
                                        <input type="number" class="form-control" id="" placeholder="00">
                                        <input type="number" class="form-control" id="" placeholder="--">
                                    </div>
                                </div>
                                <div class="mb-3 d-flex divider">
                                    <label for="" class="form-label">{{ translate('stepper.define_adult_age') }}</label>
                                    <div class="box d-flex">
                                        <input type="number" class="form-control" id="" placeholder="00">
                                        <input type="number" class="form-control" id="" placeholder="--">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="d-flex pb-3" id="back_preview" style="column-gap:30px"></div>
                        <div class="error text-center text-danger blink-text pb-3"></div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="Next" /> -->
                            <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                            <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset> 
                        <div class="row">
                            <div class="col-md-6 fieldset-4" style="text-align: left;">
                                <h4 class="">{{ translate('stepper.step_2') }}</h4>
                                <h2 class="semi_bold">{{ translate('stepper.make_stand_out') }}</h2>
                                <p class="fs-18">
                                    {{ translate('stepper.enhance_experience') }}
                                </p>
                            </div>
                            <div class="col-md-6 fieldset-4">
                                <div class="pot_img">
                                    <img src="{{ asset('website') }}/images/experience.png" alt="Property Image"
                                        class="img-fluid">
                                </div>
                            </div>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                            <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset class="car_feature">
                        <h2 class="semi_bold text-center">{{ translate('stepper.boat_details') }}</h2>
                        <div class="inner-box1 field_set">
                            <div class="inline-form w-100">
                                <div class="mb-3">
                                    <label for="">{{ translate('stepper.specs_features') }}</label>
                                    <select name="" class="multi_select">
                                        <option value="" disabled selected>{{ translate('stepper.multiple_selection_field') }}</option>
                                        <option value="connectivity">{{ translate('stepper.connectivity') }}</option>
                                        <option value="entertainment">{{ translate('stepper.entertainment') }}</option>
                                        <option value="kitchen">{{ translate('stepper.kitchen') }}</option>
                                        <option value="laundry">{{ translate('stepper.laundry') }}</option>
                                        <option value="parking">{{ translate('stepper.parking') }}</option>
                                        <option value="environment">{{ translate('stepper.environment') }}</option>
                                        <option value="work_space">{{ translate('stepper.workspace') }}</option>
                                    </select>
                                </div>
                            </div>
                            {{-- key feature --}}
                            <div class="col-md-12 key_feature">
                                <div class="form_field_padding inp_text_gap feature">
                                    <div class="parent-key">
                                        <label for="">{{ translate('stepper.key_features') }}</label>
                                        <span class="add-muliple-feature-car">
                                            <img src="{{ asset('website') }}/images/plus.png"
                                                alt="" height="20px">
                                        </span>
                                    </div>
                                    <input type="text" name=""
                                        id="feature_title" class="form-control" placeholder="{{ translate('stepper.title') }}"
                                        value="">
                                    <textarea class="form-control" id="feature_des" name=""
                                        id="" rows="5" column="25" placeholder="{{ translate('stepper.description') }}"></textarea>
                                </div>
                            </div>
                            {{-- Add button-1 --}}
                            <div class="options_wrapper">
                                <div class="txt_field options_txt_field">
                                    <label>{{ translate('stepper.accessibility') }}</label>
                                    <div class="add_btn_wrapper">
                                        <input type="text" class="form-control" id="" name="" placeholder="{{ translate('stepper.type_field') }}"/>
                                        <a href="javascript:void(0)" class="btn add_options_btn">{{ translate('stepper.add') }}</a>
                                    </div>
                                </div>
                            </div>
                            {{-- Add button-2 --}}
                            <div class="options_wrapper_field">
                                <div class="txt_field options_txt_field_set">
                                    <label>{{ translate('stepper.itinerary') }}</label>
                                    <div class="add_btn_wrapper_query">
                                        <input type="text" class="form-control" id="" name="" placeholder="{{ translate('stepper.type_field') }}"/>
                                        <a href="javascript:void(0)" class="btn add_options_btn_query">{{ translate('stepper.add') }}</a>
                                    </div>
                                </div>
                            </div>
                            {{-- Add button-3 --}}
                            <div class="new-btn">
                                <div class="options_wrapper2">
                                    <div class="txt_field options_txt_field2">
                                        <label>{{ translate('stepper.itinerary') }}</label>
                                        <div class="add_btn_wrapper2">
                                            <input type="text" class="form-control" id="" name="" placeholder="{{ translate('stepper.type_field') }}"/>
                                            <a href="javascript:void(0)" class="btn add_options_btn2">{{ translate('stepper.add') }}</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="options_wrapper3">
                                    <div class="txt_field options_txt_field3">
                                        <label>{{ translate('stepper.itinerary') }}</label>
                                        <div class="add_btn_wrapper3">
                                            <input type="text" class="form-control" id="" name="" placeholder="{{ translate('stepper.type_field') }}"/>
                                            <a href="javascript:void(0)" class="btn add_options_btn3">{{ translate('stepper.add') }}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset> 
                        <h2 class="semi_bold text-center">{{ translate('stepper.tour_rules') }}</h2>
                        <div class="padding">
                            <h4 class="text-center">{{ translate('stepper.pets_allowed') }}</h4>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="custom-yes">
                            <input type="radio" class="radio_btn" id="yes" name="category_id"
                                value="2" checked>
                            <label for="yes">
                                    {{ translate('stepper.yes') }}
                            </label>
                            <input type="radio" class="radio_btn" id="no" name="category_id"
                                value="2">
                            <label for="no">
                                    {{ translate('stepper.no') }}
                            </label>
                        </div>
                        <div class="rules">
                            <a href="#" class="rules" type="button" data-bs-toggle="modal" data-bs-target="#exampleModal2">{{ translate('stepper.house_rules') }}</a>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset> 
                        <div class="main">
                            <h2 class="bold mx-auto">{{ translate('stepper.add_photos') }}</h2>
                            <p class="fs-18 text-center">{{ translate('stepper.high_quality_photos') }}</p>
                            <div class="drop_photos">
                                <img src="{{ asset('website/images/text.png') }}" alt="">
                                <h6 class="text-center">{{ translate('stepper.drop_photos') }}</h6>
                                <p class="text-center">{{ translate('stepper.choose_images') }}</p>
                                <div class="mb-3 upload-file">
                                    <label for="formFile" class="form-label">{{ translate('stepper.upload_device') }}</label>
                                    <input class="form-control" type="file" id="formFile">
                                </div>
                            </div>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset class="exp1">
                        <h2 class="bold mx-auto">{{ translate('stepper.title_prompt') }}</h2>
                        <p class="fs-18 text-center">{{ translate('stepper.title_note') }}</p>
                        <div class="mb-3 title">
                            <input type="text" value="" placeholder="{{ translate('stepper.title_place') }}">
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset class="exp2">
                        <h2 class="bold mx-auto">{{ translate('stepper.describe_experience') }}</h2>
                        <p class="fs-18 text-center">{{ translate('stepper.unique_space') }}</p>
                        <div class="mb-3 title">
                            <textarea name="" id="" cols="30" rows="">{{ translate('stepper.get_creative') }}</textarea>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset>
                        <h2 class="bold mx-auto">{{ translate('stepper.advance_listing') }} </h2>
                        <div class="mb-3 advance">
                            <label for="">{{ translate('stepper.minimum_stay_length') }}</label>
                            <select class="form-select" aria-label="Default select example">
                            <option selected>{{ translate('stepper.minimum_nights') }}</option>
                            <option value="1">{{ translate('stepper.one') }}</option>
                            <option value="2">{{ translate('stepper.two') }}</option>
                            <option value="3">{{ translate('stepper.three') }}</option>
                            </select>
                        </div>
                        <div class="mb-3 advance">
                            <label for="">{{ translate('stepper.listing_availability') }}</label>
                            <select class="form-select" aria-label="Default select example">
                            <option selected>{{ translate('stepper.advance_booking') }}</option>
                            <option value="1">{{ translate('stepper.one') }}</option>
                            <option value="2">{{ translate('stepper.two') }}</option>
                            <option value="3">{{ translate('stepper.three') }}</option>
                            </select>
                        </div>
                        <div class="mb-3 advance">
                            <label for="">{{ translate('stepper.advance_booking') }}</label>
                            <select class="form-select" aria-label="Default select example">
                            <option selected>{{ translate('stepper.selection_field') }}</option>
                            <option value="1">{{ translate('stepper.one') }}</option>
                            <option value="2">{{ translate('stepper.two') }}</option>
                            <option value="3">{{ translate('stepper.three') }}</option>
                            </select>
                        </div>
                        <div class="mb-3 advance">
                            <label for="">{{ translate('stepper.cancelation_policy') }}</label>
                            <select class="form-select" aria-label="Default select example">
                            <option selected>{{ translate('stepper.selection_field') }}</option>
                            <option value="1">{{ translate('stepper.one') }}</option>
                            <option value="2">{{ translate('stepper.two') }}</option>
                            <option value="3">{{ translate('stepper.three') }}</option>
                            </select>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset class="exp3">  
                        <div class="row">
                            <div class="col-md-6" style="text-align:left;">
                                <h4 class="">{{ translate('stepper.step_3') }}</h4>
                                <h2 class="semi_bold">{{ translate('stepper.finish_publish') }}</h2>
                                <p class="fs-18">
                                    Finally, you’ll choose booking settings, set up pricing, and publish your 
                                    listing</p>
                            </div>
                            <div class="col-md-6">
                                <div class="pot_img">
                                    <img src="{{ asset('website') }}/images/experience.png" alt="Property Image"
                                        class="img-fluid">
                                </div>
                            </div>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                            <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset class="exp4"> 
                        <h2 class="bold mx-auto">{{ translate('stepper.set_price_child') }}</h2>
                        <p class="fs-18 text-center">{{ translate('stepper.change_anytime') }}</p>
                        <div class="price">
                            <span>{{ translate('stepper.price_example') }}</span>
                            <img src="{{ asset('website/images/price.png') }}" alt="">
                        </div>
                        <p class="fs-18 text-center bold" style="color:black;">{{ translate('stepper.price_after_commission') }}</p>
                            <a href="#" class="price_link">{{ translate('stepper.learn_pricing') }}</a>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset>
                        <h2 class="bold mx-auto">{{ translate('stepper.set_price_adult') }}</h2>
                        <p class="fs-18 text-center">{{ translate('stepper.change_anytime') }}</p>
                        <div class="price">
                            <span>$137.99</span>
                            <img src="{{ asset('website/images/price.png') }}" alt="">
                        </div>
                        <p class="fs-18 text-center bold" style="color:black;">{{ translate('stepper.price_after_commission') }}</p>
                            <a href="#" class="price_link">{{ translate('stepper.learn_pricing') }}</a>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset class="discount">
                        <div class="inner-discount">
                            <h2 class="bold mx-auto text-left">{{ translate('stepper.add_discounts') }}</h2>
                            <p class="fs-18 text-left">{{ translate('stepper.listing_tip') }}</p>
                            <div class="form-check">
                                <label class="form-check-label" for="flexCheckDefault">
                                    <div class="discount">
                                        <div class="box1">
                                            <input type="number" name="" id="" placeholder="00%">
                                        </div>
                                    <div class="box2">
                                            <h6>{{ translate('stepper.new_listing_discount') }}</h6>
                                            <p>{{ translate('stepper.upto_3_bookings') }}</p>
                                    </div>
                                    </div>
                                </label>
                                <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                            </div>
                            <div class="form-check">
                                <label class="form-check-label" for="flexCheckDefault">
                                    <div class="discount">
                                        <div class="box1">
                                            <input type="number" name="" id="" placeholder="20%">
                                        </div>
                                    <div class="box2">
                                            <h6>{{ translate('stepper.weekly_discount') }}</h6>
                                            <p>{{ translate('stepper.weekly_note') }}</p>
                                    </div>
                                    </div>
                                </label>
                                <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                            </div>
                            <div class="form-check">
                                <label class="form-check-label" for="flexCheckDefault">
                                    <div class="discount">
                                        <div class="box1">
                                            <input type="number" name="" id="" placeholder="20%">
                                        </div>
                                    <div class="box2">
                                            <h6>{{ translate('stepper.monthly_discount') }}</h6>
                                            <p>{{ translate('stepper.monthly_note') }}</p>
                                    </div>
                                    </div>
                                </label>
                                <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                            </div> 
                            <div class="seasonal_price">
                                <h6>{{ translate('stepper.seasonal_pricing') }}</h6>
                                <span>{{ translate('stepper.seasonal_note') }}</span>
                                <br><br>
                                <div class="box1">
                                    <div class="mb-3">
                                        <label for="" class="form-label">{{ translate('stepper.season_start') }}</label>
                                        <input type="date" class="" id="">
                                    </div>
                                    <div class="mb-3">
                                        <label for="" class="form-label">{{ translate('stepper.season_end') }}</label>
                                        <input type="date" class="" id="">
                                    </div>                                    
                                </div>
                                <div class="mb-3 price_change">
                                        <label for="" class="form-label">{{ translate('stepper.price_change') }}</label>
                                        <input type="number" class="" id="" placeholder="00%">
                                </div>
                            </div>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.next') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                    <fieldset> 
                        <div class="inner-box1">
                            <h2 class="bold mx-auto">{{ translate('stepper.review_experience') }}</h2>
                            <p class="fs-18 text-center">{{ translate('stepper.review_note') }}</p>
                            <div class="row place-rw">
                                <div class="col-md-5">
                                    <div class="review-card">
                                        <img src="{{ asset('website/images/dummy.png') }}" alt="">
                                        <div class="place">
                                            <h6>{{ translate('stepper.best_experience') }}</h6>
                                            <span>{{ translate('stepper.new') }} <i class="bi bi-star-fill"></i></span>
                                        </div>
                                        <p><del>{{ translate('stepper.price_example') }}</del>{{ translate('stepper.price_per_night') }}/<span>{{ translate('stepper.night') }}</span></p>
                                    </div>
                                </div>
                                <div class="col-md-7">
                                    <div class="place_box">
                                        <h2>{{ translate('stepper.whats_next') }}</h2>
                                        <div class="box1">
                                            <div class="p1">
                                                <img src="{{ asset('website/images/p1.png') }}" alt="">
                                            </div>
                                            <div class="p2">
                                                <h6>{{ translate('stepper.confirm_and_publish') }}</h6>
                                                <p>{{ translate('stepper.verification_note') }}</p>
                                            </div>
                                        </div>
                                        <div class="box1">
                                            <div class="p1">
                                                <img src="{{ asset('website/images/calendar-2.png') }}" alt="">
                                            </div>
                                            <div class="p2">
                                                <h6>{{ translate('stepper.confirm_and_publish') }}</h6>
                                                <p>{{ translate('stepper.verification_note') }}</p>
                                            </div>
                                        </div>
                                        <div class="box1">
                                            <div class="p1">
                                                <img src="{{ asset('website/images/edit-icon.png') }}" alt="">
                                            </div>
                                            <div class="p2">
                                                <h6>{{ translate('stepper.confirm_and_publish') }}</h6>
                                                <p>{{ translate('stepper.verification_note') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <input type="button" name="next" class="next action-button btn button1  mb-4"
                            value="{{ translate('stepper.finish') }}" />
                        <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
                    </fieldset>
                </div>
            </div>
        </div>
    </form>
</section>   

</body>
</html>

  <!-- House Rules Modal -->
  <section class="house-rule-modal">
    <div class="modal fade" id="exampleModal2" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-body">
                    <form action="">
                        <div class="col-md-12 rule">
                            <div class="form_field_padding inp_text_gap feature">
                                <div class="parent-key">
                                    <label for="">Rule 1</label>
                                    <span class="add-muliple-feature">
                                        <img src="{{ asset('website') }}/images/plus.png"alt="" height="20px">
                                    </span>
                                </div>
                                <input type="text" name="key_features" id="feature_title" class="form-control" placeholder="{{ translate('stepper.title') }}" value="">
                                <div class="radio-box d-flex">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1">
                                        <label class="form-check-label" for="radiovalue">{{ translate('stepper.allowed') }}</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="flexRadioDefault" id="radiovalue" checked>
                                        <label class="form-check-label" for="radiovalue">{{ translate('stepper.not_allowed') }}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="btn-box">
                                <button type="submit" class="yellow">{{ translate('stepper.update') }}</button>
                                <button type="submit" class="black">{{ translate('stepper.cancel') }}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

    {{-- <section class="det_form cmsForms">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <h1 class="bold">Content Management System</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <h2 class="semi-bold">Contact Us</h2>
                    <form id="contactForm">
                        <div class="row">
                            <div class="col-md-4 d-flex">
                                <div class="form_field_padding">
                                    <label for="">Phone</label>
                                    <input type="phone" name="phone" id="phone" class="form-control"
                                        placeholder="0000000000000" value="">
                                </div>
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="col-md-4 d-flex">
                                <div class="form_field_padding">
                                    <label for="email">Email</label>
                                    <input type="email" name="email" id="email" class="form-control"
                                        placeholder="<EMAIL>" value="">
                                </div>
                                <i class="fas fa-edit"></i>

                            </div>
                            <div class="col-md-4 d-flex">
                                <div class="form_field_padding">
                                    <label for="address">Address</label>
                                    <input type="text" name="address" id="address" class="form-control"
                                        placeholder="Address Here" value="">
                                </div>
                                <i class="fas fa-edit"></i>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form_field_padding">
                                    <div class="list_form_btn">
                                        <input class="btn cancel_btn" type="submit" value="Cancel">
                                        <input class="btn create_btn" type="submit" value="Save & Update">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section> --}}
{{-- @endsection
@push('js') --}}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>

{{-- Stepper Form query --}}
    <script>
        $(document).ready(function(){

        var current_fs, next_fs, previous_fs; //fieldsets
        var opacity;
        var current = 1;
        var steps = $("fieldset").length;

        setProgressBar(current);

        $(".next").click(function(){

        current_fs = $(this).parent();
        next_fs = $(this).parent().next();

        //Add Class Active
        $("#progressbar li").eq($("fieldset").index(next_fs)).addClass("active");

        //show the next fieldset
        next_fs.show();
        //hide the current fieldset with style
        current_fs.animate({opacity: 0}, {
        step: function(now) {
        // for making fielset appear animation
        opacity = 1 - now;

        current_fs.css({
        'display': 'none',
        'position': 'relative'
        });
        next_fs.css({'opacity': opacity});
        },
        duration: 500
        });
        setProgressBar(++current);
        });

        $(".previous").click(function(){

        current_fs = $(this).parent();
        previous_fs = $(this).parent().prev();

        //Remove class active
        $("#progressbar li").eq($("fieldset").index(current_fs)).removeClass("active");

        //show the previous fieldset
        previous_fs.show();

        //hide the current fieldset with style
        current_fs.animate({opacity: 0}, {
        step: function(now) {
        // for making fielset appear animation
        opacity = 1 - now;

        current_fs.css({
        'display': 'none',
        'position': 'relative'
        });
        previous_fs.css({'opacity': opacity});
        },
        duration: 500
        });
        setProgressBar(--current);
        });

        function setProgressBar(curStep){
        var percent = parseFloat(100 / steps) * curStep;
        percent = percent.toFixed();
        $(".progress-bar")
        .css("width",percent+"%")
        }

        $(".submit").click(function(){
        return false;
        })

        });



    </script>

{{-- Map query  --}}
    <script>
        function initMap() {
            var map = new google.maps.Map(document.getElementById('map'), {
                center: {lat: -34.397, lng: 150.644},
                zoom: 8
            });

            var input = document.getElementById('autocomplete');
            var autocomplete = new google.maps.places.Autocomplete(input);

            autocomplete.bindTo('bounds', map);

            var infowindow = new google.maps.InfoWindow();
            var marker = new google.maps.Marker({
                map: map,
                anchorPoint: new google.maps.Point(0, -29)
            });

            autocomplete.addListener('place_changed', function() {
                infowindow.close();
                marker.setVisible(false);
                var place = autocomplete.getPlace();

                if (!place.geometry) {
                    window.alert("No details available for input: '" + place.name + "'");
                    return;
                }

                if (place.geometry.viewport) {
                    map.fitBounds(place.geometry.viewport);
                } else {
                    map.setCenter(place.geometry.location);
                    map.setZoom(17);  // Why 17? Because it looks good.
                }

                marker.setPosition(place.geometry.location);
                marker.setVisible(true);

                var content = '<div class="map-caption"><b>' + place.name + '</b><br>' +
                    place.formatted_address + '</div>';

                infowindow.setContent(content);
                infowindow.open(map, marker);
            });
        }
    </script>

    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBb0IoM4fw78ZZnRGuVDMqlZEmx6F7ivyE&libraries=places&callback=initMap" async defer></script>

{{-- Add Feature query --}}
    <script>
        $(document).ready(function() {
            var counter = {{ isset($listing->key_features) ? count($listing->key_features) - 1 : 0 }};
            $(".add-muliple-feature-car").on("click", function() {

                var value = counter + 1;
                var latestRow = $('.key_feature .form_field_padding').last(); // Get the latest added row
                var title = latestRow.find('input[id^="feature_title"]').val();
                var description = latestRow.find('textarea[id^="feature_des"]').val();

                if (title && description) {
                    counter++;
                    $(".key_feature").append(
                        `<div class="form_field_padding inp_text_gap"> 
                        <div class="parent-key ">
                            <label for="">${@json(translate('stepper.key_features'))} #${value}</label>
                            <span class="remove_jq" > 
                            <i class="fa-regular fa-square-minus" style="color: #1c274c;"></i></span>
                        </div>
                        <input type="text" name="key_features[${parseInt(counter)}][title]" id="feature_title${parseInt(counter)}" class="form-control" id="feature_title" placeholder="${@json(translate('stepper.title'))}"><div class="keyTitle error"><p>Please enter title</p></div>
                        <textarea class="form-control" name="key_features[${parseInt(counter)}][description]" id="feature_des${parseInt(counter)}" rows="5" column="25" placeholder="${@json(translate('stepper.description'))}"></textarea> <div class="keyDes error"><p>Please enter description</p></div>
                    </div>`
                    );
                    $(latestRow).find('.error.keyTitle').css('display', 'none');
                    $(latestRow).find('.error.keyDes').css('display', 'none');
                }else if (title === "") {
                    $(latestRow).find('.error.keyTitle').css('display', 'block');
                }else if (description === "") {
                    $(latestRow).find('.error.keyDes').css('display', 'block');
                } else {
                    $(latestRow).find('.error.keyTitle').css('display', 'block');
                    $(latestRow).find('.error.keyDes').css('display', 'block');
                }
            });

            $(document).on('click', '.feature .remove_jq', function() {
                console.log('test');
                $(this).closest('.key_feature .form_field_padding').remove();
                counter--;
                value = counter;
                var $remainingItems = $('.key_feature  .form_field_padding');
                for (var i = $remainingItems.length - 1; i >= 0; i--) {
                    var newValue = value - i;
                    $($remainingItems[i]).find('label').text(@json(translate('stepper.key_features')) + ' #' + newValue);
                }
            });
        });
        $(document).ready(function() {
            var counter = 0;
            $(".add-another").on("click", function() {
                var last_row = $('.clone_row .form_field_padding').last();
                var extra = last_row.find('input[name^="extras"]').val();
                if (extra != '') {
                    $(".clone_row").append(
                        ' <div class="form_field_padding "> <div class="plus_jq"> <label for="">Extras</label> <span class="remove_jq"><i class="fa-regular fa-square-minus" style="color: #1c274c;"></i></span> </div> <input type="text" name="extras[]' +
                        counter + '" class="form-control" placeholder="Add"><div class="extra error"><p>Please enter extra</p></div> </div>');
                    counter += 1;
                    $(last_row).find('.error.extra').css('display', 'none');
                }else {
                    $(last_row).find('.error.extra').css('display', 'block');
                }
            });
            $(document).on('click', '.remove_jq', function() {
                console.log('test');
                $(this).closest('.form_field_padding').remove();
                counter -= 1;
            })
        });
        $(document).ready(function() {
            var counter = 0;
            $(".add-another-two").on("click", function() {
                var last_row = $('#rowContainertwo .clone_row2 .form_field_padding').last();
                var services = last_row.find('input[name^="services"]').val();
                if (services != '') {
                    $(".clone_row2").append(
                        ' <div class="form_field_padding"> <div class="plus_jq"> <label for="">Services</label> <span class="remove_jq" ><i class="fa-regular fa-square-minus" style="color: #1c274c;"></i></span> </div> <input type="text" name="services[]' +
                        counter + '" class="form-control" placeholder="Add"><div class="service error"><p>Please enter service</p></div> </div> ');
                    counter += 1;
                    $(last_row).find('.error.service').css('display', 'none');
                }else {
                    $(last_row).find('.error.service').css('display', 'block');
                }
            });
            $(document).on('click', '.remove_jq', function() {
                console.log('test');
                $(this).closest('.form_field_padding').remove();
                counter -= 1;
            })
        });
    </script>

{{-- add field 1 query  --}}
    <script>
        $(document).ready(function () {
            var tags_count = 1;
            var optionsArray = [];
            $(document).on('click', '.add_btn_wrapper .add_options_btn', function () {
                var tag_text = $('.options_txt_field .add_btn_wrapper input[type="text"]').val().trim();

                // Check if the option is not empty and unique
                if (tag_text !== "" && optionsArray.indexOf(tag_text) === -1) {
                    optionsArray.push(tag_text);
                    $('.options_wrapper').append('<div class="single_option"> <p>' + tag_text + '</p> <input type="checkbox" name="test_link[]" id="option_' + tags_count + '" /> <label class="" for="option_' + tags_count + '"><i class="far fa-times-circle"></i></label> </div>');
                    tags_count++;
                    $('.options_txt_field .add_btn_wrapper input[type="text"]').val('');
                    clearErrorMessage();
                } else {
                    // Show an error message for empty or non-unique options
                    if (tag_text === "") {
                        showErrorMessage("Please enter a non-empty option");
                    } else {
                        showErrorMessage("Please enter a unique option");
                    }
                }
            });

            $(document).on('click', '.options_wrapper .single_option label', function () {
                var optionIndex = $(this).closest('.single_option').index();
                optionsArray.splice(optionIndex, 1);
                $(this).closest('.single_option').remove();
                tags_count--;
                clearErrorMessage();
            });

            $(document).on('click', '#submitBtn', function () {
                if (optionsArray.length > 0) {
                    $('#optionName').val(optionsArray.join(',')); // Convert array to comma-separated string
                    // alert(JSON.stringify(optionsArray));
                    clearErrorMessage();
                } else {
                    // Show an error message if no options are added
                    showErrorMessage("Please add at least one option");
                }
            });

            function showErrorMessage(message) {
                $('#error_message').text(message);
            }

            function clearErrorMessage() {
                $('#error_message').text('');
            }
        });
    </script>

{{-- add field 2 query --}}
    <script>
        $(document).ready(function () {
            var tags_count = 1;
            var optionsArray = [];
            $(document).on('click', '.add_btn_wrapper_query .add_options_btn_query', function () {
                var tag_text = $('.options_txt_field_set .add_btn_wrapper_query input[type="text"]').val().trim();

                // Check if the option is not empty and unique
                if (tag_text !== "" && optionsArray.indexOf(tag_text) === -1) {
                    optionsArray.push(tag_text);
                    $('.options_wrapper_field').append('<div class="single_option"> <p>' + tag_text + '</p> <input type="checkbox" name="test_link[]" id="option_' + tags_count + '" /> <label class="" for="option_' + tags_count + '"><i class="far fa-times-circle"></i></label> </div>');
                    tags_count++;
                    $('.options_txt_field_set .add_btn_wrapper_query input[type="text"]').val('');
                    clearErrorMessage();
                } else {
                    // Show an error message for empty or non-unique options
                    if (tag_text === "") {
                        showErrorMessage("Please enter a non-empty option");
                    } else {
                        showErrorMessage("Please enter a unique option");
                    }
                }
            });

            $(document).on('click', '.options_wrapper_field .single_option label', function () {
                var optionIndex = $(this).closest('.single_option').index();
                optionsArray.splice(optionIndex, 1);
                $(this).closest('.single_option').remove();
                tags_count--;
                clearErrorMessage();
            });

            $(document).on('click', '#submitBtn', function () {
                if (optionsArray.length > 0) {
                    $('#optionName').val(optionsArray.join(',')); // Convert array to comma-separated string
                    // alert(JSON.stringify(optionsArray));
                    clearErrorMessage();
                } else {
                    // Show an error message if no options are added
                    showErrorMessage("Please add at least one option");
                }
            });

            function showErrorMessage(message) {
                $('#error_message').text(message);
            }

            function clearErrorMessage() {
                $('#error_message').text('');
            }
        });
    </script>

{{-- add field 3 query --}}
<script>
    $(document).ready(function () {
        var tags_count = 1;
        var optionsArray = [];
        $(document).on('click', '.add_btn_wrapper2 .add_options_btn2', function () {
            var tag_text = $('.options_txt_field2 .add_btn_wrapper2 input[type="text"]').val().trim();

            // Check if the option is not empty and unique
            if (tag_text !== "" && optionsArray.indexOf(tag_text) === -1) {
                optionsArray.push(tag_text);
                $('.options_wrapper2').append('<div class="single_option"> <p>' + tag_text + '</p> <input type="checkbox" name="test_link[]" id="option_' + tags_count + '" /> <label class="" for="option_' + tags_count + '"><i class="far fa-times-circle"></i></label> </div>');
                tags_count++;
                $('.options_txt_field2 .add_btn_wrapper2 input[type="text"]').val('');
                clearErrorMessage();
            } else {
                // Show an error message for empty or non-unique options
                if (tag_text === "") {
                    showErrorMessage("Please enter a non-empty option");
                } else {
                    showErrorMessage("Please enter a unique option");
                }
            }
        });

        $(document).on('click', '.options_wrapper2 .single_option label', function () {
            var optionIndex = $(this).closest('.single_option').index();
            optionsArray.splice(optionIndex, 1);
            $(this).closest('.single_option').remove();
            tags_count--;
            clearErrorMessage();
        });

        $(document).on('click', '#submitBtn', function () {
            if (optionsArray.length > 0) {
                $('#optionName').val(optionsArray.join(',')); // Convert array to comma-separated string
                // alert(JSON.stringify(optionsArray));
                clearErrorMessage();
            } else {
                // Show an error message if no options are added
                showErrorMessage("Please add at least one option");
            }
        });

        function showErrorMessage(message) {
            $('#error_message').text(message);
        }

        function clearErrorMessage() {
            $('#error_message').text('');
        }
    });
</script>

{{-- add field 4 query --}}
<script>
    $(document).ready(function () {
        var tags_count = 1;
        var optionsArray = [];
        $(document).on('click', '.add_btn_wrapper3 .add_options_btn3', function () {
            var tag_text = $('.options_txt_field3 .add_btn_wrapper3 input[type="text"]').val().trim();

            // Check if the option is not empty and unique
            if (tag_text !== "" && optionsArray.indexOf(tag_text) === -1) {
                optionsArray.push(tag_text);
                $('.options_wrapper3').append('<div class="single_option"> <p>' + tag_text + '</p> <input type="checkbox" name="test_link[]" id="option_' + tags_count + '" /> <label class="" for="option_' + tags_count + '"><i class="far fa-times-circle"></i></label> </div>');
                tags_count++;
                $('.options_txt_field3 .add_btn_wrapper3 input[type="text"]').val('');
                clearErrorMessage();
            } else {
                // Show an error message for empty or non-unique options
                if (tag_text === "") {
                    showErrorMessage("Please enter a non-empty option");
                } else {
                    showErrorMessage("Please enter a unique option");
                }
            }
        });

        $(document).on('click', '.options_wrapper3 .single_option label', function () {
            var optionIndex = $(this).closest('.single_option').index();
            optionsArray.splice(optionIndex, 1);
            $(this).closest('.single_option').remove();
            tags_count--;
            clearErrorMessage();
        });

        $(document).on('click', '#submitBtn', function () {
            if (optionsArray.length > 0) {
                $('#optionName').val(optionsArray.join(',')); // Convert array to comma-separated string
                // alert(JSON.stringify(optionsArray));
                clearErrorMessage();
            } else {
                // Show an error message if no options are added
                showErrorMessage("Please add at least one option");
            }
        });

        function showErrorMessage(message) {
            $('#error_message').text(message);
        }

        function clearErrorMessage() {
            $('#error_message').text('');
        }
    });
</script>

{{-- Modal add features query --}}
    <script>
        $(document).ready(function() {
        var counter = {{ isset($listing->key_features) ? count($listing->key_features) - 1 : 0 }};
        $(".add-muliple-feature").on("click", function() {

            var value = counter + 1;
            var latestRow = $('.rule .form_field_padding').last(); // Get the latest added row
            var title = latestRow.find('input[id^="feature_title"]').val();
            var description = latestRow.find('input[id^="radiovalue"]').val();

            if (title && description) {
                counter++;
                $(".rule").append(
                    `<div class="form_field_padding inp_text_gap"> 
                    <div class="parent-key">
                        <label for="">Rule ${value}</label>
                        <span class="remove_jq" > 
                        <i class="fa-regular fa-square-minus" style="color: #1c274c;"></i></span>
                    </div>
                    <input type="text" name="key_features[${parseInt(counter)}][title]" id="feature_title${parseInt(counter)}" class="form-control" id="feature_title" placeholder="Title"><div class="keyTitle error"><p>Please enter title</p></div>
                    <div class="radio-box d-flex">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1">
                            <label class="form-check-label" for="radiovalue">allowed</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="flexRadioDefault" id="radiovalue" checked>
                            <label class="form-check-label" for="radiovalue">not allowed</label>
                        </div>
                    </div>
                </div>`
                );
                $(latestRow).find('.error.keyTitle').css('display', 'none');
                $(latestRow).find('.error.keyDes').css('display', 'none');
            }else if (title === "") {
                $(latestRow).find('.error.keyTitle').css('display', 'block');
            }else if (description === "") {
                $(latestRow).find('.error.keyDes').css('display', 'block');
            } else {
                $(latestRow).find('.error.keyTitle').css('display', 'block');
                $(latestRow).find('.error.keyDes').css('display', 'block');
            }
        });

        $(document).on('click', '.feature .remove_jq', function() {
            console.log('test');
            $(this).closest('.key_feature .form_field_padding').remove();
            counter--;
            value = counter;
            var $remainingItems = $('.key_feature  .form_field_padding');
            for (var i = $remainingItems.length - 1; i >= 0; i--) {
                var newValue = value - i;
                $($remainingItems[i]).find('label').text('Key Feature #' + newValue);
            }
        });
        });
        $(document).ready(function() {
        var counter = 0;
        $(".add-another").on("click", function() {
            var last_row = $('.clone_row .form_field_padding').last();
            var extra = last_row.find('input[name^="extras"]').val();
            if (extra != '') {
                $(".clone_row").append(
                    ' <div class="form_field_padding "> <div class="plus_jq"> <label for="">Extras</label> <span class="remove_jq"><i class="fa-regular fa-square-minus" style="color: #1c274c;"></i></span> </div> <input type="text" name="extras[]' +
                    counter + '" class="form-control" placeholder="Add"><div class="extra error"><p>Please enter extra</p></div> </div>');
                counter += 1;
                $(last_row).find('.error.extra').css('display', 'none');
            }else {
                $(last_row).find('.error.extra').css('display', 'block');
            }
        });
        $(document).on('click', '.remove_jq', function() {
            console.log('test');
            $(this).closest('.form_field_padding').remove();
            counter -= 1;
        })
        });
            $(document).ready(function() {
            var counter = 0;
            $(".add-another-two").on("click", function() {
                var last_row = $('#rowContainertwo .clone_row2 .form_field_padding').last();
                var services = last_row.find('input[name^="services"]').val();
                if (services != '') {
                    $(".clone_row2").append(
                        ' <div class="form_field_padding"> <div class="plus_jq"> <label for="">Services</label> <span class="remove_jq" ><i class="fa-regular fa-square-minus" style="color: #1c274c;"></i></span> </div> <input type="text" name="services[]' +
                        counter + '" class="form-control" placeholder="Add"><div class="service error"><p>Please enter service</p></div> </div> ');
                    counter += 1;
                    $(last_row).find('.error.service').css('display', 'none');
                }else {
                    $(last_row).find('.error.service').css('display', 'block');
                }
            });
            $(document).on('click', '.remove_jq', function() {
                console.log('test');
                $(this).closest('.form_field_padding').remove();
                counter -= 1;
            })
        });
    </script>
{{-- @endpush --}}
 