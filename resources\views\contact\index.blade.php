@extends('layouts.master')
@push('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
@endpush
@section('content')
    <section class="head_btn">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <h1>{{ translate('dashboard_contact.contact_messages') }}</h1>
                    <div class="custom_btns">
                        <div class="head_bt">
                            <ul class="nav nav-pills">
                                <li class="active"><a data-toggle="pill" data-target="#user">{{ translate('dashboard_contact.all') }}</a></li>
                                <li><a data-toggle="pill" data-target="#service">{{ translate('dashboard_contact.unresolved') }}</a></li>
                                <li><a data-toggle="pill" data-target="#admins">{{ translate('dashboard_contact.resolved') }}</a></li>
                            </ul>
                        </div>
                        <div class="nav_search main d-flex gap-1">
                            <!-- Actual search box -->
                            <div class="d-inline-block form-group has-feedback has-search">
                                <form class="example" action="/action_page.php" style="width: 100%;">
                                    <button type="button"><i class="fa fa-search"></i></button>
                                    <input type="text" placeholder="{{ translate('dashboard_contact.search') }}" id="searchBar" name="search2" />
                                    {{-- <i class="fa-solid fa-filter" style="color: #000000;"></i> --}}
                                </form>
                            </div>

                            <a href="javascript:void(0)" class="btn btn_yellow" data-toggle="modal"
                                data-target="#edit-temp"> {{ translate('dashboard_contact.edit_temp') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="tab-content">
        {{-- All --}}
        <div id="user" class="tab-pane fade in active">
            <section class="table_sect">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table_wrraper">
                                <div class="table-responsive">
                                    <table id="myTable" class="table  custom_table">
                                        <thead>
                                            <tr>
                                                <th scope="col">{{ translate('dashboard_contact.first_name') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.last_name') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.phone_number') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.email') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.subject') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.status') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.action') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($contact_infos as $contact_info)
                                                <tr>
                                                    <td>{{ $contact_info->first_name }} </td>
                                                    <td>{{ $contact_info->last_name }} </td>
                                                    <td>{{ $contact_info->phone }} </td>
                                                    <td>{{ $contact_info->email }} </td>
                                                    <td>
                                                        <p class="limit">
                                                            {{ $contact_info->subject }}
                                                        </p>
                                                    </td>
                                                    <td>
                                                        @if ($contact_info->status == 0)
                                                            <span class="text-primary">{{ translate('dashboard_contact.unresolved') }} </span>
                                                        @else
                                                            <span class="text-success"> {{ translate('dashboard_contact.resolve') }} </span>
                                                        @endif
                                                    </td>
                                                    <td class="form_btn ">
                                                        <div class="dropdown">
                                                            <button class=" dropdown-toggle" type="button"
                                                                id="dropdownMenuButton" data-toggle="dropdown"
                                                                aria-haspopup="true" aria-expanded="false">
                                                                <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                            </button>
                                                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <a class="dropdown-item view-contact"
                                                                    data-contact-message-id="{{ $contact_info->ids }}"
                                                                    data-toggle="modal"
                                                                    data-target="#view_contact_modal">{{ translate('dashboard_contact.view') }}</a>
                                                                @if ($contact_info->status == 0)
                                                                    <a href="{{ route('contact_message.resolved', $contact_info->ids) }}"
                                                                        class="dropdown-item delete-contact">{{ translate('dashboard_contact.resolved') }}</a>
                                                                @else
                                                                    <a href="{{ route('contact_message.resolved', $contact_info->ids) }}"
                                                                        class="dropdown-item delete-contact">{{ translate('dashboard_contact.unresolved') }}</a>
                                                                @endif
                                                                <a class="dropdown-item reply-contact"
                                                                    data-contact-message-id="{{ $contact_info->ids }}"
                                                                    data-toggle="modal"
                                                                    data-target="#reply-contact">{{ translate('dashboard_contact.reply') }}
                                                                </a>
                                                                <a class="dropdown-item delete-contact"
                                                                    data-contact-message-id="{{ $contact_info->ids }}"
                                                                    data-toggle="modal"
                                                                    data-target="#">{{ translate('dashboard_contact.delete') }}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="6" class="text-center">
                                                        {{ translate('dashboard_contact.no_contact_message') }}</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        {{-- All end --}}
        {{-- Unresolved --}}
        <div id="service" class="tab-pane fade">
            <section class="table_sect">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table_wrraper">
                                <div class="table-responsive">
                                    <table id="myTable" class="table  custom_table">
                                        <thead>
                                            <tr>
                                                <th scope="col">{{ translate('dashboard_contact.first_name') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.last_name') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.phone_number') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.email') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.subject') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.status') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.action') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($contact_infos as $contact_info)
                                                @if ($contact_info->status == 0)
                                                    <tr>
                                                        <td>{{ $contact_info->first_name }} </td>
                                                        <td>{{ $contact_info->last_name }} </td>
                                                        <td>{{ $contact_info->phone }} </td>
                                                        <td>{{ $contact_info->email }} </td>
                                                        <td>
                                                            {{ $contact_info->subject }}
                                                        </td>
                                                        <td>
                                                            <span class="text-primary">{{ translate('dashboard_contact.unresolved') }} </span>
                                                        </td>
                                                        <td class="form_btn ">
                                                            <div class="dropdown">
                                                                <button class=" dropdown-toggle" type="button"
                                                                    id="dropdownMenuButton" data-toggle="dropdown"
                                                                    aria-haspopup="true" aria-expanded="false">
                                                                    <i class="fa-solid fa-ellipsis"
                                                                        style="color: #a0aec0;"></i>
                                                                </button>
                                                                <div class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton">
                                                                    <a class="dropdown-item view-contact"
                                                                        data-contact-message-id="{{ $contact_info->ids }}"
                                                                        data-toggle="modal"
                                                                        data-target="#view_contact_modal">{{ translate('dashboard_contact.view') }}
                                                                    </a>
                                                                    <a class="dropdown-item reply-contact"
                                                                        data-contact-message-id="{{ $contact_info->ids }}"
                                                                        data-toggle="modal"
                                                                        data-target="#reply-contact">{{ translate('dashboard_contact.reply') }}
                                                                    </a>
                                                                    <a class="dropdown-item delete-contact"
                                                                        data-contact-message-id="{{ $contact_info->ids }}"
                                                                        data-toggle="modal"
                                                                        data-target="#">{{ translate('dashboard_contact.delete') }}
                                                                    </a>
                                                                    @if ($contact_info->status == 0)
                                                                        <a href="{{ route('contact_message.resolved', $contact_info->ids) }}"
                                                                            class="dropdown-item delete-contact">{{ translate('dashboard_contact.resolved') }}
                                                                        </a>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endif
                                            @empty
                                                <tr>
                                                    <td colspan="6" class="text-center">
                                                        {{ translate('dashboard_contact.no_contact_message') }}</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        {{-- Unresolved end --}}
        {{-- resolved --}}
        <div id="admins" class="tab-pane fade">
            <section class="table_sect">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table_wrraper">
                                <div class="table-responsive">
                                    <table id="myTable" class="table  custom_table">
                                        <thead>
                                            <tr>
                                                <th scope="col">{{ translate('dashboard_contact.first_name') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.last_name') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.phone_number') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.email') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.subject') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.status') }}</th>
                                                <th scope="col">{{ translate('dashboard_contact.action') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($contact_infos as $contact_info)
                                                @if ($contact_info->status == 1)
                                                    <tr>
                                                        <td>{{ $contact_info->first_name }} </td>
                                                        <td>{{ $contact_info->last_name }} </td>
                                                        <td>{{ $contact_info->phone }} </td>
                                                        <td>{{ $contact_info->email }} </td>
                                                        <td>
                                                            {{ $contact_info->subject }}
                                                        </td>
                                                        <td>
                                                            <span class="text-success">{{ translate('dashboard_contact.resolved') }}</span>
                                                        </td>
                                                        <td class="form_btn ">
                                                            <div class="dropdown">
                                                                <button class=" dropdown-toggle" type="button"
                                                                    id="dropdownMenuButton" data-toggle="dropdown"
                                                                    aria-haspopup="true" aria-expanded="false">
                                                                    <i class="fa-solid fa-ellipsis"
                                                                        style="color: #a0aec0;"></i>
                                                                </button>
                                                                <div class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton">
                                                                    <a class="dropdown-item view-contact"
                                                                        data-contact-message-id="{{ $contact_info->ids }}"
                                                                        data-toggle="modal"
                                                                        data-target="#view_contact_modal">{{ translate('dashboard_contact.view') }}</a>
                                                                    <a class="dropdown-item delete-contact"
                                                                        data-contact-message-id="{{ $contact_info->ids }}"
                                                                        data-toggle="modal"
                                                                        data-target="#">{{ translate('dashboard_contact.delete') }}</a>
                                                                    @if ($contact_info->status == 0)
                                                                        <a href="{{ route('contact_message.resolved', $contact_info->ids) }}"
                                                                            class="dropdown-item approve-contact">{{ translate('dashboard_contact.resolve') }}</a>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endif
                                            @empty
                                                <tr>
                                                    <td colspan="6" class="text-center">
                                                        {{ translate('dashboard_contact.no_contact_message') }}</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
        {{-- resolved end --}}
    </div>
    {{-- tab pane body end --}}
    <section class="report reject">

        {{-- view modal --}}
        <div class="modal fade" id="view_contact_modal" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel1">
            <div class="modal-dialog modal-lg" id="myModal" role="document">
                <div class="modal-content">
                    {{-- Content goes here --}}
                </div>
            </div>
        </div>
        {{-- view modal end --}}


        <div class="modal fade" id="edit-temp" tabindex="-1" role="dialog" aria-labelledby="replyContact">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <form action="{{ route('contact_message.template_update', $customer_template->id) }}" method="POST"
                        class="modal-body">
                        @csrf
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="title">{{ translate('dashboard_contact.template') }}</h1>
                        <div class="form_field_padding">
                            <div class="mb-3 mod_cust_text">
                                <label for="customer_template">{{ translate('dashboard_contact.template_body') }}</label>
                                @if ($customer_template->key_words ?? null)
                                    <label for="Key_words" class="control-label">{{ translate('dashboard_contact.key_words') }} <span
                                            class="text-danger">({{ translate('dashboard_contact.dont_give_space') }})</span></label>
                                    <table class="table table-border table-bordered">
                                        @forelse ($customer_template->key_words as $key => $key_word)
                                            <tr>
                                                <td>
                                                    @php
                                                        echo '{' . '{' . $key . '}' . '}';
                                                    @endphp
                                                </td>
                                                <td>{{ $key_word }}</td>
                                            </tr>
                                        @empty
                                        @endforelse
                                    </table>
                                @endif
                            </div>
                            <div class="mb-3 mod_cust_text">
                                <label for="customer_template">{{ translate('dashboard_contact.template_body') }}</label>
                                <textarea name="template" rows="8" id="customer_template" class="form-control mb-3"
                                    placeholder="{{ translate('dashboard_contact.enter_template_body') }}">{!! $customer_template->body !!}</textarea>
                            </div>
                            <button class="btn_yellow">{{ translate('dashboard_contact.submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="modal fade" id="reply-contact" tabindex="-1" role="dialog" aria-labelledby="replyContact">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content ">
                    <form action="{{ route('contact_message.answer') }}" method="POST" class="modal-body"
                        enctype="multipart/form-data">
                        @csrf
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="title">{{ translate('dashboard_contact.reply_contact') }}</h1>
                        <div class="form_field_padding">
                            <input type="hidden" name="contact_info_id" id="contact_info_id">
                            <div class="mb-3 mod_cust_text">
                                <label for="attachmentsInp">{{ translate('dashboard_contact.attachments') }}</label>
                                <input type="file" id="attachmentsInp" name="attachments" class="form-control">
                            </div>
                            <div class="mb-3 mod_cust_text">
                                <label for="reply-body">{{ translate('dashboard_contact.reply') }}</label>
                                <textarea name="reply_body" rows="8" id="reply_content" class="form-control mb-3"
                                    placeholder="{{ translate('dashboard_contact.enter_reply_here') }}">{{ $reply_template->body }}</textarea>
                            </div>
                            <button class="btn_yellow">{{ translate('dashboard_contact.submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')
    <script src="{{ asset('js/db1.js') }}"></script>
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>
    <script>
        $(document).ready(function() {
            $(".delete-contact").on("click", function() {
                let contact_message_id = $(this).attr("data-contact-message-id");
                if (contact_message_id) {
                    Swal.fire({
                        title: @json(translate('dashboard_contact.are_you_sure')),
                        text: @json(translate('dashboard_contact.delete_confirmation')),
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#3085d6",
                        cancelButtonColor: "#d33",
                        confirmButtonText: "Yes, delete it!"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = "{{ route('contact_message.delete', '') }}/" +
                                contact_message_id;
                        }
                    });
                }
            })

            // view contact
            $(".view-contact").on("click", function() {
                let contact_id = $(this).attr("data-contact-message-id");
                if (contact_id) {
                    $.ajax({
                        url: `{{ route('contact_message.view', '') }}/${contact_id}`,
                        type: "GET",
                        success: function(response) {
                            if (response.status) {
                                $("#view_contact_modal .modal-content").html(response.data);
                            }
                        }
                    })
                } else {
                    return false;
                }
            })


            $(document).on("click", ".reply-contact", function() {
                let contact_id = $(this).data("contact-message-id");
                $("#contact_info_id").val(contact_id);
            })


            // reply template
            CKEDITOR.replace('reply_content', {
                allowedContent: true,
                extraAllowedContent: 'p h1 h2 h3 h4 h5 h6 strong em; a[!href]; ul ol li; img[!src,alt,width,height]',
                disallowedContent: 'script; *[on*]',
                removePlugins: 'paste,sourcearea,image,scayt,templates,about,forms,table,tabletools,tableselection,iframe,div,language',
                removeButtons: 'ExportPdf,NewPage,Save'
            });
            // reply template end

            // customer template
            CKEDITOR.replace('customer_template', {
                allowedContent: true,
                extraAllowedContent: 'p h1 h2 h3 h4 h5 h6 strong em; a[!href]; ul ol li; img[!src,alt,width,height]',
                disallowedContent: 'script; *[on*]',
                removePlugins: 'paste,sourcearea,image,scayt,templates,about,forms,table,tabletools,tableselection,iframe,div,language',
                removeButtons: 'ExportPdf,NewPage,Save'
            });
            // customer template end
        })
    </script>
@endpush
