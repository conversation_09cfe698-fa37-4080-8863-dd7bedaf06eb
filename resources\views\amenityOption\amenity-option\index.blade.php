@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <div class="container-fluid">
        <!-- .row -->
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    <div class="d-flex justify-content-between">
                        <h3 class="box-title">
                            {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOptions of ') }}{{ $amenity->name ?? '' }}
                        </h3>
                        <div class="d-flex">
                            <div class="nav_search main me-2">
                                <!-- Actual search box -->
                                <div class="form-group has-feedback has-search m-0">
                                    <form class="example" action="/action_page.php" style="width: 100%">
                                        <button type="button"><i class="fa fa-search"></i></button>
                                        <input type="text" placeholder="{{ translate('content_management_system.search') }}" id="searchBar" class="searchBar"
                                            name="search">
                                        {{-- <i class="fa-solid fa-filter" style="color: #000000;"></i> --}}
                                    </form>
                                </div>
                            </div>
                            <a class="btn btn_trans topbar me-2" href="{{ url('cms') }}#amenities_pane">{{ translate('content_management_system.back') }}</a>


                            @can('add-' . str_slug('AmenityOption'))
                                <a class="btn btn_yellow"
                                    href="{{ route('amenity-option.create', ['amenity_id' => $amenity->ids]) }}">
                                    {{-- <i class="icon-plus"></i>  --}}
                                    {{ translate('content_management_system.add') }}
                                    {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', translate('content_management_system.amenity_option')) }}</a>
                            @endcan
                        </div>
                    </div>
                    <div class="clearfix"></div>
                    <hr>
                    <div class="table-responsive">
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{{ translate('content_management_system.amenity') }}</th>
                                    {{-- <th>{{ __('name') }}</th> --}}
                                    <th>{{ translate('content_management_system.action')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($amenityoption as $item)
                                    <tr>
                                        <td>{{ $loop->iteration ?? $item->id }}</td>
                                        {{-- <td>{{ $item->amenity->name ?? '' }}</td> --}}
                                        <td>{{ $item->name }}</td>
                                        <td class="form_btn ">
                                            <div class="dropdown">
                                                <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                </button>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    @can('view-' . str_slug('AmenityOption'))
                                                        <a href="#!" data-toggle="modal" data-target="#amenityDetail"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}">
                                                            <button class="dropdown-item view-detail"
                                                                data-amenity-id="{{ $item->id }}">
                                                                {{ translate('content_management_system.view') }}
                                                            </button>
                                                        </a>
                                                        {{-- <a href="{{ url('/amenityOption/amenity-option/' . $item->id) }}"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}">
                                                            <button class="dropdown-item">
                                                                View
                                                            </button>
                                                        </a> --}}
                                                    @endcan
                                                    @can('edit-' . str_slug('AmenityOption'))
                                                        <a href="{{ route('amenity-option.edit', ['amenity_id' => $amenity->ids, 'amenity_option' => $item->id]) }}"
                                                            title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}">
                                                            <button class="dropdown-item">
                                                                {{ translate('content_management_system.edit') }}
                                                            </button>
                                                        </a>
                                                    @endcan
                                                    @can('delete-' . str_slug('AmenityOption'))
                                                        <form method="POST"
                                                            action="{{route('amenity-option.destroy', ['amenity_id' => $amenity->ids, 'amenity_option' => $item->id])}}"
                                                            accept-charset="UTF-8" style="display:inline">
                                                            {{ method_field('DELETE') }}
                                                            {{ csrf_field() }}
                                                            <button type="submit" class="dropdown-item"
                                                                title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}"
                                                                {{-- onclick="return confirm(&quot;Confirm delete?&quot;)" --}}
                                                                onclick="confirmDelete(event, this)">
                                                                {{ translate('content_management_system.delete') }}
                                                            </button>
                                                        </form>
                                                    @endcan
                                                </div>
                                            </div>
                                        </td>
                                        {{-- <td>
                                            @can('view-' . str_slug('AmenityOption'))
                                                <a href="{{ url('/amenityOption/amenity-option/' . $item->id) }}"
                                                    title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}">
                                                    <button class="btn btn-info btn-sm">
                                                        <i class="fa fa-eye" aria-hidden="true"></i> {{ __('view') }}
                                                    </button>
                                                </a>
                                            @endcan

                                            @can('edit-' . str_slug('AmenityOption'))
                                                <a href="{{ url('/amenityOption/amenity-option/' . $item->id . '/edit') }}"
                                                    title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}">
                                                    <button class="btn btn-primary btn-sm">
                                                        <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                                        {{ __('edit') }}
                                                    </button>
                                                </a>
                                            @endcan

                                            @can('delete-' . str_slug('AmenityOption'))
                                                <form method="POST"
                                                    action="{{ url('/amenityOption/amenity-option' . '/' . $item->id) }}"
                                                    accept-charset="UTF-8" style="display:inline">
                                                    {{ method_field('DELETE') }}
                                                    {{ csrf_field() }}
                                                    <button type="submit" class="btn btn-danger btn-sm"
                                                        title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'AmenityOption') }}"
                                                        onclick="return confirm(&quot;Confirm delete?&quot;)"><i
                                                            class="fa fa-trash-o" aria-hidden="true"></i> {{ __('delete') }}
                                                    </button>
                                                </form>
                                            @endcan


                                        </td> --}}
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" style="text-align: center">{{ translate('content_management_system.no_amenity_in_this_category') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        <div class="pagination-wrapper"> {!! $amenityoption->appends(['search' => Request::get('search')])->render() !!} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Modal --}}
    <section class="report amenity popup">
        <div class="modal fade" id="amenityDetail" tabindex="-1" role="dialog" aria-labelledby="amenityDetail">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="title">{{ translate('content_management_system.amenity_detail') }}</h1>
                        <div class="form_field_padding">
                            <div class="loader text-center">{{ translate('content_management_system.loading') }}...</div>
                            <div class="mb-3 mod_cust_text d-none">
                                <div class="amen_img">
                                    <img src="" alt="Amenity Image" class="img-fluid"
                                        onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;">
                                </div>
                                <p id="name"><span>{{ translate('content_management_system.amenity_name') }} {{ translate('content_management_system.in_eng') }}:</span>
                                    <span class="info"></span>
                                </p>
                                <p id="amenityNameSpanish"><span>{{ translate('content_management_system.amenity_name')  }}
                                        {{ translate('content_management_system.in_spanish') }}:</span>
                                    <span class="info"></span>
                                </p>
                                <p id="desc_eng"><span>{{ translate('content_management_system.description') }} {{ translate('content_management_system.in_eng') }}:</span>
                                    <span class="info"> </span>
                                </p>
                                <p id="desc_spanish"><span>{{ translate('content_management_system.description') }} {{ translate('content_management_system.in_spanish') }}:</span>
                                    <span class="info"></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('js')

    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    <script>

        function confirmDelete(event, button) {
            event.preventDefault();
            Swal.fire({
                title: @json(translate('content_management_system.are_you_sure')),
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#FFCE32",
                cancelButtonColor: "#000",
                confirmButtonText: @json(translate('content_management_system.yes_delete_it')),
            }).then((result) => {
                if (result.isConfirmed) {
                    let form = button.closest("form");
                    if (form) {
                        form.submit();
                    }
                }
            });
        }

        $(function() {
            $('#myTable').DataTable({
                pageLength: 10,
                drawCallback: function (settings) {
                    var api = this.api();
                    var dataCount = api.data().length;
                    var pageLength = api.page.len();

                    if (dataCount <= pageLength) {
                        $('.dataTables_paginate, .dataTables_info').hide();
                    } else {
                        $('.dataTables_paginate, .dataTables_info').show();
                    }
                },
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }]
            });

        });
        $(document).on("click", ".view-detail", function() {
            var amenityID = $(this).data('amenity-id');

            $.ajax({
                url: "{{ url($amenity->ids . '/amenity-option') }}" + '/' + amenityID,
                type: 'GET',
                success: function(response) {
                    if (response.status) {
                        var data = response.data;

                        // Set default values
                        var nameEn = "N/A";
                        var nameEs = "N/A";
                        var descEn = "N/A";
                        var descEs = "N/A";

                        // Extract translations
                        if (data.translations && data.translations.length > 0) {
                            data.translations.forEach(function(translation) {
                                if (translation.locale === 'en') {
                                    nameEn = translation.name;
                                    descEn = translation.description;
                                }
                                if (translation.locale === 'es') {
                                    nameEs = translation.name;
                                    descEs = translation.description;
                                }
                            });
                        }

                        // Populate modal fields
                        $('#amenityDetail').modal('show');
                        $('#name .info').html(nameEn);
                        $('#amenityNameSpanish .info').html(nameEs);
                        $('#desc_eng .info').html(descEn);
                        $('#desc_spanish .info').html(descEs);
                        $('#amenityDetail .loader').addClass('d-none');
                        $('#amenityDetail .mod_cust_text').removeClass('d-none');



                        // Set image with a fallback
                        var imagePath = data.image ? "{{ asset('website') }}/" + data.image :
                            "{{ asset('website/images/plcaeholderListingImg.png') }}";
                        $('.amen_img img').attr('src', imagePath);
                    } else {
                        alert("Error: Data not found.");
                    }
                },
                error: function(xhr) {
                    console.error("Error fetching amenity details:", xhr);
                }
            });
        });
    </script>
@endpush
