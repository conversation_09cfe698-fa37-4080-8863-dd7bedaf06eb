<?php

namespace App\Http\Controllers;

use App\Booking;
use App\CommonSetting;
use App\Http\Controllers\Controller;
use App\Listing;
use App\Models\CurrencyConversionRate;
use App\Models\User;
use App\Services\{BookingService, CancellationPolicyService, ListingService};
use App\Wallet;
use App\WithdrawalRequest;
use Carbon\Carbon;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CronController extends Controller
{
    private $clientId;
    private $clientSecret;
    function __construct(
        protected CancellationPolicyService $cancellationPolicyService,
        protected BookingService $bookingService,
        protected ListingService $listingService
    ) {
        $this->clientId = "tipalti.thirdpartyapi.FTeCrli-8e3rUbeQTMrzBh4FxKg";
        $this->clientSecret = "aSTyuU6SY0rjOzx0EcVzOYqyW_0";
    }
    function index()
    {
        $bookings = Booking::where("status", 0)->get();
        $completedBookings = 0;

        foreach ($bookings as $booking) {
            if ($booking->check_out) {
                $check_out = Carbon::parse($booking->check_out);

                // For Daily bookings, mark as completed if check_out date has passed
                if (in_array($booking->listing_basis, ["Daily", "Tour"]) && $check_out <= today()) {
                    $booking->status = 3; // Completed
                    $booking->save();
                    $completedBookings++;
                }

                // For Hourly bookings, check if all slots have passed
                elseif ($booking->listing_basis == "Hourly") {
                    $currentTime = Carbon::now();
                    $allSlots = $booking->hourly_slots;

                    if ($allSlots->count() > 0) {
                        $allSlotsPassed = true;

                        foreach ($allSlots as $slot) {
                            // Parse the slot time (format is typically "21:00 - 22:00")
                            $slotParts = explode(' - ', $slot->slot);

                            if (count($slotParts) == 2) {
                                // Use the slot's date field to construct the full datetime
                                $slotStartTime = Carbon::parse($slot->date . ' ' . $slotParts[0]);
                                $slotEndTime = Carbon::parse($slot->date . ' ' . $slotParts[1]);

                                // Handle midnight crossover (e.g., "23:00 - 00:00")
                                // If end time is earlier than start time, it means it crosses midnight
                                if ($slotEndTime <= $slotStartTime) {
                                    $slotEndTime->addDay(); // Move end time to next day
                                }

                                // If any slot's end time is in the future, not all slots have passed
                                if ($slotEndTime > $currentTime) {
                                    $allSlotsPassed = false;
                                    break;
                                }
                            }
                        }

                        // If all slots have passed, mark the booking as completed
                        if ($allSlotsPassed) {
                            $booking->status = 3; // Completed
                            $booking->save();
                            $completedBookings++;
                        }
                    } else {
                        // If no slots found but check_out date has passed, mark as completed (fallback)
                        if ($check_out <= Carbon::now()) {
                            $booking->status = 3; // Completed
                            $booking->save();
                            $completedBookings++;
                        }
                    }
                }
            }
        }

        return [
            "message" => "Cron successfully done",
            "completed_bookings" => $completedBookings,
            "today" => today()->format("Y-m-d H:i:s")
        ];
    }
    public function walletAmount()
    {
        // Fetch all the bookings with status 3 (completed) and is_wallet = 0 (wallet not updated)
        $bookings = Booking::where("status", 3)
            ->where('is_wallet', 0)
            ->get();
        foreach ($bookings as $booking) {
            // Fetch the provider's completed bookings sorted by the earliest first
            $providerBookings = Booking::where('provider_id', $booking->provider_id)
                //->where('status', 3) // Only consider completed bookings
                ->orderBy('check_out', 'asc')  // Sort by earliest check_out date
                ->get();
            // Check if this is the first booking for this provider
            $firstBooking = $providerBookings->first(); // Get the first completed booking
            if ($firstBooking) {
                // Check if it's the first booking (i.e., there's no previous booking in the last 30 days)
                // $firstBookingCheck = $providerBookings->where('check_out', '>=', Carbon::now()->subDays(30))->count();
                $firstBookingCheck = $providerBookings->where('check_out', '>=', Carbon::parse($firstBooking->check_out)->subDays(2))->count();
                // If it's the first booking and it's after 30 days of checkout, add the amount to wallet
                if ($firstBookingCheck == 1 && Carbon::parse($firstBooking->check_out)->addDays(2)->isPast()) {
                    // Get the tax rate safely, defaulting to a value if listing or category is null
                    $taxRate = 0; // Default tax rate
                    if ($booking->listing && $booking->listing->category) {
                        $taxRate = $booking->listing->category->tax;
                    }
                    
                    $serviceProviderAmount = $booking->sub_total - get_percentage($taxRate, $booking->sub_total);
                    $wallet = Wallet::where('user_id', $booking->provider_id)->first();
                    
                    if ($wallet) {
                        $wallet->amount += $serviceProviderAmount;
                        // Only add commission amount if we have a valid tax rate
                        if ($booking->listing && $booking->listing->category) {
                            $wallet->commission_amount += get_percentage($taxRate, $booking->sub_total);
                        }
                        $wallet->save();
                    } else {
                        $walletData = [
                            'user_id' => $booking->provider_id,
                            'amount' => $serviceProviderAmount,
                        ];
                        
                        // Only add commission amount if we have a valid tax rate
                        if ($booking->listing && $booking->listing->category) {
                            $walletData['commission_amount'] = get_percentage($taxRate, $booking->sub_total);
                        }
                        
                        Wallet::create($walletData);
                    }
                    
                    $booking->is_wallet = 1;
                    $booking->save();
                }
                // For subsequent bookings, add the amount after 48 hours of the checkout
                elseif ($firstBookingCheck > 1 && Carbon::parse($booking->check_out)->addHours(48)->isPast()) {
                    // Get the tax rate safely, defaulting to a value if listing or category is null
                    $taxRate = 10; // Default tax rate
                    if ($booking->listing && $booking->listing->category) {
                        $taxRate = $booking->listing->category->tax;
                    }
                    
                    $serviceProviderAmount = $booking->sub_total - get_percentage($taxRate, $booking->sub_total);
                    $wallet = Wallet::where('user_id', $booking->provider_id)->first();
                    
                    if ($wallet) {
                        $wallet->amount += $serviceProviderAmount;
                        // Only add commission amount if we have a valid tax rate
                        if ($booking->listing && $booking->listing->category) {
                            $wallet->commission_amount += get_percentage($taxRate, $booking->sub_total);
                        }
                        $wallet->save();
                    } else {
                        $walletData = [
                            'user_id' => $booking->provider_id,
                            'amount' => $serviceProviderAmount,
                        ];
                        
                        // Only add commission amount if we have a valid tax rate
                        if ($booking->listing && $booking->listing->category) {
                            $walletData['commission_amount'] = get_percentage($taxRate, $booking->sub_total);
                        }
                        
                        Wallet::create($walletData);
                    }
                    
                    $booking->is_wallet = 1;
                    $booking->save();
                }
            }
        }
        return ["message" => "Wallet Cron successfully executed", "today" => today()->format("Y-m-d H:i:s")];
    }
    function cancellationAmount()
    {
        $bookings = Booking::where("status", 7)->where("is_wallet", 0)
            ->whereDate('check_out', '<=', Carbon::now()->subHours(48)->toDateString())
            ->get();
        foreach ($bookings as $booking) {
            $wallet = Wallet::where('user_id', $booking->provider_id)->first();
            
            // Get tax rate safely
            $taxRate = 10;
            if ($booking->listing && $booking->listing->category) {
                $taxRate = $booking->listing->category->tax;
            }
            
            $refund_amount = $booking->service_refund_amount_cop - get_percentage($taxRate, $booking->service_refund_amount_cop);
            
            if ($refund_amount > 0) {
                if ($wallet) {
                    $wallet->amount += $refund_amount;
                    $wallet->save();
                } else {
                    Wallet::create([
                        'user_id' => $booking->provider_id,
                        'amount' => $refund_amount,
                    ]);
                }
                $booking->is_wallet = 1;
                $booking->save();
            }
        }
        return api_response(true, "Wallet Cancel Cron job successfully executed!");
    }
    public function scheduledAmount()
    {
        $users = User::whereNotNull('schedule_amount')->get();
        foreach ($users as $user) {
            $wallet = Wallet::where('user_id', $user->id)->first();
            // Check if user already has a pending withdrawal request
            $pendingRequest = WithdrawalRequest::where('user_id', $user->id)
                ->whereIn('status', [0]) // Pending statuses
                ->where('amount', '>=', $wallet->amount)
                ->exists();
            if (!$pendingRequest && $wallet && $wallet->amount >= $user->schedule_amount && $user->schedule_active == 1) {
                $usd_conversion_rate = CurrencyConversionRate::where('target_currency', 'USD')->value('rate');
                $usd_conversion_rate = $usd_conversion_rate ? $usd_conversion_rate : 1;
                // Calculate USD equivalent of wallet amount to ensure it meets minimum requirement
                $walletAmountInUsd = $wallet->amount * $usd_conversion_rate;
                $this->refreshAccessToken();
                $accessToken = session('tipalti_access_token');
                if (!$accessToken) {
                    return back()->with(["message" => "Missing Tipalti Access Token", "type" => "error"]);
                }
                if ($walletAmountInUsd >= 50) {
                    $reference_code = uniqid();
                    $response = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $accessToken,
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                    ])->post('https://api-sb.tipalti.com/api/v1/payment-batches', [
                        'paymentInstructions' => [
                            [
                                'amountSubmitted' => [
                                    'amount' => $wallet->amount, // Send entire wallet balance
                                    'currency' => 'COP',
                                ],
                                'refCode' => (string)$reference_code,
                                'payeeId' => $user->payee_id,
                            ]
                        ]
                    ]);
                    $data = $response->json();
                    if ($response->successful()) {
                        $withdrawal_request = new WithdrawalRequest();
                        $withdrawal_request->user_id = $user->id;
                        $withdrawal_request->reference_code = $reference_code;
                        $withdrawal_request->currency = 'COP';
                        $withdrawal_request->amount = $wallet->amount; // Record entire amount being withdrawn
                        $withdrawal_request->wallet_amount = $wallet->amount;
                        $withdrawal_request->status = 0; // Pending status
                        $withdrawal_request->save();
                        $wallet->save();
                        // Log success
                        Log::info('Auto withdrawal request for user ' . $user->id . ' processed successfully. Amount: ' . $withdrawal_request->amount . ' COP');
                    } else {
                        Log::error('Error in sending auto withdrawal request to Tipalti: ' . json_encode($data));
                    }
                } else {
                    Log::warning('Wallet amount less than required USD minimum for user ' . $user->id . '. Amount: ' . $wallet->amount . ' COP, USD equivalent: ' . $walletAmountInUsd . ' USD');
                }
            }
        }
        return "Scheduled Wallet Cron job successfully executed!";
    }
    public function refreshAccessToken()
    {
        $client = new Client();
        $refreshToken = CommonSetting::first()->refresh_token ?? '';
        // $refreshToken = session()->get('tipalti_refresh_token');
        if (!$refreshToken) {
            //return response()->json(["error" => "Refresh token is missing."], 400);
            return redirect()->route('tipalti.auth');
        }
        try {
            $response = $client->post('https://sso.sandbox.tipalti.com/connect/token', [
                'form_params' => [
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $refreshToken,
                ],
                'headers' => [
                    'Accept' => 'application/json',
                ],
            ]);
            $tokenData = json_decode($response->getBody(), true);
            session()->put('tipalti_access_token', $tokenData['access_token']);
            session()->put('tipalti_refresh_token', $tokenData['refresh_token']);
            session()->put('tipalti_token_expires_in', now()->addSeconds($tokenData['expires_in']));
            return response()->json(["message" => "Access token refreshed successfully", "token" => $tokenData]);
        } catch (\Exception $e) {
            return response()->json(["error" => "Failed to refresh access token", "message" => $e->getMessage()], 400);
        }
    }
    function delete_draft_listing()
    {
        $draft_listings = Listing::where("status", 6)
            ->whereDate('created_at', '<=', Carbon::now()->subDays(30))
            ->get();
        foreach ($draft_listings as $draft_listing) {
            $this->listingService->deleteListing($draft_listing->id);
        }
        return api_response(true, "Draft Listing Deleted");
    }
}
