@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', 'title');
@endphp
<fieldset class="accommodation_title_step">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? '' }}</h2>
                    </div>
                    @isset($step_data->sub_title)
                        <div class="content">
                            <p>{{ $step_data->sub_title ?? '' }}</p>
                        </div>
                    @endisset
                    <div class="accommodation_title_field">
                        <div class="txt_field">
                            <input name="name" id="count-title" maxlength="50" value="{{ $listing->name ?? '' }}"
                                placeholder="{{ translate('stepper.eg_vip_helicopter_tour_over_the_caribbean_coast') }}">
                            <p id="titleCount" class="text-end">0/50</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script>
        $(document).ready(function() {
            var $titleInput = $("#count-title");
            var maxLength = 50;
            var $charCount = $("#titleCount");
            function updateCharCount() {
                var textLength = $titleInput.val().length;
                if (textLength > maxLength) {
                    $titleInput.val($titleInput.val().substring(0, maxLength));
                    textLength = maxLength;
                }
                $charCount.text(textLength + "/" + maxLength);
            }
            $(document).on('input', '#count-title', function() {
                updateCharCount();
            });
            updateCharCount();
        });
    </script>
@endpush
