@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "ownership");
@endphp
<fieldset class="owner_documents_step">
    <div class="inner_section_fieldset h_100">
        <div class="row h_100">
            <div class="col-md-6 col_left">
                <div class="inner_section_left">
                    <h2 class="pb-2"> {{ $step_data->title ?? "" }} </h2>
                    <div class=" top_box1 d-flex justify-content-between py-2 flex-wrap">
                        <div class="inner_section_ownership_docs">
                            <div class="content">
                                <p>
                                    {{$step_data->sub_title ?? ""}}
                                </p>
                                <p>
                                   {{ translate('stepper.need_help') }}    <a href="{{ ($step_data->url ?? "") }}" class="list_of_accepted_doc_btn"
                                        target="_blank">{{ translate('stepper.accepted_documents_info') }} </a>
                                </p>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 col_right">
                <div class="inner_section_right">
                    <div class="step_image_wrapper">
                        <img src="{{ asset('website') . "/" . ($step_data->image ?? "") }}" alt="{{ translate('stepper.stacked_documents') }}"
                            class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    {{-- <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" /> --}}

</fieldset>


@push('js')
    <script></script>
@endpush
