@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "map");
@endphp

<div class="col-md-12">
    <div class="inner_section_main_col">
        <div class="main_step_title">
            <h2>{{ $step_data->title ?? "" }}</h2>
        </div>
        
        @isset($step_data->sub_title)
            <div class="step_description">
                <p>{{ $step_data->sub_title ?? "" }}</p>
            </div>
        @endisset
        <div class="location_map_wrapper">
            <input id="autocomplete" name="address" class="form-control" value="{{ $listing->address?->address ?? '' }}"
                type="text" placeholder="{{ translate('stepper.enter_your_address_here') }}">
            <input id="lat" name="lat" type="text" class="no_validate" value="{{ $listing->lat ?? '' }}"
                hidden />
            <input id="lng" name="lng" type="text" class="no_validate" value="{{ $listing->lng ?? '' }}"
                hidden />
            <div class="scrollable-section" id="map"></div>
        </div>
    </div>
</div>



@push('js')
    <script>
        var infowindow, marker;

        function initMap() {
            var map = new google.maps.Map(document.getElementById('map'), {
                center: {
                    lat: {{ $listing->lat ?? '41.3873974' }},
                    lng: {{ $listing->lng ?? '2.168568' }}
                },
                zoom: 14,
                draggable: true
            });

            var input = document.getElementById('autocomplete');
            var autocomplete = new google.maps.places.Autocomplete(input);
            autocomplete.bindTo('bounds', map);

            infowindow = new google.maps.InfoWindow();
            marker = new google.maps.Marker({
                map: map,
                anchorPoint: new google.maps.Point(0, -29),
                draggable: true // Make the marker draggable
            });

            autocomplete.addListener('place_changed', function() {
                marker.setVisible(false);
                var place = autocomplete.getPlace();
                if (!place.geometry) {
                    window.alert("No details available for input: '" + place.name + "'");
                    return;
                }

                updateTextFields(place.geometry.location.lat(), place.geometry.location.lng());
                extractAddressComponents(place);

                if (place.geometry.viewport) {
                    map.fitBounds(place.geometry.viewport);
                } else {
                    map.setCenter(place.geometry.location);
                    map.setZoom(17); // Optimal zoom
                }
                marker.setPosition(place.geometry.location);
                marker.setVisible(true);


                var content = '<div class="map-caption"><b>' + place.name + '</b><br>' + place.formatted_address +
                    '</div>';
                infowindow.setContent(content);
                infowindow.open(map, marker);
            });

            // Listen for marker drag end event
            marker.addListener('dragend', function() {
                var position = marker.getPosition();
                updateTextFields(position.lat(), position.lng());
                reverseGeocode(position);
            });
        }

        function updateTextFields(lat, lng) {
            $('#lat').val(lat);
            $('#lng').val(lng);
        }

        function extractAddressComponents(place) {
            var city = '',
                country = '',
                zipCode = '',
                address = '',
                state = '';
            if (place.address_components) {
                address = place.formatted_address; // Full address
                place.address_components.forEach(function(component) {
                    var types = component.types;
                    if (types.includes('locality')) {
                        city = component.long_name;
                    } else if (types.includes('country')) {
                        country = component.long_name;
                    } else if (types.includes('postal_code')) {
                        zipCode = component.long_name;
                    } else if (types.includes('administrative_area_level_1')) {
                        state = component.long_name;
                    }
                });
            }

            $('#country').val(country);
            $('#street').val(address);
            $('#city').val(city);
            $('#state').val(state);
            $('#zip').val(zipCode);
            $('#autocomplete').val(place.formatted_address);
        }

        function reverseGeocode(position) {
            var geocoder = new google.maps.Geocoder();
            geocoder.geocode({
                'location': position
            }, function(results, status) {
                if (status === 'OK') {
                    if (results[0]) {
                        var place = results[0];
                        console.log(place);
                        extractAddressComponents(place);
                        infowindow.setContent('<div><strong>Location:</strong><br>' + place.formatted_address +
                            '</div>');
                        infowindow.open(map, marker);

                    } else {
                        window.alert('No results found');
                    }
                } else {
                    window.alert('Geocoder failed due to: ' + status);
                }
            });
        }
    </script>
    <script src="{{ url('google-map') }}?libraries=places&callback=initMap"></script>
@endpush
