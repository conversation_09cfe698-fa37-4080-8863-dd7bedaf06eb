<?php

namespace App\Services;

use App\Mail\PasswordUpdatedMail;
use App\Models\PasswordOtp;
use App\Notifications\UserRegisterNotification;
use App\Models\User;
use App\Profile;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Services\VerificationService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class AuthService
{
    protected $verificationService;
    function __construct(VerificationService $verificationService)
    {
        $this->verificationService = $verificationService;
    }
    function reset_password($request_data)
    {
        try {
            $validator = Validator::make($request_data, [
                "otp" => "required",
                'email' => 'required|email',
                'new_password' => [
                    'required',
                    'min:8',
                    'max:32',
                    'regex:/[A-Z]/',      // at least one uppercase letter
                    'regex:/[a-z]/',      // at least one lowercase letter
                    'regex:/[0-9]/',      // at least one digit
                    'regex:/[@#$%^&*()_+\-=\[\]{};:"\\|,.<>\/?]+/', // at least one special character
                    'same:confirm_password',
                ],
                'confirm_password' => 'required|min:8',

            ], [
                "new_password.min" => 'Password must be at least 8 characters.',
                "new_password.max" => 'Password may not be greater than 32 characters.',
                "new_password.regex" => 'Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character.',
                "new_password.confirmed" => 'Passwords do not match.'
            ]);
            if ($validator->fails()) {
                return ["status" => false, "message" => $validator->errors(), "data" => null];
            }
            $user = User::where("email", $request_data["email"])->first();
            if ($user) {
                $password_verify = PasswordOtp::where("email", $user->email)->first();
                if (!$password_verify || decrypt($password_verify->is_verified) == 0 || !Hash::check($request_data["otp"], $password_verify->code)) {
                    return ["status" => false, "message" => "please verify otp of forget password ", "data" => null];
                } else {
                    if ($user->email == $password_verify->email) {
                        $password_verify->delete();
                        $user->password = Hash::make($request_data["new_password"]);
                        $user->save();
                        $data["name"] = $user->name;
                        $data["date"] = today();
                        Mail::to($user->email)->send(new PasswordUpdatedMail($data));
                        return ["status" => true, "message" => "Password updated successfully", "data" => $user];
                    } else {
                        return ["status" => true, "message" => "Please change the verified otp email password", "data" => $user];
                    }
                }
            } else {
                return ["status" => false, "message" => "email not found ", "data" => null];
            }
        } catch (\Throwable $th) {
            return ["status" => false, "message" => $th->getMessage(), "data" => null];
        }
    }

    function signup($request_data)
    {
        try {
            $user = User::create([
                'name' => $request_data["first_name"] . " " . $request_data["last_name"],
                'first_name' => $request_data["first_name"],
                'last_name' => $request_data["last_name"],
                'phone' => $request_data["phone"],
                'email' => $request_data["email"],
                'password' => Hash::make($request_data["password"]),
                "avatar" => "user-profile/no_avatar.jpg",
                "device_token" => $request_data["device_token"],
                "device_type" => $request_data["device_type"]
            ]);
            if ($user->profile == null) {
                $profile = new Profile();
                $profile->user_id = $user->id;
                $profile->save();
            }
            activity($user->name)
                ->performedOn($user)
                ->causedBy($user)
                ->log('Registered');
            $user->assignRole('customer');
            $this->verificationService->send_otp($request_data['email'], "email", "email_verfication");
            $user->notify(new UserRegisterNotification("Congratulations! " . $user->name . " your account has been registered at LuxuStars"));
            admins_notification(new UserRegisterNotification("New Registration " . $user->name . " has been registered"));
            if (Auth::attempt(["email" => $request_data["email"], "password" => $request_data["password"]])) {
                $new_user = User::find($user->id);
                return response()->json([
                    'status' => true,
                    'message' => 'User Created Successfully',
                    'data' => [
                        'user' => $new_user,
                        'token' => $user->createToken("API TOKEN")->plainTextToken
                    ],
                ], 200);
            }
        } catch (\Exception $e) {
            return ["status" => false, "message" => $e->getMessage() . $e->getLine() . " on " . $e->getFile(), "data" => null];
        }
    }

    function email_verification($request_data)
    {
        session()->put("user_data", $request_data);
        return $this->verificationService->send_otp($request_data['email'], "email", "email_verfication");
    }
    function wallet_verification($request_data)
    {
        session()->put("wallet_data", $request_data);
        return $this->verificationService->send_otp(auth()->user()->email, "email", "wallet_verfication");
    }
}
