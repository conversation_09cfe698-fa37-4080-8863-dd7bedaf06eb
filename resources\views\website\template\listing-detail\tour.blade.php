e<div class="col-md-12 listing_custom_meta divider listing_data">
    <ul class="list-unstyled d-flex gap-3 m-0 parent-box flex-wrap">
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/user.svg') }}" alt="" height="20px" width="20px">
            {{ $listing->detail->booking_capacity ?? 0 }} {{ translate('listing_details.booking_capacity') }}
        </li>
        @if (($listing->detail->tour_day_type ?? '') == 'same_day')
            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                <img src="{{ asset('website/images/hourglass.png') }}" alt="" height="20px" width="20px">
                {{ translate('listing_details.same_day_tour') }}
                {{-- <div class="time pt-2">
                    <div class="d-flex gap-3 align-items-center justify-content-between w-100">
                        <p class="m-0 fs-14">Start Time</p>
                        <p class="m-0 fs-14">{{ $listing->detail->start_time ?? '-' }}</p>
                    </div>
                    <div class="d-flex gap-3 align-items-center justify-content-between w-100">
                        <p class="m-0 fs-14">End Time</p>
                        <p class="m-0 fs-14">{{ $listing->detail->end_time ?? '-' }}</p>
                    </div>
                </div> --}}
            </li>
            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                <img src="{{ asset('website/images/hourglass.png') }}" alt="" height="20px" width="20px">
                {{ translate('listing_details.duration') }}:
                {{ \Carbon\Carbon::parse($listing->detail->start_time)->diffInHours(\Carbon\Carbon::parse($listing->detail->end_time)) }}
                {{ translate('listing_details.hours') }}
            </li>
        @else
            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                <img src="{{ asset('website/images/hourglass.png') }}" alt="" height="20px" width="20px">
                {{ $listing->tour_durations->count() }}
                {{ translate('listing_details.days_tour') }}
            </li>
            {{-- @foreach ($listing->tour_durations as $tour_duration)
                <li class="catg box" data-aos="fade">
                    <b>Day {{ $tour_duration->day }}</b>
                    <div class="time pt-2">
                        <div class="d-flex gap-3 align-items-center justify-content-between w-100">
                            <p class="m-0 fs-14">Start Time</p>
                            <p class="m-0 fs-14">{{ $tour_duration->start_time }}</p>
                        </div>
                        <div class="d-flex gap-3 align-items-center justify-content-between w-100">
                            <p class="m-0 fs-14">End Time</p>
                            <p class="m-0 fs-14">{{ $tour_duration->end_time }}</p>
                        </div>
                    </div>
                </li>
            @endforeach --}}
        @endif
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/newborn-baby-icon.png') }}" alt="" height="20px" width="20px">
            @if ($listing->detail->child_allow == 'yes')
                {{ translate('listing_details.children_allowed') }}
            @else
                {{ translate('listing_details.children_not_allowed') }}
            @endif
        </li>
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px" width="20px">
            @if ($listing->detail->pet == 'yes')
                {{ translate('listing_details.pets_allowed') }}
            @else
                {{ translate('listing_details.pets_not_allowed') }}
            @endif
        </li>
        @if (($listing->detail->tour_day_type ?? '') == 'same_day')
            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                <img src="{{ asset('website/images/hourglass.png') }}" alt="" height="20px" width="20px">
                {{ translate('listing_details.start_time') }}: {{ $listing->detail->start_time }}
            </li>
            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                <img src="{{ asset('website/images/hourglass.png') }}" alt="" height="20px" width="20px">
                {{ translate('listing_details.end_time') }}: {{ $listing->detail->end_time }}
            </li>
        @endif
    </ul>
</div>
{{-- Amenities --}}
@if (isset($listing->amenity_detail[0]))
    <div class="col-lg-12 divider listing_data listing_amenities">
        <div class="amenities-box">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.accessibility_features') }}</h3>
            <div class="parent-box row g-0 align-items-center">
                @foreach ($listing->amenity_detail as $key => $amenities)
                    @if ($key < 6)
                        <div class="col-md-3 col-sm-12">
                            <div class="box d-flex gap-3 align-items-start" data-aos="fade">
                                <img src="{{ asset('website') . '/' . $amenities->image }}" alt=""
                                    height="30px" width="30px"
                                    onerror="this.onerror=null;this.src=`{{ asset('website/images/snowflake.png') }}`;">
                                <div class="amenity-data">
                                    <span>{{ ucfirst($amenities->name) ?? '' }}</span>
                                    @isset($amenities->description)
                                        <p class="amenties_desc fs-14 text-black-50">
                                            {{ $amenities->description ?? '' }}
                                        </p>
                                    @endisset

                                </div>
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
            {{-- @if (count($listing->amenity_detail) > 16) --}}
            <button type="button" class="button button1 mt-3" data-bs-toggle="modal" data-bs-target="#all-amenties">
                {{ translate('listing_details.show_all') }} {{ count($listing->amenity_detail) }} {{ translate('listing_details.accessibility_features') }}
            </button>
            {{-- @endif --}}
        </div>
    </div>
    <div class="modal fade all-amenties" id="all-amenties" tabindex="-1" aria-modal="true" role="dialog"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content pb-0">
                <div class="modal-header border-0 ">
                    <h4 class="modal-title mx-auto">{{ translate('listing_details.accessibility_features') }}</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body pb-lg-10 px-lg-10 pt-4 pb-0">
                    <div class="parent-box row g-0 align-items-start">
                        @foreach ($listing->amenity_detail as $amenities)
                            <div class="col-md-12 divider pb-3">
                                <div class="box d-flex gap-3 align-items-center" data-aos="fade">
                                    <img src="{{ asset('website') . '/' . $amenities->image }}" alt=""
                                        height="30px" width="30px"
                                        onerror="this.onerror=null;this.src=`{{ asset('website/images/snowflake.png') }}`;">
                                    <div class="amenity-data ">
                                        <span>{{ $amenities->name ?? '' }}</span>
                                        @isset($amenities->translations->firstWhere('locale', session('locale', 'en'))->description)
                                            <p class="amenties_desc fs-14 text-black-50">
                                                {{ $amenities->translations->firstWhere('locale', session('locale', 'en'))->description ?? '' }}
                                            </p>
                                        @endisset
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
{{-- End Amenities --}}
{{-- Accessibility --}}
@if (isset($listing->accessibilities[0]))
    <div class="col-lg-12 divider listing_data listing_accessibility">
        <div class="amenities-box">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.accessibility') }}</h3>
            <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                @forelse ($listing->accessibilities as $accessibility)
                    <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                        <img src="{{ asset('website/images/square-check.svg') }}" alt="">
                        <span>{{ $accessibility->name }}</span>
                    </div>
                @empty
                    <p>{{ translate('listing_details.no_accessibility') }}</p>
                @endforelse
            </div>
        </div>
    </div>
@endif
{{-- End Accessibility --}}
{{-- Language --}}
@if (isset($listing->includes[0]))
    <div class="col-lg-12 divider listing_data listing_language">
        <div class="amenities-box">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.included_languages') }}</h3>
            <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                @forelse ($listing->tour_languages ?? [] as $tour_language)
                    <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                        <img src="{{ asset('website') . '/' . $tour_language->image }}" style="object-fit: contain;"
                            height="20px" width="25px">
                        <span>{{ $tour_language->country_name }}</span>
                    </div>
                @empty
                    <p>{{ translate('listing_details.no_language_added') }}</p>
                @endforelse
            </div>
        </div>
    </div>
@endif
{{-- End Language --}}
{{-- Key and feature --}}
@if (isset($listing->key_features[0]))
    <div class="col-lg-12 divider listing_data listing_key_feature">
        <div class="key_features">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.key_features') }}</h3>
            <div class="parent-feature parent-box row g-0 gap-3 align-items-start">
                @foreach ($listing->key_features as $key_feature)
                    <div class="box col-md-3" data-aos="fade">
                        <h6 class="fs-16 semi-bold">{{ $key_feature->title }}</h6>
                        <p class="fs-12">{!! $key_feature->description !!}</p>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endif
{{-- End Key and feature --}}
{{-- Include and not include --}}
@if (isset($listing->includes[0]))
    <div class="col-lg-12 divider listing_data listing_include">
        <div class="amenities-box">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.whats_included') }}</h3>
            <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                @forelse ($listing->includes as $include)
                    <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                        <img src="{{ asset('website/images/square-check.svg') }}" height="20px" width="20px">
                        <span>{{ $include->name }}</span>
                    </div>
                @empty
                    <p>{{ translate('listing_details.no_include_added') }}</p>
                @endforelse
            </div>
        </div>
    </div>
@endif
@if (isset($listing->not_includes[0]))
    <div class="col-lg-12 divider listing_data listing_not_include">
        <div class="amenities-box">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.not_included') }}</h3>
            <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                @forelse ($listing->not_includes as $not_include)
                    <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                        <img src="{{ asset('website/images/ticksquare.svg') }}" height="20px" width="20px">
                        <span>{{ $not_include->name }}</span>
                    </div>
                @empty
                    <p>{{ translate('listing_details.no_not_include_added') }}</p>
                @endforelse
            </div>
        </div>
    </div>
@endif
{{-- End Include and not include --}}
{{-- Itinerary --}}
<div class="row py-5 divider listing_data sec2_detail">
    <div class="col-md-10 listing_itinerary">
        <h3 class="pb-3 listing_data_heading fs-22">{{ translate('listing_details.itinerary') }}</h3>
        <div class="tabs">
            @php
                $itineraryData = $listing->itineraries->groupBy('day') ?? [];
            @endphp

            <div class="custom_btns">
                <div class="head_bt ">
                    <ul class="nav nav-tabs gap-4 border-0 pb-5" role="tablist">
                        @foreach ($itineraryData as $day => $itineraries)
                            <li class="nav-item">
                                <a class="nav-link @if ($loop->first) active @endif"
                                    data-bs-toggle="tab" data-bs-target="#day{{ $day }}">{{ translate('listing_details.day') }}
                                    {{ $day }}</a>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <div class="tab-content">
                    @foreach ($itineraryData as $day => $itineraries)
                        <div class="tab-pane fade @if ($loop->first) active show start @elseif($loop->last) end @else mid_days @endif"
                            id="day{{ $day }}">

                            @php
                                $dayDurations = collect($itineraries)
                                    ->flatMap->tour_durations->where('day', $day)
                                    ->first();
                            @endphp

                            <ol class="ps-5">
                                {{-- start --}}
                                @foreach ($itineraries as $itinerary)
                                    <li class="@if (!$loop->last) pb-4 @endif fs-18">

                                        @if (($listing->detail->tour_day_type ?? '') == 'same_day')
                                            @if ($loop->first)
                                                <p class="d-block fs-12 m-0">
                                                    {{ translate('listing_details.start_time') }}: {{ $listing->detail->start_time ?? '' }}
                                                </p>
                                            @endif
                                        @else
                                            @if ($loop->first)
                                                @if (isset($dayDurations))
                                                    <p class="d-block fs-12 m-0">
                                                        {{ translate('listing_details.start_time') }}: {{ $dayDurations['start_time'] }}
                                                    </p>
                                                @endif
                                            @endif
                                        @endif

                                        <span>
                                            {{ $itinerary->title }}
                                            <p class="d-block fs-15 m-0">
                                                {!! $itinerary->description !!}
                                            </p>
                                        </span>

                                        @if (($listing->detail->tour_day_type ?? '') == 'same_day')
                                            @if ($loop->last)
                                                <p class="d-block fs-12 m-0">
                                                    {{ translate('listing_details.end_time') }}: {{ $listing->detail->end_time ?? '' }}
                                                </p>
                                            @endif
                                        @else
                                            @if ($loop->last)
                                                @if (isset($dayDurations))
                                                    <p class="d-block fs-12 m-0">
                                                        {{ translate('listing_details.end_time') }}: {{ $dayDurations['end_time'] }}
                                                    </p>
                                                @endif
                                            @endif
                                        @endif

                                    </li>
                                @endforeach
                                {{-- end --}}

                            </ol>

                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
{{-- End Itinerary --}}
