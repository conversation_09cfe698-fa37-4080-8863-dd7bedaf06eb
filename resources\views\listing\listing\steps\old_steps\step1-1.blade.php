<fieldset>
    <div class="row">
        <div class="col-md-6 fieldset-2" style="text-align:left;">
            <h4 class="">{{ __('Step') }} 1</h4>
            <h2 class="semi_bold">{{ __('step_1_title', ['name' => $category->display_name]) }} </h2>
            <p class="fs-18">
                {{ __('we_ll_inquire_about_the_type_of_name_you_own_afterward_please_provide_details_regarding_its_location_and_the_maximum_number_of', ['name' => $category->display_name]) }}

                @if ($category->id == 2)
                    {{ trans('passengers') }}
                @else
                    {{ trans('guests') }}
                @endif
                {{ trans('it_can_accommodate') }}
            </p>
            <label for="#property">{{ __('type_of_name', ['name' => $category->display_name]) }}*</label>
            <select name="type_id" id="property">
                <option value="" disabled selected>{{ trans('selection_field') }}</option>
                @foreach ($category->listing_types as $type)
                    <option value="{{ $type->id }}" {{ ($listing->type_id ?? '') == $type->id ? 'selected' : '' }}>
                        {{ $type->name }}</option>
                @endforeach
            </select>
            <div class="error text-center text-danger blink-text pb-3"></div>
            <div class="other_parernt pt-4" style='display:{{ ($listing->type ?? '') == 'Other' ? 'block' : 'none' }}'>
                {{-- <label for="others">Others</label> --}}
                <input type="text" name="other_type" placeholder="Type here..."
                    value="{{ $listing->other_type ?? '' }}" id="others" class="form-control no_validate">
            </div>

        </div>
        <div class="col-md-6 fieldset-2">
            <div class="pot_img">
                <img src="{{ $listing_img }}" alt="Property Image" class="img-fluid">
            </div>
        </div>
    </div>
    <div class="progress">
        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0"
            aria-valuemax="100"></div>
    </div>
    <input type="button" name="next" class="next action-button btn button1  mb-4" value="{{ translate('stepper.next') }}" />
</fieldset>


@push('js')
    <script>
        $(document).ready(function() {
            var select_property = $('#property').find('option:selected').text().trim();
            var next_btn = $('fieldset').find('.next');
            $('#property').on('change', function() {
                var selected_property = $(this).find('option:selected').text().trim();
                if (selected_property == 'Other') {
                    console.log(selected_property);
                    $('.other_parernt').show(100);
                    $('.other_parernt').find('input').removeClass('no_validate').prop('disabled', false);
                } else {
                    $('.other_parernt').hide(100);
                    $('.other_parernt').find('input').addClass('no_validate').prop('disabled', true);
                }
            });
        });
    </script>
@endpush
