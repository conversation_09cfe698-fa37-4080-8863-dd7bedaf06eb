<div class="dropdown filter_btn_wrapper hidden d-inline-block">
    {{-- <button class="button " type="button" id="adv_filter" data-bs-toggle="dropdown"
        aria-expanded="false">
        {{ __('website.advance_filter') }}
        <i class="fas fa-filter"></i>
    </button> --}}
    <button class="button pt-1" type="button" id="adv_filter" data-bs-toggle="modal"
        data-bs-target="#filter_category">
        {{-- {{ __('website.advance_filter') }} --}}
        {{-- <i class="fas fa-filter"></i> --}}
        <i class="bi bi-funnel-fill"></i>
        {{-- <img src="{{ asset('website') }}/images/filter-list-icon.png" alt="" height="20px" width="20px"> --}}
    </button>
    {{-- <button class="button " type="button" id="car_filter" data-bs-toggle="modal" data-bs-target="#car_filter_category">
       car
        <i class="fas fa-filter"></i>
    </button> --}}
    {{-- <button class="button " type="button" id="boat_filter" data-bs-toggle="modal" data-bs-target="#boat_filter_category">
        Boat
         <i class="fas fa-filter"></i>
     </button> --}}
    {{-- <button class="button " type="button" id="tour_filter" data-bs-toggle="modal" data-bs-target="#tour_filter_category">
        Tour
         <i class="fas fa-filter"></i>
     </button>                --}}
    <ul class="dropdown-menu adv_filter_menu" aria-labelledby="adv_filter">
        <li class="">
            <div class="bg_white">
                <h6 class="title">{{ translate('home.price_range') }}</h6>
                <input type="text" class="js-range-slider" name="my_range"
                    value="" data-skin="round" data-type="double"
                    data-min="0" data-max="1000" data-grid="false" />
                {{-- <div class="range-input">
                    <input type="range" class="min-range" min="0"
                        max="10000" value="2500" step="1">
                    <input type="range" class="max-range" min="0"
                        max="10000" value="8500" step="1">
                </div> --}}
            </div>
        </li>
        <li class="">
            <div class="bg_white amenity">
                <h6 class="title">{{ translate('home.amenities') }}</h6>
                <select class="form-select select_multi" aria-label="Rating select">
                    {{-- <option selected disabled>Select Amenities</option> --}}
                    <option value="1">{{ translate('home.amenity_1') }}</option>
                    <option value="2">{{ translate('home.amenity_2') }}</option>
                    <option value="3">{{ translate('home.amenity_3') }}</option>
                    <option value="4">{{ translate('home.amenity_4') }}</option>
                    {{-- <option value="3">5</option> --}}
                </select>
            </div>
        </li>
        <li class="">
            <div class="bg_white">
                <h6 class="title">{{ translate('home.rating') }}</h6>
                <select class="form-select " aria-label="Rating select">
                    <option selected disabled>{{ translate('home.select') }}
                        {{ translate('home.rating') }}</option>
                    <option value="1">1+</option>
                    <option value="2">2+</option>
                    <option value="3">3+</option>
                    <option value="4">4+</option>
                    {{-- <option value="3">5</option> --}}
                </select>
            </div>
        </li>
        <li class="">
            <div class="bg_white">
                <h6 class="title">{{ translate('home.select') }}
                    {{ translate('home.rules') }}</h6>
                <select class="form-select" aria-label="Rating select">
                    <option selected disabled>{{ translate('home.select_rules') }}</option>
                    <option value="allowed">{{ translate('home.pets_allowed') }}</option>
                    <option value="not_allowed">{{ translate('home.pets_not_allowed') }}</option>
                    {{-- <option value="3">5</option> --}}
                </select>
            </div>
        </li>
        <li class="">
            <div class="bg_white">
                <h6 class="title">{{ translate('home.bedrooms') }}</h6>
                {{-- <input type="number" class="fomr-control" min="1"
                    max="10000" value="" placeholder="3"> --}}
                <select class="form-select " aria-label="Rating select">
                    <option selected disabled>{{ translate('home.select') }}
                        {{ translate('home.bedrooms') }}</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                    {{-- <option value="3">5</option> --}}
                </select>
            </div>
        </li>
        <li class="">
            <div class="bg_white">
                <h6 class="title">{{ translate('home.beds') }}</h6>
                {{-- <input type="number" class="fomr-control" min="1"
                    max="10000" value="" placeholder="2"> --}}
                <select class="form-select " aria-label="Rating select">
                    <option selected disabled>{{ translate('home.select') }}
                        {{ translate('home.beds') }}</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                    {{-- <option value="3">5</option> --}}
                </select>
            </div>
        </li>
        <li class="">
            <div class="bg_white">
                <h6 class="title">{{ translate('home.bedrooms') }}</h6>
                {{-- <input type="number" class="fomr-control" min="1"
                    max="10000" value="" placeholder="2"> --}}
                <select class="form-select " aria-label="Rating select">
                    <option selected disabled>{{ translate('home.select') }}
                        {{ translate('home.bedrooms') }}</option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    <option value="3">3</option>
                    <option value="4">4</option>
                    <option value="5">5</option>
                    <option value="6">6</option>
                    {{-- <option value="3">5</option> --}}
                </select>
            </div>
        </li>
        <li class="">
            <div class="submit_btn d-flex justify-content-between flex-wrap">
                <button class="button yellow_btn">{{ translate('home.apply') }}</button>
                <button class="button">{{ translate('home.reset') }}</button>
            </div>
        </li>
    </ul>
    <!-- Button trigger modal -->
    {{-- <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal">
        Launch demo modal
    </button> --}}

</div>