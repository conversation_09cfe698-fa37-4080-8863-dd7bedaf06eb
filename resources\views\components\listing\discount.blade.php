@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "discount");
@endphp

<div class="row">
    <div class="col-md-12">
        <div class="inner_section_main_col">
            <div class="main_step_title">
                <h2>{{ $step_data->title ?? ""}}</h2>
            </div>
            <div class="step_description">
                <p>{{ $step_data->sub_title ?? "" }}</p>
            </div>
            <div class="discounts_fields_wrapper">
                @if (count($listing->bookings ?? []) < 3)
                    <div class="single_dicount">
                        <div class="discount_field">
                            <input type="number" name="new_listing_discount" min="0" max="90"
                                value="{{ $listing->discount->new_listing_discount ?? '' }}"
                                class="listing-discount-inp new_listing_discount_field no_validate" pattern="/^-?\d+\.?\d*$/"
                                onKeyPress="if(this.value.length==2) return false;" placeholder="20"
                                @if (!isset($listing->discount) || is_null($listing->discount->new_listing_discount)) disabled @endif>
                            <i class="fas fa-percent"></i>
                        </div>
                        <div class="discount_details_wrapper">
                            <div class="discount_title_description_wrapper">
                                <div class="discount_title">
                                    <h6>{{ translate('stepper.new_listing_discount') }}</h6>
                                </div>
                                <div class="discount_description">
                                    <p>{{ translate('stepper.for_up_to_3_bookings') }}</p>
                                </div>
                            </div>
                            <div class="discount_check">
                                <label for="new_discount"></label>
                                <input class="form-check-input discount_checkbox listing-discount-checkbox "
                                    type="checkbox" id="new_discount" value="new_discount"
                                    {{ isset($listing->discount->new_listing_discount) ? 'checked' : '' }}>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="single_dicount">
                        <div class="discount_field">
                            <input type="number" name="new_listing_discount" min="0" max="100"
                                value="{{ $listing->discount->new_listing_discount ?? '' }}"
                                class="listing-discount-inp new_listing_discount_field no_validate" pattern="/^-?\d+\.?\d*$/"
                                onKeyPress="if(this.value.length==2) return false;" placeholder="20" disabled>
                            <i class="fas fa-percent"></i>
                        </div>
                        <div class="discount_details_wrapper">
                            <div class="discount_title_description_wrapper">
                                <div class="discount_title">
                                    <h6>{{ translate('stepper.new_listing_discount') }}</h6>
                                </div>
                                <div class="discount_description">
                                    <p>{{ translate('stepper.for_up_to_3_bookings') }}</p>
                                    {{-- <p class="text-danger fw-bold">The listing booking limit has been exceeded</p> --}}
                                    <p class="text-danger fw-bold">{{ translate('stepper.offer_disabled') }}</p>
                                </div>
                            </div>
                            <div class="discount_check">
                                <label for="new_discount"></label>
                                <input class="form-check-input discount_checkbox listing-discount-checkbox "
                                    type="checkbox" id="new_discount" value="new_discount" disabled>
                            </div>
                        </div>
                    </div>
                @endif

                {{-- @if ($listing->detail->basis_type === 'Daily') --}}
                @if(($category->id ?? '') != 1)
                    <div class="single_dicount daily_hourly_discount">
                        <div class="discount_field">
                            <input class="no_validate group_discount" type="number" name="weekly_discount" min="0"
                                max="100" pattern="/^-?\d+\.?\d*$/" onKeyPress="if(this.value.length==3) return false;"
                                @if (!isset($listing->discount) || is_null($listing->discount->weekly_discount)) disabled @endif
                                value="{{ $listing->discount->weekly_discount ?? '' }}" placeholder="20">
                            <i class="fas fa-percent"></i>
                        </div>
                        <div class="discount_details_wrapper">
                            <div class="discount_title_description_wrapper">
                                <div class="discount_title">
                                    <h6>{{ translate('stepper.weekly_discount') }}</h6>
                                </div>
                                <div class="discount_description">
                                    <p>{{ $category->id == 4 ? translate('stepper.weekly_discount_description_nights') : translate('stepper.weekly_discount_description_days') }}</p>
                                </div>
                            </div>
                            <div class="discount_check">
                                <label for="weeklyDiscountCheck"></label>
                                <input class="form-check-input enable-group-edit" type="checkbox"
                                    {{ isset($listing->discount->weekly_discount) ? 'checked' : '' }} value=""
                                    id="weeklyDiscountCheck">
                            </div>
                        </div>
                    </div>
                    <div class="single_dicount daily_hourly_discount">
                        <div class="discount_field">
                            <input class="no_validate group_discount percent" type="number" name="monthly_discount"
                                min="0" max="100" pattern="/^-?\d+\.?\d*$/"
                                onKeyPress="if(this.value.length==3) return false;"
                                @if (!isset($listing->discount) || is_null($listing->discount->monthly_discount)) disabled @endif
                                value="{{ $listing->discount->monthly_discount ?? '' }}" placeholder="20">
                            <i class="fas fa-percent"></i>
                        </div>
                        <div class="discount_details_wrapper">
                            <div class="discount_title_description_wrapper">
                                <div class="discount_title">
                                    <h6>{{ translate('stepper.monthly_discount') }}</h6>
                                </div>
                                <div class="discount_description">
                                    <p>{{ $category->id == 4 ? translate('stepper.monthly_discount_description_nights') : translate('stepper.monthly_discount_description_days') }}</p>
                                </div>
                            </div>
                            <div class="discount_check">
                                <label for="monthlyDiscountCheck"></label>
                                <input class="form-check-input enable-group-edit" type="checkbox"
                                    {{ isset($listing->discount->monthly_discount) ? 'checked' : '' }} value=""
                                    id="monthlyDiscountCheck">
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@push('js')
    <script>
        $(document).ready(function() {
            $(document).on('change', '.listing_stepper .add_discounts_step .single_dicount .discount_check input',
                function() {
                    if ($(this).is(':checked')) {
                        // Find the closest .single_discount and enable the input[type=number]
                        $(this).closest('.single_dicount').find('input[type="number"]').removeAttr('disabled');
                        $(this).closest('.single_dicount').find('input[type="number"]').removeClass(
                            'no_validate');
                    } else {
                        // If unchecked, disable the input[type=number]
                        $(this).closest('.single_dicount').find('input[type="number"]').attr('disabled',
                            'disabled');
                        $(this).closest('.single_dicount').find('input[type="number"]').val('');
                        $(this).closest('.single_dicount').find('input[type="number"]').addClass('no_validate');
                    }
                });

                $(document).on('input', '.discounts_fields_wrapper .new_listing_discount_field', function() {
                    var max = parseInt($(this).attr('max'));
                    var value = parseInt($(this).val());

                    if (value > max) {
                        $(this).val(max); // Restrict to the max value
                    }
                });
        });
    </script>
@endpush
