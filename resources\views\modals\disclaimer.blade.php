<div class="modal fade add_listing_disclaimer_modal modal_extra_width" id="add_listing_disclaimer_modal" tabindex="-1"
    role="dialog" aria-labelledby="exampleModalLabel1">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <div class="modal_title_cross_icon_wrapper">
                    <span class="close" data-dismiss="modal">&times;</span>
                    <h1 class="modal-title">{{ $supplier_aggreement->title ?? '' }}</h1>
                </div>
                @if ($errors->any())
                    <ul class="alert alert-danger" id="form-error">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                @endif
                <form action="{{ url('consent-post') }}" method="POST">
                    @csrf
                    <div class="agreement_content">
                        @php($cleanInput = strip_tags($supplier_aggreement->description ?? '', '<p><a><div><br><h1><h2><h3><h4><h5><i><b><h6>'))
                        {!! htmlspecialchars_decode($supplier_aggreement->description ?? '') !!}
                    </div>
                    <div class=" modal_btn text-center">
                        <a href="javascript:void(0)" data-dismiss="modal" class="cancel_btn yellow" id=""
                            style="margin-right: 5px;">{{ translate('dashboard_listing.disagree') }}</a>
                        <button type="submit" class="btn create_btn" id="">{{ translate('dashboard_listing.i_agree') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
