@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "file-upload");
@endphp
<div class="col-md-12">
    <div class="inner_section_main_col">
        <div class="main_step_title">
                <h2>{{ $step_data->title ?? "" }}</h2>
        </div>
        <div class="step_description">
            <p>{{ $step_data->sub_title ?? "" }} <a href="{{ $step_data->url ?? "" }}" class="learn_more_btn" target="_blank">{{ translate('stepper.see_a_list_of_required_documents') }}</a></p>
        </div>
        <div class="drag_drop_photos_wrapper scrollable-section">
            @forelse ($listing->files ?? [] as $file)
                {{-- <div class="drag_drop_photo_single" data-document-id="{{ $file->id }}">
                    @php
                        if (str_contains($file->url, '.pdf')) {
                            $filePath = asset('website') . '/images/pdf_icon.png';
                        } else {
                            $filePath = asset('website') . '/' . $file->url;
                        }
                    @endphp
                    <img alt="Preview 0" src="{{ $filePath }}">
                    <p class="doc_name">{{ $file->name }}</p>
                    <div class="dropdown">
                        <button class="btn btn-secondary" type="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            <i class="fa fa-ellipsis-h" aria-hidden="true"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item delete_btn" data-document-id="{{ $file->id }}"
                                    href="#">{{ translate('stepper.delete') }}</a>
                            </li>
                        </ul>
                    </div>
                </div> --}}
                <div class="drag_drop_photo_single" data-document-id="{{ $file->id }}">
                    @if (str_contains($file->url, '.pdf'))
                        <canvas id="pdfCanvas-{{ $file->id }}" class="pdf-preview"></canvas>
                        <script>
                            document.addEventListener("DOMContentLoaded", function() {
                                renderPDF("{{ asset('website') }}/{{ $file->url }}", "pdfCanvas-{{ $file->id }}");
                            });
                        </script>
                    @else
                        <img alt="Preview {{ $file->id }}" src="{{ asset('website') }}/{{ $file->url }}">
                    @endif
                
                    <div class="dropdown">
                        <button class="btn btn-secondary" type="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            <i class="fa fa-ellipsis-h" aria-hidden="true"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item delete_btn" data-document-id="{{ $file->id }}" href="#">{{ translate('stepper.delete') }}</a>
                            </li>
                        </ul>
                    </div>
                </div>                
            @empty
            @endforelse
            <div class="drag_drop_photo_single add_photo_box">
                <div class="photo_icon">
                    <img src="{{ asset('website') }}/images/document_icon.png" alt="">
                </div>
                <div class="add_photo_btn">
                    <label class="add_photos_lbl" for="add_document_file">{{ translate('stepper.add_documents') }}</label>
                    <label class="plus_icon_lbl" for="add_document_file"><i class="fa fa-plus"
                            aria-hidden="true"></i></label>
                    <input id="add_document_file" class="no_validate" type="file" name="documents[]" multiple
                        accept=".pdf, .jpeg, .jpg, .png">
                </div>
            </div>
        </div>
    </div>
</div>



@push('js')

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.14.305/pdf.min.js"></script>

    <script>
        $(document).ready(function() {
            var documentNumber = $('.listing_stepper .add_documents_step:not(.experiences_documents_step) .drag_drop_photo_single').length;
            if (documentNumber >= 2) {
                $('.listing_stepper .add_documents_step:not(.experiences_documents_step) .next.action-button').prop('disabled', false);
            } else {
                $('.listing_stepper .add_documents_step:not(.experiences_documents_step) .next.action-button').prop('disabled', true);
            }
        });

        function documentValidation() {
            var documentNumber = $(
                '.listing_stepper .add_documents_step:not(.experiences_documents_step) .drag_drop_photo_single').length;
            if (documentNumber >= 2) {
                $('.listing_stepper .add_documents_step:not(.experiences_documents_step)').find('.next.action-button').prop('disabled',
                    false);
            } else {
                $('.listing_stepper .add_documents_step:not(.experiences_documents_step)').find('.next.action-button').prop('disabled',
                    true);
            }
        }

        // Image upload
        $('#add_document_file').on('change', function(event) {
            let listing_id = $('input[name="listing_id"]').val();
            const files = event.target.files;
            if (files.length === 0) {
                alert(@json(translate('stepper.no_files_selected')));
                return;
            }
            const listing_images = new FormData();
            for (let i = 0; i < files.length; i++) {
                listing_images.append("media[]", files[i]);
            }
            listing_images.append("listing_id", listing_id);
            listing_images.append("type", "file");


            let uploadedCount = 0;
            const totalFiles = files.length;

            Swal.fire({
                title: @json(translate('stepper.uploading_documents')),
                html: `@json(translate('stepper.uploading')) <b>0</b> out of ${totalFiles}`,
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                },
            });


            $.ajax({
                url: "{{ route('listingMedia') }}",
                type: "POST",
                headers: {
                    "X-CSRF-TOKEN": "{{ csrf_token() }}",
                },
                data: listing_images,
                contentType: false,
                processData: false,

                xhr: function () {
                    let xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener("progress", function (evt) {
                        if (evt.lengthComputable && Swal.isVisible()) {
                            let percentComplete = Math.round((evt.loaded / evt.total) * 100);
                            uploadedCount = Math.round((percentComplete / 100) * totalFiles); // Estimate uploaded files
                            uploadedCount = Math.min(uploadedCount, totalFiles); // Ensure it doesn't exceed totalFiles

                            Swal.update({
                                html: `@json(translate('stepper.uploading')) <b>${uploadedCount}</b> out of ${totalFiles}...`,
                            });
                        }
                    }, false);
                    return xhr;
                },

                success: function(response) {

                    if (response.status == true) {
                        $.each(response.data, function(index, file) {

                            let filePreview = '';

                            if (file.url.toLowerCase().endsWith('.pdf')) {
                                filePreview =
                                    // `<img alt="PDF Preview ${index}" src="{{ asset('website') }}/images/pdf_icon.png">`;
                                    `<canvas id="pdfCanvas-${file.id}" class="pdf-preview"></canvas>`;
                            } else {
                                filePreview =
                                    `<img alt="Preview ${index}" src="{{ asset('website') }}/${file.url}">`;
                            }

                            // For BACKUP
                            // <p class="doc_name">${file.name}</p>
                            
                            const newDiv = $(
                                `<div class="drag_drop_photo_single" data-document-id="${file.id}">
                                ${filePreview}
                                
                                <div class="dropdown">
                                    <button class="btn btn-secondary" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fa fa-ellipsis-h" aria-hidden="true"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item delete_btn" data-document-id="${file.id}" href="#">@json(translate('stepper.delete'))</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>`
                            );
                            const addPhotoBox = $(
                                '.listing_stepper .add_documents_step .drag_drop_photo_single.add_photo_box'
                            );
                            newDiv.insertBefore(addPhotoBox);

                            if (file.url.toLowerCase().endsWith('.pdf')) {
                                renderPDF("{{ asset('website') }}/" + file.url, `pdfCanvas-${file.id}`);
                            }

                        });
                        documentValidation();

                        Swal.fire({
                            title: @json(translate('stepper.success')),
                            text: @json(translate('stepper.your_files_have_been_uploaded')),
                            icon: "success",
                            timer: 1500,
                            showConfirmButton: false
                        });

                        $('#add_document_file').val('');
                    }else{
                        Swal.fire({
                            title: @json(translate('stepper.error')),
                            text: response.message,
                            icon: "error"
                        });

                        $('#add_document_file').val('');

                        documentValidation();

                        return;
                    }
                },
               
                error: function(xhr) {
                    alert(@json(translate('stepper.error_uploading_images')));
                    console.error(xhr.responseText);
                },
            });
        });
        // Image upload end

        // PDF Rendering Script

        function renderPDF(pdfURL, canvasID) {
            pdfjsLib.getDocument(pdfURL).promise.then(function(pdf) {
                pdf.getPage(1).then(function(page) {
                    const scale = 1.5;
                    const viewport = page.getViewport({ scale });
                    const canvas = document.getElementById(canvasID);
                    const context = canvas.getContext("2d");

                    canvas.width = viewport.width;
                    canvas.height = viewport.height;
                    canvas.style.display = "block";

                    const renderContext = {
                        canvasContext: context,
                        viewport: viewport
                    };
                    page.render(renderContext);
                });
            }).catch(error => {
                console.error("Error loading PDF: ", error);
            });
        }

        // Delete image
        $(document).on('click',
            '.listing_stepper .add_documents_step .drag_drop_photos_wrapper .drag_drop_photo_single .dropdown-menu .delete_btn',
            function() {
                let listing_image = $(this);
                let documentId = listing_image.data('document-id');
                var listing_id = $("input[name='listing_id']").val();

                Swal.fire({
                    title: @json(translate('stepper.are_you_sure')),
                    // text: "You won't be able to revert this!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: @json(translate('stepper.yes_delete'))
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('listing_image_delete') }}",
                            type: "DELETE",
                            headers: {
                                "X-CSRF-TOKEN": "{{ csrf_token() }}",
                            },
                            data: {
                                image_id: documentId,
                                listing_id
                            },
                            success: function(response) {
                                if (response.status == true) {
                                    listing_image.closest('.drag_drop_photo_single').remove();
                                    var documentNumber = $(
                                        '.listing_stepper .add_documents_step:not(.experiences_documents_step) .drag_drop_photo_single'
                                    ).length;
                                    if (documentNumber >= 6) {
                                        $('.listing_stepper .add_documents_step:not(.experiences_documents_step) .next.action-button')
                                            .prop('disabled',
                                                false);
                                    } else {
                                        $('.listing_stepper .add_documents_step:not(.experiences_documents_step) .next.action-button')
                                            .prop('disabled',
                                                true);
                                    }
                                    Swal.fire({
                                        title: @json(translate('stepper.deleted')),
                                        text: @json(translate('stepper.your_file_has_been_deleted')),
                                        icon: "success"
                                    });
                                    documentValidation();
                                }
                            }
                        });
                    }
                });
            });
        // Delete image end
    </script>
@endpush
