@push('css')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="{{ asset('plugins/components/dropify/dist/css/dropify.min.css') }}">
@endpush

<div class="form-group row {{ $errors->has('name') ? 'has-error' : '' }}">
    <label for="amenImg" class="col-md-12 ">{{ translate('content_management_system.upload_type_icon') }}</label>
    <div class="col-md-6 form_field_padding">
        <input type="file" id="image" name="image" class="dropify" accept="image/*" data-height="150"
            data-default-file="{{ isset($listingType->image) ? asset('website') . '/' . $listingType->image : '' }}" />
    </div>
</div>
<div class="create_amenity_option_sec">
    <ul class="nav nav-pills">
        <li class="active"><a data-toggle="pill" href="#english">{{ translate('content_management_system.in_eng') }}</a></li>
        <li><a data-toggle="pill" href="#spanish">{{ translate('content_management_system.in_spanish') }}</a></li>
    </ul>
</div>

<div class="tab-content">
    <div id="english" class="tab-pane fade in active">
        <div class="form-group {{ $errors->has('en.name') ? 'has-error' : '' }}">
            <label for="name" class="col-md-12">{{ translate('content_management_system.type_name') }}</label>
            <div class="col-md-12 form_field_padding">
                <input class="form-control" name="en[name]" type="text" id="en_name"
                    placeholder="{{ translate('content_management_system.enter') }} {{ translate('content_management_system.type') }} {{ translate('content_management_system.in_eng') }}"
                    value="{{ old('en.name', $listingType?->translate('en')->name ?? '') }}">
                {!! $errors->first('name', '<p class="help-block">:message</p>') !!}
            </div>
        </div>
    </div>
    <div id="spanish" class="tab-pane fade">
        <div class="form-group {{ $errors->has('es.name') ? 'has-error' : '' }}">
            <label for="es-name" class="col-md-12">{{ translate('content_management_system.type_name')  }}</label>
            <div class="col-md-12 form_field_padding">
                <input class="form-control" name="es[name]" type="text" id="es-name"
                    placeholder="{{ translate('content_management_system.enter') }} {{ translate('content_management_system.type') }} {{ translate('content_management_system.in_spanish') }}"
                    value="{{ old('es.name', $listingType?->translate('es')->name ?? '') }}">
                {!! $errors->first('name', '<p class="help-block">:message</p>') !!}
            </div>
        </div>
    </div>
</div>
<div class="form-group {{ $errors->has('status') ? 'has-error' : '' }}">
    <label for="status" class="col-md-12">{{ translate('content_management_system.status')  }}</label>
    <div class="col-md-12 form_field_padding">
        <select class="form-control" name="status" id="status">
            <option value="" disabled selected>Select Status</option>
            <option value="1" {{ ($listingType->status ?? '') == 1 ? 'selected' : 'selected' }}>{{ translate('content_management_system.active')  }}</option>
            <option value="0" {{ ($listingType->status ?? '') == 0 ? 'selected' : '' }}>{{ translate('content_management_system.inactive')  }}</option>
        </select>
        {!! $errors->first('status', '<p class="help-block">:message</p>') !!}
    </div>
</div>
<div class="form-group">
    <div class="col-md-4">
        <input class="btn btn_yellow" type="submit" value="{{ $submitButtonText ?? 'Create' }}">
    </div>
</div>
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>
    <script src="{{ asset('plugins/components/dropify/dist/js/dropify.min.js') }}"></script>

    <script>
        $(document).ready(function() {
            $(".multi_select").select2({
                closeOnSelect: false,
                placeholder: "Select categories",
                // allowHtml: true,
                allowClear: true,
                tags: true,
                multiple: true
            });
        });
    </script>

    <script>
        $('.dropify').dropify();
    </script>
@endpush
