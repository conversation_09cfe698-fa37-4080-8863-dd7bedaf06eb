@foreach ($bookings as $booking)
    <div class="col-xl-6 col-md-12 booking_cards_parent">
        <div class="main_cart position-relative booked_card flex-md-row flex-column"
            data-booking-id="{{ $booking->booking_number ?? '-' }}">
            {{-- onclick="window.location.href='{{ route('my-booking-detail', ['ids' => $booking->ids, 'booking_number' => $booking->booking_number]) }}';" --}}

            <div class="property-image">
                <a class="-inline-block h-100 w-100"
                    href="{{ route('my-booking-detail', ['ids' => $booking->ids, 'booking_number' => $booking->booking_number]) }}">
                    <img class="img-fluid"
                        src="{{ asset('website') . '/' . ($booking->listing->thumbnail_image->url ?? '') }}"
                        onerror="this.onerror=null;this.src=`{{ asset('website/images/plcaeholderListingImg.png') }}`;"
                        alt="listing image">
                </a>
            </div>
            <div class="parent_flag position-absolute">
                <a class="flag" href="{{ url('detail') . '/' . ($booking->listing->ids ?? '') . '/' . ($booking->listing->slug ?? '') }}"><i class="fas fa-eye"></i></a>
                <button class="flag report-btn" data-bs-toggle="modal" data-bs-target="#report_modal"
                    data-booking-id="{{ $booking->ids }}">
                    <i class="fas fa-flag"></i>
                </button>
                {{-- <button class="cancel report-btn" data-booking-id="{{ $booking->ids }}">
                    <i class="fas fa-times"></i>
                </button> --}}
            </div>

            <div class="cart_text position-relative">
                <a
                    href="{{ route('my-booking-detail', ['ids' => $booking->ids, 'booking_number' => $booking->booking_number]) }}">

                    <div class="property-asset">
                        <div class="fs-12">
                            <p class="mb-1 light-bold"> {{ translate('user_bookings.host_name') }}</p>
                            <p class="mb-1 host_name">{{ $booking->listing->user->first_name ?? '' }}
                                {{ $booking->listing->user->last_name ?? '' }}</p>
                        </div>
                        <div class="fs-12">
                            <p class="mb-1 light-bold">{{ translate('user_bookings.booking_id') }} </p>
                            <p class="mb-1">{{ $booking->booking_number ?? '' }} </p>
                        </div>
                    </div>
                    <div class="property-asset">
                        <div class="fs-12">
                            <p class="mb-1 light-bold"> {{ translate('user_bookings.from') }}</p>
                            <p class="mb-1">
                                {{ date(config('constant.date_format'), strtotime($booking->check_in ?? '-')) }}</p>
                        </div>
                        <div class="fs-12">
                            <p class="mb-1 light-bold"> {{ translate('user_bookings.to') }}</p>
                            <p class="mb-1">
                                {{ date(config('constant.date_format'), strtotime($booking->check_out ?? $booking->check_in)) }}
                            </p>
                        </div>
                    </div>
                </a>
                <div class="property-asset2 pb-0">
                    {{-- <a
                        href="{{ route('my-booking-detail', ['ids' => $booking->ids, 'booking_number' => $booking->booking_number]) }}"> --}}

                        <div class="fs-12">
                            <p class="mb-1 light-bold"> {{ translate('user_bookings.property_name') }}</p>
                            <p class="listing_name">{{ $booking->listing->name ?? '-' }}</p>
                        </div>
                    {{-- </a> --}}
                    <a
                        href="{{ route('my-booking-detail', ['ids' => $booking->ids, 'booking_number' => $booking->booking_number]) }}">

                        <div class="fs-12">
                            <p class="mb-1 light-bold"> {{ translate('user_bookings.price') }}</p>
                            <p class="mb-1"> $ {{ $booking->total_usd_amount ?? '-' }}</p>
                        </div>
                    </a>
                    <div class="fs-12">
                        <p class="mb-1 light-bold"> {{ translate('user_bookings.review') }} </p>
                        <p class="mb-1">
                            @if ($booking->is_reviewed == 1)
                                @if (!empty($booking->review()->onlyTrashed()->first()))
                                    <span class="text-danger"> Deleted </span>
                                @else
                                    <i class="fas fa-star"></i> {{ $booking->review->rating ?? '0' }}.0
                                    @if (!isset($booking->review->reply) && $booking->review->is_updated == 0)
                                        <button type="button" data-review-id="{{ $booking?->review?->id ?? '' }}"
                                            class="edit-review-btn trans_btn" data-bs-toggle="modal"
                                            data-bs-target="#review_edit_modal">
                                            Edit
                                        </button>
                                    @endif
                                @endif
                            @elseif ($booking->status == 7)
                                <button type="button" class="text-danger trans_btn">Cancelled</button>
                            @elseif ($booking->status != 0)
                                <button type="button" data-booking-id="{{ $booking->ids }}"
                                    data-lisitng-id="{{ $booking?->listing?->ids ?? '' }}"
                                    class="review-not-btn review-btn trans_btn" data-bs-toggle="modal"
                                    data-bs-target="#review">
                                    Review Now
                                </button>
                            @else
                                <button type="button" class="text-primary trans_btn">Pending</button>
                            @endif
                        </p>
                    </div>
                </div>

            </div>
        </div>
    </div>
@endforeach
