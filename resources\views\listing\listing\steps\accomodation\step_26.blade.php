@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "review");
@endphp
<fieldset class="review_details_step text-start" id="">
    <div class="inner_section_fieldset h_100">
        {{-- <iframe style="width: 100%; height: 100vh;" src="{{route('detail_slide_iframe')}}" frameborder="0"></iframe> --}}

    </div>
    <input type="button" name="submit" data-status="0" class="save_and_exit_btn action-button btn button1"
        value="{{ translate('stepper.publish') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <script>
        $(document).ready(function() {
            $('.review_details_step .inner_section_fieldset').html(
                `<div id="list_preview_loader"> <div class="inner_section_col"> 
                    <div class="main_step_title"> 
                        <h2>{{ $step_data->title ?? "" }}</h2> 
                    </div> 
                    <div class="step_description"> 
                        <p>
                            {{ $step_data->sub_title ?? "" }}
                        </p> 
                    </div> 
                </div> 
                <div class="load"> <img src="{{ asset('website') }}/images/luxustar-loader.gif" alt="Loader"> </div> <p>{{ translate('stepper.generating_preview') }}</p> </div>`
            );
            var id = $('input[name="listing_id"]').val();
            var category = 4;
            $.ajax({
                type: 'GET',
                url: `{{ url('detail-slide') }}/` + id + `/` + category,
                data: {
                    id: id,
                    category: category
                },
                success: function(data) {
                    setTimeout(() => {
                        if (data) {
                            $(".review_details_step .inner_section_fieldset").html(data);
                        } else {
                            console.log('Error: No view received.');
                        }
                    }, 4500);
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    swal("Error!", @json(translate('stepper.please_select_a_valid_view')));
                }
            });
        });

        $('.final_next_btn').click(function() {
            $('.review_details_step .inner_section_fieldset').html(
                `<div id="list_preview_loader"> 
                    <div class="inner_section_col"> 
                        <div class="main_step_title"> 
                            <h2>{{ $step_data->title ?? "" }}</h2> 
                        </div> 
                        <div class="step_description"> 
                            <p>{{ translate('stepper.here_what_your_guests_will_see') }}</p> 
                        </div> 
                    </div> <div class="load"> <img src="{{ asset('website') }}/images/luxustar-loader.gif" alt="Loader"> </div> <p>{{ translate('stepper.generating_preview') }}</p> </div>`
            );
            var id = $('input[name="listing_id"]').val();
            var category = 4;
            $.ajax({
                type: 'GET',
                url: `{{ url('detail-slide') }}/` + id + `/` + category,
                data: {
                    id: id,
                    category: category
                },
                success: function(data) {
                    setTimeout(() => {
                        if (data) {
                            $(".review_details_step .inner_section_fieldset").html(data);
                        } else {
                            console.log('Error: No view received.');
                        }
                    }, 4500);
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    swal("Error!", "Please select a valid view!");
                }
            });
        });
    </script>
@endpush
