<fieldset class="select_categories_step min_stay_requirement_step">
    <div class="inner_section_fieldset h_100">

        <x-listing.availability title="Control How Far Ahead Renters Can Book"
         :listing="$listing ?? null" :category="$category" />

    </div>

    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>

@push('js')
    {{-- <script>
        $(document).on('keyup', '.categories_search', function() {
            let input = $(this).val().toLowerCase();

            $('.inner_section_available_main_col .single_category_col').each(function() {
                if ($(this).find('.category_title h5').text().toLowerCase().includes(input)) {
                    $(this).css('display', 'block');
                } else {
                    $(this).css('display', 'none');
                }
            });
        });
    </script> --}}
@endpush
