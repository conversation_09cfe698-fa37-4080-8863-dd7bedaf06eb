@extends('layouts.master')

@push('css')
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <div class="container-fluid">
        <!-- .row -->
        <div class="row">
            <div class="col-sm-12">
                <div class="white-box">
                    <h3 class="box-title pull-left"> {{ translate('dashboard_help_center.help_center_articles') }}
                        {{-- {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }} --}}
                        </h3>
                    @can('add-' . str_slug('HelpCenter'))
                        <a class="btn  pull-right btn_yellow" href="{{ url('/helpCenter/help-center/create') }}">
                            {{-- <i class="icon-plus"></i>  --}}
                            {{ translate('dashboard_help_center.add_category') }}
                            {{-- {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }} --}}
                            </a>
                    @endcan
                    <div class="clearfix"></div>
                    <hr>
                    <div class="table-responsive">
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>{{ translate('dashboard_help_center.title') }}</th>
                                    <th>{{ translate('dashboard_help_center.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($helpcenter as $item)
                                    <tr>
                                        <td>{{ $loop->iteration ?? $item->id }}</td>
                                        <td><a href="{{ url('/helpCenter/help-center/' . $item->id . '/edit') }}">{{ $item->title }}</a></td>
                                        <td class="form_btn ">
                                            <div class="dropdown">
                                                <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                </button>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    @can('view-' . str_slug('HelpCenter'))
                                                        <a href="#!" class="view_detail_btn" data-help-id="{{ $item->id }}"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}">
                                                            <button class="dropdown-item">
                                                                {{ translate('dashboard_help_center.view') }}
                                                            </button>
                                                        </a>
                                                        {{-- <a href="{{ url('/helpCenter/help-center/' . $item->id) }}"
                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}">
                                                            <button class="dropdown-item">
                                                                View
                                                            </button>
                                                        </a> --}}
                                                    @endcan
                                                    @can('edit-' . str_slug('HelpCenter'))
                                                        <a href="{{ url('/helpCenter/help-center/' . $item->id . '/edit') }}"
                                                            title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}">
                                                            <button class="dropdown-item">
                                                                {{ translate('dashboard_help_center.edit') }}
                                                            </button>
                                                        </a>
                                                    @endcan
                                                    @can('delete-' . str_slug('HelpCenter'))
                                                        <form method="POST" id="delete-form-{{ $item->id }}"
                                                            action="{{ url('/helpCenter/help-center' . '/' . $item->id) }}"
                                                            accept-charset="UTF-8" style="display:inline">
                                                            {{ method_field('DELETE') }}
                                                            {{ csrf_field() }}
                                                            <button type="button" class="dropdown-item delete-btn"
                                                                data-id="{{ $item->id }}"
                                                                title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}">
                                                                {{ translate('dashboard_help_center.delete') }}
                                                            </button>
                                                        </form>
                                                    @endcan
                                                </div>
                                            </div>
                                        </td>


                                        {{-- <td>
                                            @can('view-' . str_slug('HelpCenter'))
                                                <a href="{{ url('/helpCenter/help-center/' . $item->id) }}"
                                                    title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}">
                                                    <button class="btn btn-info btn-sm">
                                                        <i class="fa fa-eye" aria-hidden="true"></i> View
                                                    </button>
                                                </a>
                                            @endcan

                                            @can('edit-' . str_slug('HelpCenter'))
                                                <a href="{{ url('/helpCenter/help-center/' . $item->id . '/edit') }}"
                                                    title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}">
                                                    <button class="btn btn-primary btn-sm">
                                                        <i class="fa fa-pencil-square-o" aria-hidden="true"></i> Edit
                                                    </button>
                                                </a>
                                            @endcan

                                            @can('delete-' . str_slug('HelpCenter'))
                                                <form method="POST"
                                                    action="{{ url('/helpCenter/help-center' . '/' . $item->id) }}"
                                                    accept-charset="UTF-8" style="display:inline">
                                                    {{ method_field('DELETE') }}
                                                    {{ csrf_field() }}
                                                    <button type="submit" class="btn btn-danger btn-sm"
                                                        title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'HelpCenter') }}"
                                                        onclick="return confirm(&quot;Confirm delete?&quot;)"><i
                                                            class="fa fa-trash-o" aria-hidden="true"></i> Delete
                                                    </button>
                                                </form>
                                            @endcan


                                        </td> --}}


                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        <div class="pagination-wrapper"> {!! $helpcenter->appends(['search' => Request::get('search')])->render() !!} </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <section class="report helpCenter popup">
        <div class="modal fade" id="helpCenter" tabindex="-1" role="dialog" aria-labelledby="amenityDetail">

        </div>
    </section>


    {{-- Modal --}}
    {{-- <section class="report helpCenter popup">
        <div class="modal fade" id="helpCenter" tabindex="-1" role="dialog" aria-labelledby="amenityDetail">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="title">{{ __('help_detail') }}</h1>
                        <div class="form_field_padding">
                            <div class="mb-3 mod_cust_text">
                                <p id="title"><span>{{ __('title') }}:</span>
                                    <span class="info">
                                        Testing Help Center
                                    </span>
                                </p>
                                <p id="short_description"><span>{{ __('description') }}:</span>
                                    <span class="info">
                                        This is testing helpcenter description
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </section> --}}
@endsection

@push('js')
    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <!-- start - This is for export functionality only -->
    <!-- end - This is for export functionality only -->
    <script>
        $(function() {
            $('#myTable').DataTable({
                'aoColumnDefs': [{
                    'bSortable': false,
                    'aTargets': [-1] /* 1st one, start by the right */
                }]
            });
        });


        $('.view_detail_btn').click(function () {
            var id = $(this).data('help-id');
                $.ajax({
                    type: 'GET',
                    url: '{{ url('view-help-center') }}/' + id,
                    // data: { id: id },
                    success: function(data) {
                        if (data) {
                            $("#helpCenter").html(data);
                            $('#helpCenter').modal('show');
                        } else {
                            console.log('Error: No view received.');
                        }
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                        swal(@json('dashboard_help_center.error'), @json('dashboard_help_center.please_select_a_valid_view'));
                    }
                });
            });


    </script>
@endpush
