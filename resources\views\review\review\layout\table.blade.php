<div class="table-responsive">
    <table class="table">
        <thead>
            <tr>
                {{-- <th>#</th> --}}
                @if (!auth()->user()->hasRole('service'))
                    <th>{{ translate('dashboard_reviews.booking_id') }}</th>
                    <th>{{ translate('dashboard_reviews.customer_name') }}</th>
                @else
                    <th>{{ translate('dashboard_reviews.profile_image') }}</th>
                    <th>{{ translate('dashboard_reviews.customer_name') }}</th>
                @endif
                <th>{{ translate('dashboard_reviews.listing_name') }}</th>
                @if (!auth()->user()->hasRole('service'))
                    <th>{{ translate('dashboard_reviews.service_provider_name') }}</th>
                @else
                    <th>{{ translate('dashboard_reviews.internal_name') }}</th>
                @endif
                <th>{{ translate('dashboard_reviews.rating') }}</th>
                <th>{{ translate('dashboard_reviews.date') }}</th>
                <th class="d-none"></th>
                {{-- @if (!auth()->user()->hasRole('service')) --}}
                <th>{{ translate('dashboard_reviews.reply') }}</th>
                {{-- @endif --}}
                <th>{{ translate('dashboard_reviews.actions') }}</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($review as $item)
                <tr>
                    {{-- <td class="d-none">{{ $item->id ?? '-' }}</td> --}}
                    @if (!auth()->user()->hasRole('service'))
                        <td>{{ $item->booking->booking_number }}</td>
                    @else
                        <td><img class="review_profile_image" src="{{ asset('/website') . '/'  . $item->user->avatar ?? '' }}" alt="" width="50" height="50"
                                onerror="this.onerror=null;this.src=`{{ asset('website/images/favicon_white_bg.svg') }}`;">
                        </td>
                    @endif
                    <td><a href="{{ url('account-setting') }}" target="_blank">
                            {{ $item->user->first_name ?? '' }} {{ $item->user->last_name ?? '' }}
                        </a>
                    </td>
                    <td>{{ $item->listing->name ?? '-' }}</td>
                    @if (!auth()->user()->hasRole('service'))
                        <td>{{ $item->provider->first_name ?? '' }}
                            {{ $item->provider->last_name ?? '' }}
                        </td>
                    @else
                        <td>{{ $item->listing->internal_name ?? '-' }}</td>
                    @endif
                    {{-- <td>{{ $item->created_at->format("m/y/d") }}</td> --}}
                    <td>{{ $item->rating }}.0
                        {{-- <span><i class="fa-solid fa-star" style="color: #FFCE32;"></i></span> --}}
                    </td>
                    <td>{{ $item->created_at->format(config('constant.date_format')) }}</td>
                    {{-- @if (!auth()->user()->hasRole('service')) --}}
                    @if ($item->provider_id == auth()->user()->id)
                        <td>
                            @if ($item->reply_count > 0)
                                <i class="fas fa-check-circle fs-medium yellow-color"></i>
                            @else
                                <i class="fas fa-times-circle fs-medium text-danger"></i>
                            @endif
                        </td>
                    @else
                        <td class="text-center">
                            <span class="reply_not_allowed">-</span>
                        </td>
                    @endif
                    <td class="d-none">{{ $item->booking->customer->name ?? '' }}</td>
                    {{-- <td>
                    <p class="limit">{{ $item->comment ?? '-' }}</p>
                </td> --}}
                    <td class="form_btn ">
                        <div class="dropdown">
                            <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                            </button>
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                @can('view-' . str_slug('Review'))
                                    {{-- <a href="#" data-toggle="modal" data-target="#review"
                                        title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}">
                                        <button class="dropdown-item">
                                            {{ trans('view') }}
                                        </button>
                                    </a> --}}
                                    {{-- <a href="{{ url('/review/review/' . $item->id) }}" --}}
                                    <a class="view_review_btn" href="#!" data-toggle="modal"
                                        data-target="#view_review_modal" data-lisitng-id="{{ $item->listing->id ?? '' }}"
                                        data-booking-id="{{ $item->booking->id ?? '' }}"
                                        data-review-id="{{ $item->id ?? '' }}"
                                        title="{{ translate('dashboard_reviews.view') }} {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}">
                                        <button class="dropdown-item">
                                            @if (auth()->user()->hasRole('service'))
                                                {{ translate('dashboard_reviews.view_details') }}
                                            @else
                                                {{ translate('dashboard_reviews.view_review') }}
                                            @endif

                                        </button>
                                    </a>
                                @endcan
                                @can('edit-' . str_slug('Review'))
                                    <a class="edit_review_btn dropdown-item" href="#!" data-toggle="modal"
                                        data-target="#edit_review_modal" data-review-id="{{ $item->id ?? '' }}"
                                        title="{{ translate('dashboard_reviews.view') }} {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}">
                                        {{ translate('dashboard_reviews.edit_review') }}
                                    </a>
                                @endcan
                                @can('delete-' . str_slug('Review'))
                                    <form method="POST" action="{{ url('/review/review' . '/' . $item->id) }}"
                                        id="delete-form-{{ $item->id }}" accept-charset="UTF-8" style="display:inline">
                                        {{ method_field('DELETE') }}
                                        {{ csrf_field() }}
                                        <button type="submit" class="dropdown-item delete_btn text-danger"
                                            data-id="{{ $item->id }}"
                                            title="{{ translate('dashboard_reviews.delete') }} {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}">
                                            {{-- <i class="fa fa-trash-o" aria-hidden="true"></i>  --}}
                                            {{ translate('dashboard_reviews.delete_review') }}
                                        </button>
                                    </form>
                                @endcan
                                {{-- @if (auth()->user()->hasRole('service')) --}}
                                @if ($item->provider_id == auth()->user()->id)
                                    @if (($item->reply->is_updated ?? null) != 1)
                                        <a class="reply_review dropdown-item" href="#!" data-toggle="modal"
                                            data-target="#view_review_modal" data-lisitng-id="{{ $item->listing->id ?? '' }}"
                                            data-booking-id="{{ $item->booking->id ?? '' }}"
                                            data-review-id="{{ $item->id ?? '' }}" data-reply-mode="true" title="{{ translate('dashboard_reviews.reply') }}">
                                            @if ($item->reply_count > 0 && auth()->user()->hasRole('service'))
                                                {{ translate('dashboard_reviews.edit_reply') }}
                                            @else
                                                {{ translate('dashboard_reviews.reply') }}
                                            @endif
                                        </a>
                                    @endif

                                    <a class="report_review_btn dropdown-item" href="#!" data-toggle="modal" data-target="#report_review"
                                                                    data-type="review"
                                        data-review-id="{{ $item->id ?? '' }}" title="{{ translate('dashboard_reviews.report') }}">
                                        {{ translate('dashboard_reviews.report') }}
                                    </a>
                                @endif
                            </div>
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="8">
                        <p class="text-center"> {{ translate('dashboard_reviews.no_review_found') }}</p>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <div class="pagination-info">
            {{ translate('dashboard_reviews.showing_entries', ['from' => $review->firstItem(), 'to' => $review->lastItem(), 'total' => $review->total()]) }}
        </div>
        <div class="pagination-wrapper"> {!! $review->appends(['search' => Request::get('search')])->render() !!} </div>
    </div>
</div>
