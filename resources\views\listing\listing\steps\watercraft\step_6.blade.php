@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', "detail-step");
@endphp
<fieldset class="basic_accommodation_step basic_watercraft_step">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title ?? "" }}</h2>
                    </div>
                    @isset($record->sub_title)
                        <div class="step_description">
                            <p>{{ $step_data->sub_title ?? "" }}</p>
                        </div>
                        
                    @endisset
                    <div class="space_details_wrapper fields_wrapper scrollable-section">
                        <div class="space_detail_single">
                            <div class="space_detail_title">
                                <label for="capacity">Capacity</label>
                            </div>
                            <div class="space_detail_quantity_wrapper">
                                <button class="btn minus_btn" type="button"
                                    onclick="this.parentNode.querySelector('input[type=number').stepDown()">
                                    <i class="fa fa-minus" aria-hidden="true"></i>
                                </button>
                                <input type="number" min="1" value="{{ $listing->detail->capacity ?? '1' }}"
                                    class="form-control file" name="capacity" id="capacity" required />
                                <button class="btn plus_btn" type="button"
                                    onclick="this.parentNode.querySelector('input[type=number').stepUp()">
                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>
                        <div class="space_detail_single">
                            <div class="space_detail_title">
                                <label for="boat_length">{{ translate('stepper.length') }}
                                    {{-- <span class="fs-10"> (Optional)</span> --}}
                                </label>
                            </div>
                            <div
                                class="space_detail_quantity_wrapper watercraft_boat_length_field justify-content-between align-items-center white-box m-0 py-0 gap-1">
                                <input class="no_validate" value="{{ $listing->detail->boat_length ?? '' }}"
                                    type="text" name="boat_length" id="boat_length"
                                    pattern="[^@]+@[^@]+\.[a-zA-Z]{2,6}" placeholder="----">
                                <span class="semi-bold">ft</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script>
        $(document).ready(function() {
            $("#boat_length").on("input", function() {
                $(this).val($(this).val().replace(/\D/g, ""));
            });
        });
    </script>
@endpush
