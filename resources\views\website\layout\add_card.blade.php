<div class="modal fade " id="add_card" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered ">
        <div class="modal-content">
            <div class="modal-header border-0 ">
                <h4 class="modal-title mx-auto">{{ translate('user_account_setting.add_card') }}</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pb-lg-10 px-lg-10 px-2 pt-md-4">
                <div class="card-img-parent d-flex gap-1 align-items-center flex-wrap pb-3">
                    <div class="cards-img">
                        <img src="{{ asset('website/images/master_card_new.png') }}" alt="Master Card" height="40px" width="40px"
                            class="img-fluid">
                    </div>
                    <div class="cards-img">
                        <img src="{{ asset('website/images/visa_new.png') }}" alt="Visa Card" height="40px" width="40px"
                            class="img-fluid">
                    </div>
                    <div class="cards-img">
                        <img src="{{ asset('website/images/american_express_new.png') }}" height="40px" width="40px"
                            alt="American Express" class="img-fluid">
                    </div>
                    <div class="cards-img">
                        <img src="{{ asset('website/images/discover_new.png') }}" alt="Dicover Card" height="40px" width="40px"
                            class="img-fluid">
                    </div>
                    <div class="cards-img">
                        <img src="{{ asset('website/images/union_pay.png') }}" alt="UnionPay Card" height="40px" width="40px"
                            class="img-fluid">
                    </div>

                </div>

                {{-- card --}}
                <div id="collectCardNumber" class="empty-div"></div>
                {{-- <div id="collectCvv" class="empty-div"></div> --}}
                <div id="collectExpiryDate" class="empty-div"></div>
                <div id="collectCardholderName" class="empty-div"></div>
                <div>
                    <input class="btn setting_btn" id="collectPCIData" type="submit" value="Add Card">
                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                </div>
                <div>
                    <pre id="collectResponse"></pre>
                </div>
                {{-- card stop --}}
            </div>
        </div>
    </div>
</div>
